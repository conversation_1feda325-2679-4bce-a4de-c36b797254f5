#system config
vi /etc/hosts
+127.0.0.1       localhost       file.test


#Image Server Config:
0. mv cfglocal -> cfgimage
1. pull imgd1
2. vhost.yml
      fileserver:
        match:
          hostname: file\.test
        setting:
          path_root: ~/YOUR_PATH/realmaster-appweb/src/webroot/public
          path_upload: /tmp
          site: ImageFileServer
3. base.yml
  host: 127.0.0.1
  +  port:  8091
4. config.coffee
  dbs:false #(remove all db settings)
  apps: ['<%this.srcPath%>/appImgServer'] #'<%this.srcPath%>/apps_common',
  app_config:
    + publicImgFilePath: '<%this.srcPath%>/webroot/public/img'
    + publicImgErrorFile: '<%this.srcPath%>/s3uploadError.log'
    + imgServerDlAddr: 'http://file.test:8091/img'
    + imgServerUlAddr: 'http://file.test:8091'
    + moveFile: copy/rename

<!-- NOTE: orig purpose of useBase, but have to maintain multiple servers, depreciated -->
<!-- # wepage, http://f.realmaster.com/G/T/A.png -> s3 -->
<!-- # wwwbase2, http://fs.realmaster.com/G/T/A.png -> img server -->

# @11 App Server config:
1. config.coffee
  app_config:
      + imgServerUlAddr: https://fu_t.realmaster.cn #'http://file.test:8091'
      user_file:
        + wwwbase: fs_t.realmaster.cn#'<%this.s3.bucket%>'
        folder: 'G'
        + protocol: 'http'
        + disableUpload: false
        + #port: 8091
