# GeoCoder

## 数据表
   1. geo_cache，字段定义: `docs/Collection_definitions/geo_cache.md`
   2. geoCacheOrig，存所有结果
## model/geoCoder.coffee 
提供 GeoCoder API，根据输入参数，查到缓存的 coding，或者调用 engine 进行 coding
   1. geocoder 的结果 q<100 的，半年后会重查一次
   2. 输入的数据如果含有 lat、lng，并且大于100的，存入db
   3. 输入的数据如果含有 lat、lng，q<=100，但是房源非 active，非半年内 sold 的，返回输入值，不做 real coding，不存db
   4. 调用引擎查找
      1. import 的顺序是[bingGeocode,hereGeocode,googleGeocode,mapquestGeocode]
      2. 1.5/geocoding （用于autocomplete） [hereGeocode,mapboxGeocode]
      3. 引擎一直做，得到 q = 100，zip = 6位的结果，或者从所有的结果中得到 q 最高的返回，并 copy 最好的一个 zip
   5. 对于 condo 房源，当地址可能有错误时会尝试修正，可参考此文档 fixAddrByFindSimilarAddrInGeoCacheResults 部分

## GeoCoder Service 设置
依照 cfglocal 的设置，可以决定採用本地的 GeoCoderLocal 或是使用 GeoCoderRemote 呼叫远程的 GeoCoding，这个设置是用来在多台服务器的情况可以只有一台机器负责 Geocoding，其他机器远程就呼叫这台机器的 GeoCoder Service。

在 `cfglocal/tpls/config.coffee` 设置相关 config  

在最外层的 `app` 字段可以设置要不要加载相关 API
```
# 添加 '<%this.srcPath%>/microService' 到 apps 以加载 microService 目录
apps: ['<%this.srcPath%>/microService']
```
在 `app_config` 底下设置 GeoCoder
```
geoCoderService:
  # API 验证用，每个服务器上的 token 需要设置成相同的值
  token: 'aUIh0DMLQY'
  # 如果要使用远程的 GeoCoding，须设置服务器的 URL
  url: 'http://app.test:8080/geocodingService'
  # 是否使用远程 GeoCoder Service
  useRemoteService: false
  # 作为 GeoCoder Service 服务器时，可设置 IP 白名单，只有名单内的 IP 才可以呼叫本服务器的 GeoCoder API，值为 empty array 或 key 不存在时忽略此限制
  allowedIPs: ['127.0.0.1']
```

## 前端API
1. 1.5/geocoding 
  前端api，用于autocomplete， 结果存到geo_cache， geoCacheOrig

2. 1.5/geocoding/getGeocodingByEngine
  在more->test下可以输入地址，输入使用的geocoding的type，看到返回的结果。

## GeoCoder Service API
供远程呼叫用

1.  POST geocodingService/edit  
    Upsert GeoCache  
    Body:
    ```
    {
      ptype?: string,
      ptype2?: string[],
      geoCodingResult: Object, # Please refer to docs/Collection_definitions/geo_cache.md
      uaddrBeforeFormat?: string,
      uaddrAfterFormat: string,
      forceUpdate: boolean,
    }
    ```

2.  POST geocodingService/geocoding  
    输入地址做 GeoCoding  
    Body:
    ```
    {
      zip?: string,
      cnty: string,
      prov: string,
      city: string,
      addr?: string, # use addr only or use st + st_num
      st?: string,
      st_num?: string,
      lat?: number,
      lng?: number,
      ptype?: string,
      ptype2?: string[],
      engineType?: string,
      useCache?: boolean, # default true
      saveResult?: boolean, # default true
    }
    ```

3.  GET geocodingService/reverseGeocoding  
    输入经纬度取得地址  
    Query string params:
    ```
    {
      lat: number,
      lng: number,
      engineType?: 'publicApi' | 'mapbox' | 'bing' | 'here' | 'google'| 'mapquest'
    }
    ```

### fixAddrByFindSimilarAddrInGeoCacheResults
当经纪输入的地址有可能有错误时，会自动修正 (只有 condo 房源使用此功能)
1. 如果 GeoCache 没找到 addr
2. 进行 GeoCoding，第三方地图服务在地址有少许错误时依然可以找出正确地址的经纬度
3. 使用这个经纬度寻找附近 40 米内已经有的 GeoCache
4. 判断 GeoCache 中的 addr 与房源 addr 的相似度，如果相似度够高，就取代 addr  
  addr 相似的定义
    - st 90% 的内容相同 '111 Elizabeth St' 与 '111 Elizabeths St' 视为相符
    - st 可分为两个部分，少了一段，但另外一段完全相同，例如 '123 Spring Garden Ave' 与 '123 Spring Ave' 视为相符
    - st_sfx 不影响判断，'111 Elizabeth St W' 与 '111 Elizabeth St' 视为相符
    - st_num 需要完全相同，否则视为不同房源

## google geocoding的结果参考文档
https://developers.google.com/maps/documentation/geocoding/overview
