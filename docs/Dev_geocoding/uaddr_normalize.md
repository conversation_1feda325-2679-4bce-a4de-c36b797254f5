# uaddr归一
  - 需要对geo_cache.aUaddr的旧数据进行处理，仅保留_id内容，添加唯一索引(只有第一次需要进行该项操作)
  
  - 对uaddr做归一处理，会统计出geo_cache中公寓的地址
  - 将相距20m以内(经纬度保留四位小数)的地址进行分组
  - 对分好组的地址重新计算st_num,unit,st_dir,st_sfx,st
  - 对st_num相同的地址 计算错误(st_dir,st_sfx不同,st相似度不足90%按错误计算)
  - 以错误最少使用房源最多的uaddr作为归一地址，其余质量大于等于100的地址进行归一处理,从geo_cache中删除，加入归一后地址的aUaddr中
  - 在查找geo_cache时对aUaddr进行检索
  - 将properties中被归一的uaddr进行替换

  TODO: uaddr归一后，note需要进行合并


## 数据表
### tmp.geocache_propCnt
-----------------------------------------------------------------
| field  |  type    |  desc                                     |
|--------|----------|-------------------------------------------|
| _id    |  string  | geo_cache._id(uaddr)                      |
| city   |  string  | city                                      |
| addr   |  string  | address                                   |
| prov   |  string  | province abbreviation                     |
| lng    |  number  | longitude                                 |
| lat    |  number  | latitude                                  |
| cmty   |  string  | community                                 |
| cnty   |  string  | country                                   |
| st_num |  string  | street number                             |
| st     |  string  | street                                    |
| st_sfx |  string  | street suffix                             |
| q      |  number  | quality                                   |
| count  |  number  | Number of properties using current uaddr  |
-----------------------------------------------------------------

### tmp.nearby_uaddr
--------------------------------------------------------------------------
| field       |  type    |  desc                                         |
|-------------|----------|-----------------------------------------------|
| _id         |  string  |  保留四位小数后的经纬度字符串 "#{lat},#{lng}"      |
| propCnt     |  number  |  当前经纬度下的房源数量                           |
| uaddr       |  array   |  保留四位小数后经纬度相同的geocache_propCnt记录    |
| uaddrCnt    |  number  |  当前经纬度下的uaddr数量                         |
| newUaddr    |  array   |  对uaddr归一后的结果                            |
| newCount    |  number  |  对uaddr归一后保留的uaddr数量                    |
| st_num_obj  |  object  |  对uaddr按st_num分组之后的结果                   |
-------------------------------------------------------------------------

- newUaddr.item
  - uaddr
  - aUaddr  : 存放需要删除的uaddr字符串数组
  - replacedUaddr  : 存放需要删除的uaddr的原数据(geocache_propCnt记录)

### tmp.normalize_uaddr_log
---------------------------------------------------
| field         |  type    |  desc                |
|---------------|----------|----------------------|
| _id           |  string  | geo_cache._id(uaddr) |
| aUaddr        |  array   | 需要删除的aUaddr       |
| replacedUaddr |  array   | 需要删除的aUaddr原数据  |
| lowQuality    |  array   | 低q不需要删除的原数据    |
---------------------------------------------------

- replacedUaddr.item
  - uaddr
  - lat     : latitude
  - lng     : longitude
  - q       : quality
  - st_sfx  : street suffix

### tmp.nearby_uaddr_err
-------------------------------------
| field  |  type      |  desc       |
|--------|------------|-------------|
| _id    |  ObjectId  |             |
| addr   |  string    | address     |
| uaddr  |  string    |             |
| err    |  string    | error info  |
-------------------------------------