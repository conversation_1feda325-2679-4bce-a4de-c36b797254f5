# School Census Data

School census data includes demographic information for the school's boundary area:

- Summary
  - household income median
  - household income average
- Household income
- Education
- Language
- Ethnic  

## Data Structure
```json
{
  "_id": "schoolId",
  "censusIds": ["censusId1", "censusId2"], 
  "census": {
    "0260KA": {
      "0260KA" : [
        "14175",
        "0",
        "0",
        "21"
      ],
      "0261KB" : [
        "575",
        "0",
        "0",
        null,
        "0260KA.0261KB"
      ],
      "0262KC" : [
        "360",
        "0",
        "0",
        null,
        "0260KA.0262KC"
      ],
      //...
    },
    "1998CYW": {
      "1998CYW" : [
        "21720",
        "11830",
        "9895",
        "165"
      ],
      "1999CYX" : [
        "2230",
        "1190",
        "1015",
        null,
        "1998CYW.1999CYX"
      ],
      "2000CYY" : [
        "4405",
        "2595",
        "1820",
        "167",
        "1998CYW.2000CYY"
      ], 
      //...
    },
    "0735BCH": {
      "0735BCH" : [
        "23825",
        "13035",
        "10790",
        "41"
      ],
      "0736BCI" : [
        "22200",
        "12220",
        "9990",
        null,
        "0735BCH.0736BCI"
      ],
      "0737BCJ" : [
        "17780",
        "10075",
        "7725",
        null,
        "0735BCH.0736BCI.0737BCJ"
      ],
      //...
    },
    "1683CMT": {
      "1683CMT" : [
        "23285",
        "12630",
        "10655",
        "117"
      ],
      "1684CMU" : [
        "13500",
        "6960",
        "6545",
        "118",
        "1683CMT.1684CMU"
      ],
      //...
    }
  }
}
```

key definition see src/transfiles/census2021--en.txt

## Census Data Import Process

1. Calculate center points for census areas:
```bash
./start.sh -t batch -n addCenter -cmd "lib/batchBase.coffee batch/census2021/addCenter.coffee"
```

2. Calculate school census statistics:
```bash
./start.sh -t batch -n school_census -cmd "lib/batchBase.coffee batch/school_census/school_census.coffee"
```

3. Import to production:
```bash
mongorestore --host XXX --ssl -u XXX -p XXX --db listing --authenticationDatabase admin -c school_census --gzip school_census.bson.gz --drop --tlsInsecure
```

## Data Display

The school census data is displayed in the school boundary detail page.
