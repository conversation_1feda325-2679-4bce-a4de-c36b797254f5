# prop field structure

```
# "Utilities" : [ 
#     {
#         "t" : "Water", type?
#         "m" : "Available" remark?
#     }
# ],

# "rms" : [ 
#   {
#       "t" : "3pc Bathroom", type?
#       "l" : "Bsmt" level?
#   },
#   {
#       "t" : "3pc Bathroom",
#       "l" : "Ground"
#   }]

# "Building" : {
#     "BathroomTotal" : 2,
#     "BedroomsTotal" : 3,
#     "BedroomsAboveGround" : 3,
#     "ArchitecturalStyle" : "Bungalow",
#     "BasementDevelopment" : "Finished",
#     "BasementType" : "Full (Finished)",
#     "ConstructionStyleAttachment" : "Detached",
#     "CoolingType" : "Central air conditioning",
#     "ExteriorFinish" : "Brick",
#     "FireplacePresent" : "True",
#     "FireplaceTotal" : "2",
#     "FireplaceType" : "Woodstove",
#     "FoundationType" : "Poured Concrete",
#     "HeatingFuel" : "Natural gas",
#     "HeatingType" : "Forced air",
#     "StoriesTotal" : "1",
#     "Type" : "House",
#     "UtilityWater" : "Municipal water"
# },
# "BuildingType" : "House",
# "EquipmentType" : "Water Heater",
# "Features" : "Paved driveway",
# "Land" : {
#     "SizeTotalText" : "under 1/2 acre",
#     "SizeFrontage" : "80 ft",
#     "Acreage" : "false",
#     "FenceType" : "Fence",
#     "Sewer" : "Municipal sewage system",
#     "SizeDepth" : "127 ft",
#     "SoilType" : "Loam"
# },
```