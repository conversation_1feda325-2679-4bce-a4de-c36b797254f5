# Property Image Processing Logic

## ES Storage
1. ES only stores `thumbUrl`, `propList` uses ES filtering to get required properties
2. Full property details with all images are fetched from MongoDB

## Image Fields
- `phoUrls`: Binary image list storage
- `thumbUrl`: First image thumbnail storage
- `pic.l`: Array of processed image URLs
TODO: Add new field after local image storage

## Function: thumbUrlReplace
### Purpose
- Replaces `{ml_base}` placeholder in ES stored URLs with actual base URL
- Used in frontend to display correct image URLs

### Input Format
```
{ml_base}/cda/828.1525979189/340/19414340_1.jpg
```

### Output Format
```
https://img.realmaster.com/cda/828.1525979189/340/19414340_1.jpg
```

### Usage
- Called in `propList.coffee`
- Implemented in `resources.coffee` line 164 via `libP.thumbUrlReplace`

## Function: listingPicUrls
### Purpose
- Generates actual image URLs for property listings
- Handles multiple image sources and formats

### Output Format
```javascript
[
  "https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=123&index=1",
  "https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=123&index=2"
]
```

### URL Generation Logic
1. **RESO Images** (New)
   - Uses `phoUrls`
   - Generated via `genListingPicUrlsByPhoUrls`

2. **OTW/CLG Listings** (Historical)
   - Uses `phoIDs`
   - Generated via `genListingPicUrlsByPhoIDs`

3. **TREB Online Images**
   - Uses `photonumbers`
   - Direct URL construction

4. **Local Storage**
   - Base format: `{ml_base}/cda/828.1525979189/340/19414340_1.jpg`
   - Source-specific handling:
     - DDF: Uses `ddfID`
     - BRE: Uses `picUrl`
     - RHB/EDM/CAR/OTW: Uses `phoUrls`
     - TREB: Uses TREB format

## Test Cases
1. TREB RESO: `TRBC10876214`
2. CREA RESO (phoUrl binary): `DDF27319732`
3. RM: `id:RM1-27641`
4. Local TREB: `TRBC4023996`
5. TREB Online: `TRBC3607820`
6. Local Legacy: `CLGA2156861`
7. Local DDF: `DDF27241671`