#Property filter/showing control

## List search use unified API:

- Properties.getList(options,cb(err, result-object)) or async return result-object
  # list result
  - listing-list-result-object: (also used by libapp.filterListingList as both input and output)
    - list: could be empty array, never null
    - code: when required permission is missing or other program check error, i.e. bad parameters. Do not including system error, which will be returned in throw error or call back error.
    - total: when result counting was requested
    - hasMore: when result size is limited, return true/false
    - dis(claimers):{} key:DDF/TRB/BRE/RM/PRECON, contents:{terms:[string], icons:[urls]}
      - when in list result, each listing only contain the key, not the content. The contents are in the outside object.
  # list options
  - options: \* required
    - query\*:{}
    - perm(issions):{} uid, roles
    - from:string web/app
    - dm:string domain name   collection选择/query权限(eg:src限制)
    - limit:number result limit, default use global CONSTANT(50?)
    - skip:number start from 0
    - sort:{}
    - needCount
    - proj(ection):{}
    - trans:fn/string translation function(str,prefix):string or language name. no translation, if not provided.
    - join:? not sure if needed
  - logic:
    - check parameters, and return error code if needed
    - pass through pre-query filter to add more limit on query if needed
    - recalculate limit to include 1 more row, to know if has more（limit+1 根据返回数量判断hasMore）
    - restructure query based on db configuration (mongo or es)
    - query db
    - if no result, return
    - pass through post-query filter to modify/remove/add result list. each record will pass through the detail modify logic.

## Get listing detail use unified API:

- Properties.filterDetail(listing, options, cb(err, modified-listing)) or async return modified listing
- Properties.getDetail(options, cb(err, modified-result)) or async return modified listing
  # Detail options
  - options: \* required
    - \_id\*: required for getDetail
    - perm(issions):{} uid, roles
    - from:string web/app
    - dm:string domain name
    - proj(ection):{}
    - trans:fn/string translation function(str,prefix):string or language name. no translation, if not provided.
    - disKeyOnly: true/false, only assign disclaimer key, if true. Default false. 是否需要content
  # Detail result
  - result: add/remove fields when needed:
    - needLogin: true
    - detail:detail info
    - dis(claimers):{}
      - key:DDF/TRB/BRE/RM/PRECON
      - contents: {terms:[string], icons:[urls]} or 1 when disKeyOnly is true.
      - {DDF:{terms:[],icons:[{}]}}

## listing filter logic API:
TODO: 用户收藏
- lib app.filterListingList(listingListResultObject, options)
  - filter the input list, add necessory dis(claimers)/needLogin to each listing, perform filters
  - return listingListResultObject
- libapp.filterListingDetail(listing,options)
  - filter the input listing, add necessory dis(claimers)/needLogin, perform filters
  - return listing
- options:
  - perm(issions):{} uid, roles
  - from:string web/app
  - dm:string domain name
  - trans:fn/string translation function(str,prefix):string or language name. no translation, if not provided.
  - proj(ection):{} if provided, will only return these fields
  - disKeyOnly: true/false. Disclaimer key only, if true. Default false.
- procedure:
  - init disclaimers, outputList
  - loop through all listings, for each listing
    - get source key(s) from listing input
    - init needLogin{},fieldsToCopy{},dis{} objects
    - (\*1)perform filter actions based on source key(s)
    - if fieldsToCopy is not empty, copy step: copy all fields based on fieldsToCopy, from input object to output object.
    - if fieldsToCopy is empty, set output.needLogin = true, otherwise output.needLogin = needLogin or delete needLogin when needLogin is empty.
    - update output.dis with dis
    - add output to outputList
  - construct listingListResultObject with outputList and input and translation
  - return listingListResultObject
- filter actions:
  - use data structure and modular function to check and perform filter action plan.
  - The action plans are predefined based on listing sources as the following (TRB:VOW|IDX|DTA|M;BRE:VOW|IDX|M;DDF;RM;PRECON), and all the listing sources have a respective action plan. Logic shall get the listing source from the listing as a string key, and perform the respective action plan. The string key can be something like: TRB:IDX.
  - Sources are only TRB,BRE,DDF,RM,PRECON, without the sub-type in source.
  - Every action plan has a defined priority, which is the same as the order listed above.
  - an output listing object shall be generated from input listing object, and the input listing object shall NOT be modified.
  - (\*1)filter actions: will be performed as the order below
    - returnNeedLogin: set needLogin:{SOURCE:true} to output object and stop further actions if has no user id
    - addDisclaimer:
      - add to output object {SOURCE:{terms:[],icons:[]}}, or {SOURCE:1} for list of listings or if disKeyOnly is true
      - if in loop, update and merge listDisclaimers object with perspective source, terms, icons
    - custom filter functions based on from/dm/user-roles:
      - step 1. if the fieldsToCopy variable does not exist yet for the listing, use Object.keys(input object) to get all fields as fieldsToCopy.
      - step 2. filterOutFields: filter out all forbidden fields from fieldsToCopy. If no filterOutFields, no-op.
      - step 3. filterInFields: filter out all fields not in the included fields list from fieldsToCopy. If no filterInFields, no-op.
      - we can use 'generator' pattern, by giving a function generator the required from/dm/user-roles and filterOut/InFields, we can generalize the filter function.
      filterOutFields与filterInFields 应该只存在一个
  - If a listing match multiple sources, the source key shall be a result of following logic: a list of string keys, sorted by priority.
  - For multiple sources, each action plan shall be run in priority order as the string keys array
- translation:
  - options:
    1. untranslated keep the field names, translated use \_i18n suffix
    2. untranslated use \_en suffix, translated use the field names
    3. untranslated keep the field names, translated use \_trans{} to hold whole object
    4. untranslated use \_orig{} for original object, translated use the field names
  - choose No.4, reasons:
    - minimum changes required for the frontend showing and existing code, thus minimum the chance of bugs
    - all solutions have no fundamental differences
  - how to implement:
    - when translating, keeping the origin object as a new field \_orig{}, put all translated and untranslatable fields to the outer object
    - when showing, just use the outer object, no need to use the \_orig{}
    - when creating link, sending parameters, the \_orig{} fields have to be used. i.e. prov, city, ...
    - when special prefix of translation is needed, the fields in \_orig{} may be used
    - to save network traffic, number/true-false/ fields can be removed from \_orig{}

## fields for translation

(TODO)

## data flow

- DB =()=> DB Model =(I/F)> Business logic =(INCLUDE().)=> Controller(Glue) => VIEW

