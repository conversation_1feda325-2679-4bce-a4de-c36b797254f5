增加高级检索的tag，增加列表和savedsearch支持
定义需要新增加的tag，包括显示文字(desc)，对应的key(k)和value(k)

前端增加展示以及搜索时对字段和对应值的保留
apps/80_sites/AppRM/prop/mapSearchProp.coffee
propFeatureTags增加对应格式的tag内容(高级检索页面展示)
[desc,k,v]

libapp/common.coffee
PROP_LIST_SEARCH_MODES增加对应格式的tag内容
desc: {k: k, v: v}

coffee4client/components/frac/MapAdvFilterModal.vue
data.propTmpFilter和resetFilter增加默认值 k:''

coffee4client/components/mapSearch_mixins.js
由于房源状态不同，可以看到的tag不同，salePtypeTags在对应需要显示的状态中增加desc，才能在列表页增加tag
propTmpFilter中增加 k:''
tagFields增加 k(保留k的值，以防忽略字段)
parseSerializedFilter的strArray增加 k(保留k的值，以防忽略字段)

coffee4client/components/mapSearchLogic_mixins.js
getPropDefaultFilter增加默认值 k:''



后端搜索需要增加对对应字段的支持
libapp/propElasticSearch.coffee和libapp/propSearch.coffee
创建query时增加对应筛选条件

libapp/properties.coffee
propPassSearchCondition增加判断，房源在什么情况下为满足tag的要求

libapp/propSearchReadable.coffee
getQueryReadable增加接受参数k，增加有k的情况下readable需要添加的值(e.g. readable.push({k:k, v:v, vv:desc}) if k)

libapp/savedSearch.coffee
saved search增加对应k用于保存该字段
renderSavedSearchTxt的vvFields增加 k