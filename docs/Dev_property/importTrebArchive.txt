1、dump geo_cache,
 mongodump --db d4listing --collection geo_cache -u d4 -p d4 --gzip -q "{src:'osm'}" --out geo

2. cpoy  geo_cache, copy csv files
scp -P 27 -d -r u5usr@*************:~/csv ~
scp -P 27 -d -r u5usr@*************:~/geo/d4listing/geo_cache.bson.gz ~
scp -P 27 -d -r u5usr@*************:~/geo/d4listing/geo_cache.metadata.json.gz ~

3、restore geo_cache
mongorestore -d d2listing -u d2 -p d2 -c geo_cache ./geo_cache.bson.gz --gzip

4、checkout julie branch

5 ./start.sh batch/import/trebCSV.coffee ~/csv
6 ./start.sh import/treb_import_test.coffee



update

1. remove archived
db.getCollection('mls_treb_master_records').remove({archive:true})
WriteResult({ "nRemoved" : 3042047 })
> db.getCollection('properties').remove({archive:true})

重新跑脚本
2 ./start.sh batch/import/trebCSV.coffee ~/csv
3 ./start.sh import/treb_import_test.coffee

或者
restore files in d4.
~/props/properties
~/props/mls_treb_master_records
