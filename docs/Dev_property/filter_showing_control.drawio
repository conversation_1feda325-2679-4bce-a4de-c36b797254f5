<mxfile host="65bd71144e">
    <diagram id="hjyPD2ZW819C4gTispAm" name="第 1 页">
        <mxGraphModel dx="1120" dy="552" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="10" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="2" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="getPropList" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="247" y="20" width="110" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="7" target="15" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="96" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="7" target="25" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="check params" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="227" y="100" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="134" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="12" target="95" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="build mongo query object" style="whiteSpace=wrap;html=1;rounded=0;imageWidth=24;imageHeight=24;" parent="1" vertex="1">
                    <mxGeometry x="237" y="283" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="295" y="158" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="return error" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="77" y="210" width="110" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="147" y="110" width="50" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="225" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="19" target="224" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="query in mongodb" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="237" y="477" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="126" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="25" target="29" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="133" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="25" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="is mongo" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="232" y="198" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="29" target="30" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="build es query object" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="417" y="397" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="227" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="30" target="224" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="query in es(自带count)" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="417" y="477" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="310" y="248" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="407" y="198" width="50" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="508" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#fdfcfc;" parent="1" source="50" target="437" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="init result = {list:[],dis:{}}" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="236.5" y="653" width="315" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="78" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="62" target="63" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="getPropDetail" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="1871" width="110" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="65" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="63" target="64" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1796" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="291" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="63" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1926" y="178" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="check params" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="1851" y="80" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="return error" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="1680" y="190" width="110" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="66" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1692" y="140" width="50" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="414" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="69" target="412" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="69" value="query detail in mongodb" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1861" y="185" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1921" y="140" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="135" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="95" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="136" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="95" target="109" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="95" value="needCount" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="240.5" y="375" width="123" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="104" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="171" y="367" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="294" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="109" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="109" value="count query in mongodb" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="41" y="477" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="112" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="305" y="432" width="50" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="231" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="224" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="224" value="获得房源列表 props" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="237" y="578" width="315" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="405" value="return result" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="312.75" y="810" width="163.5" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="423" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="412" target="417" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="542" style="edgeStyle=none;shape=arrow;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="412">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1610" y="292" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="412" value="将结果作为参数,调用 filterListing,获得返回值prop" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1769" y="272" width="315" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="424" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="417" target="418" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="428" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="417" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1939" y="502" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="2109" y="452"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="417" value="check disKeyOnly is true？" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="1824.5" y="349" width="204" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="425" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="418" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1926.5" y="522" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="418" value="set dis&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;claimer&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1816" y="441" width="220" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="421" value="return {prop，dis&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;claimer&lt;/span&gt;}" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="1787.75" y="530" width="276.5" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="430" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="2048" y="342" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="432" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1934.5" y="404" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="509" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#fdfcfc;" parent="1" source="437" target="405" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="510" style="edgeStyle=elbowEdgeStyle;shape=flexArrow;rounded=0;html=1;fontColor=#fdfcfc;" parent="1" source="437" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="650" y="750" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="437" value="循环props 对每个房源作处理，&lt;br&gt;得到返回结果prop并push到result.list" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="236.5" y="730" width="315" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="520" value="存在3个常量：&lt;br&gt;1.SRC_FIELDS_COPY&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;{TRB:[],DDF:[]}&lt;br&gt;2.OUT_FIELDS&lt;br&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;3.IN_FIELDS&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;p style=&quot;margin: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;;&quot; class=&quot;p1&quot;&gt;{&lt;/p&gt;&lt;p style=&quot;margin: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;;&quot; class=&quot;p1&quot;&gt;&lt;span class=&quot;Apple-converted-space&quot;&gt;&amp;nbsp; &lt;/span&gt;from:{web:[],app:[]},&lt;/p&gt;&lt;p style=&quot;margin: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;;&quot; class=&quot;p1&quot;&gt;&lt;span class=&quot;Apple-converted-space&quot;&gt;&amp;nbsp; &lt;/span&gt;dm:{aws:[]},&lt;/p&gt;&lt;p style=&quot;margin: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;;&quot; class=&quot;p1&quot;&gt;&lt;span class=&quot;Apple-converted-space&quot;&gt;&amp;nbsp; &lt;/span&gt;role:{admin:[],dev:[]}&lt;/p&gt;&lt;p style=&quot;margin: 0px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;;&quot; class=&quot;p1&quot;&gt;}&lt;/p&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); line-height: 18px;&quot;&gt;&lt;div style=&quot;&quot;&gt;根据from/dm/roles outFields取并集，inFields取交集&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=top;whiteSpace=wrap;rounded=0;fontColor=#fdfcfc;" parent="1" vertex="1">
                    <mxGeometry x="247" y="950" width="290" height="170" as="geometry"/>
                </mxCell>
                <mxCell id="541" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="660" y="20" width="936" height="892" as="geometry"/>
                </mxCell>
                <mxCell id="540" value="" style="whiteSpace=wrap;html=1;" vertex="1" parent="541">
                    <mxGeometry width="936" height="892" as="geometry"/>
                </mxCell>
                <mxCell id="330" value="init output&lt;br&gt;(copy from prop)" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="541" vertex="1">
                    <mxGeometry x="77.75" y="158" width="133" height="42" as="geometry"/>
                </mxCell>
                <mxCell id="333" value="check dm/from/user-roles&lt;br&gt;init fieldsToCopy,outFields,inFields" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="541" vertex="1">
                    <mxGeometry x="36" y="240" width="216.5" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="384" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;" parent="541" source="330" target="333" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="340" value="set needLogin" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="541" vertex="1">
                    <mxGeometry x="77.75" y="414" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="341" value="set output.dis{src:true},&lt;br&gt;设置result.dis的key,并且push terms,icons" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="541" vertex="1">
                    <mxGeometry x="77.75" y="487" width="133" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="357" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="541" source="340" target="341" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="372" value="return output" style="whiteSpace=wrap;html=1;rounded=1;container=0;" parent="541" vertex="1">
                    <mxGeometry x="77.75" y="784" width="133" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="473" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="541" source="445" target="340" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="539" style="edgeStyle=none;shape=arrow;rounded=0;html=1;" edge="1" parent="541" source="445">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="426" y="347.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="445" value="循环output所有fields进行filter判断" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="541" vertex="1">
                    <mxGeometry x="36" y="325" width="216.5" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="471" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;" parent="541" source="333" target="445" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="502" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="541" source="479" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="136" y="761" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="16" y="701"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="479" value="确认是否需要翻译，&lt;br&gt;以及翻译语言?" style="rhombus;whiteSpace=wrap;html=1;rounded=1;" parent="541" vertex="1">
                    <mxGeometry x="42.25" y="601" width="204" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="505" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;fontColor=#fdfcfc;" parent="541" source="341" target="479" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="497" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;" parent="541" source="494" target="372" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="498" style="edgeStyle=orthogonalEdgeStyle;shape=arrow;rounded=0;html=1;" parent="541" source="494" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="256" y="718" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="494" value="翻译" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="541" vertex="1">
                    <mxGeometry x="77.75" y="701" width="133" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="500" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="541" vertex="1">
                    <mxGeometry x="146" y="656" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="503" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="541" vertex="1">
                    <mxGeometry x="16" y="661" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="495" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="541" source="479" target="494" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="533" value="" style="group" vertex="1" connectable="0" parent="541">
                    <mxGeometry x="436" y="5" width="410" height="572" as="geometry"/>
                </mxCell>
                <mxCell id="468" value="" style="whiteSpace=wrap;html=1;container=0;" parent="533" vertex="1">
                    <mxGeometry width="410" height="572" as="geometry"/>
                </mxCell>
                <mxCell id="446" value="check&amp;nbsp;field in fieldsToCopy？" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="533" vertex="1">
                    <mxGeometry x="167" y="120" width="150" height="76.5" as="geometry"/>
                </mxCell>
                <mxCell id="447" value="check field in outFields？" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="533" vertex="1">
                    <mxGeometry x="167" y="224.5" width="150" height="68" as="geometry"/>
                </mxCell>
                <mxCell id="450" value="" style="edgeStyle=none;html=1;" parent="533" source="446" target="447" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="531" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="533" source="448">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="502" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="370" y="432"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="448" value="check inFields is &lt;br&gt;not empty？" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="533" vertex="1">
                    <mxGeometry x="167" y="318.5" width="150" height="68" as="geometry"/>
                </mxCell>
                <mxCell id="451" value="" style="edgeStyle=none;html=1;" parent="533" source="447" target="448" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="449" value="delete field" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="20" y="522" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="453" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="533" source="446" target="449" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="455" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="533" source="447" target="449" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="458" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="117.25" y="133.5" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="460" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="117.25" y="226.5" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="462" value="save field" style="whiteSpace=wrap;html=1;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="175.5" y="522" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="465" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="247" y="194.5" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="466" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="247" y="386.5" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="467" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="533" vertex="1">
                    <mxGeometry x="247" y="288.5" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="526" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="533" source="521" target="446">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="529" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="533" source="521" target="447">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="370" y="170"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="521" value="check&amp;nbsp;fieldsToCopy &lt;br&gt;is not empty？" style="rhombus;whiteSpace=wrap;html=1;container=0;" vertex="1" parent="533">
                    <mxGeometry x="167" y="10" width="150" height="76.5" as="geometry"/>
                </mxCell>
                <mxCell id="524" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="533" source="522" target="462">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="525" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" edge="1" parent="533" source="522" target="449">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="522" value="check field in inFields？" style="rhombus;whiteSpace=wrap;html=1;container=0;" vertex="1" parent="533">
                    <mxGeometry x="167" y="422" width="150" height="68" as="geometry"/>
                </mxCell>
                <mxCell id="523" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="533" source="448" target="522">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="527" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" vertex="1" parent="533">
                    <mxGeometry x="247" y="86.5" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="530" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" vertex="1" parent="533">
                    <mxGeometry x="340" y="120" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="532" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" vertex="1" parent="533">
                    <mxGeometry x="330" y="392" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="538" value="" style="group" vertex="1" connectable="0" parent="541">
                    <mxGeometry x="266" y="622" width="650" height="250" as="geometry"/>
                </mxCell>
                <mxCell id="536" value="" style="whiteSpace=wrap;html=1;" vertex="1" parent="538">
                    <mxGeometry width="650" height="250" as="geometry"/>
                </mxCell>
                <mxCell id="303" value="init output {_orig:{}}" style="whiteSpace=wrap;html=1;rounded=0;" parent="538" vertex="1">
                    <mxGeometry x="20" y="29" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="304" value="return output" style="whiteSpace=wrap;html=1;rounded=1;" parent="538" vertex="1">
                    <mxGeometry x="26.5" y="189" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="489" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="538" source="485" target="304" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="491" style="edgeStyle=orthogonalEdgeStyle;shape=arrow;rounded=0;html=1;" parent="538" source="485" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="119" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="485" value="循环input所有fields&lt;br&gt;进行翻译" style="whiteSpace=wrap;html=1;rounded=0;" parent="538" vertex="1">
                    <mxGeometry x="20" y="99" width="133" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="488" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="538" source="303" target="485" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="537" value="" style="group" vertex="1" connectable="0" parent="538">
                    <mxGeometry x="250" y="14" width="380" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="486" value="" style="whiteSpace=wrap;html=1;" parent="537" vertex="1">
                    <mxGeometry width="380" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="300" value="当前field是否需要翻译？" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="537" vertex="1">
                    <mxGeometry x="50" y="10" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="301" value="翻译当前field的值，放入output中" style="whiteSpace=wrap;html=1;rounded=0;" parent="537" vertex="1">
                    <mxGeometry x="58.5" y="170" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="312" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="537" source="302" target="301" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="302" value="在output._orig中copy原始key-value" style="whiteSpace=wrap;html=1;rounded=0;" parent="537" vertex="1">
                    <mxGeometry x="58.5" y="103" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="311" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="537" source="300" target="302" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="320" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="537" vertex="1">
                    <mxGeometry x="128.5" y="70" width="36" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="512" value="将当前field与value放入output" style="whiteSpace=wrap;html=1;rounded=0;" parent="537" vertex="1">
                    <mxGeometry x="229.5" y="170" width="133" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="513" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#fdfcfc;" parent="537" source="300" target="512" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="514" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="537" vertex="1">
                    <mxGeometry x="260" y="80" width="36" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>