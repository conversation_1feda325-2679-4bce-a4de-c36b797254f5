# properties ai table
refer: autocomplete.rest

<!-- TODO: add api link for similar prop/fav/evaluation -->

| api name                  | api link                                                              | api desc                                                                   | parameters                    |
|---------------------------|-----------------------------------------------------------------------|----------------------------------------------------------------------------|-------------------------------|
| prop detail in app        | GET /1.5/prop/detail/inapp?id=TRBE5858868 \POST /1.5/props/detail     | open prop detail from app, from autocomplete/map/list page                 | {_id:,locale:}                |
| prop detail from share    | GET /1.5/prop/detail?ec=406641bd4 \POST null                          | share prop, open in web; no post, prop in window.vars.prop                 | ?id=TRBW1234                  |
| prop list in app          | GET /1.5/search/prop?d=/1.5/index&id=1111 \POST /1.5/search/prop/list | native autocomplete View ALl, show list in web                             | {id,p,share,cache}            |
| prop list when shared     | GET /1.5/prop/detail?ec=d6ade06dc \POST 1.5/search/prop/list          | share prop list open in web;                                               | {id,p,share,cache}            |
| prop list from native/web | POST /1.5/props/search                                                | native/web list request api                                                | {city:,prov:}                 |
| prop map                  | GET /1.5/mapSearch?gps=1 \POST /1.5/props/search                      | native/web map request api                                                 | {bbox:}                       |
| autocomplete in native    | POST std:addrSearch                                                   | autocomple api called in native autocomplete, using standard functions api | {s:'search string',ts:number} |
| autocomplete              | POST /1.5/props/autocompleteGetNext                                   | used in web autocomplete                                                   | {s:'search string',ts:number} |
|                           |                                                                       |                                                                            |                               |