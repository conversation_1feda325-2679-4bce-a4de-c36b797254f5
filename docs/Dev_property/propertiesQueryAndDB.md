Problem: properties query slow and timeout, db too linear-gradient


Solutions:

1. move old records to separate collection, end up 2 collections
2. create new collection only new records, other collection full records



Solution 1:
  scalability: average
    * ...
  pros:
    * simple bash job, moves old record
    * saves space, no duplicate records
  cons:
    * if full db query will have to query both dbs

Solution 2:
  scalability: bad
    * same app db size as sol. 1, much larger full coll
  pros:
    * full db query runs very easy(eg. stats, edm, history,lstdhist, fav, evaluation)
  cons:
    * not only batch removes old record, but also need to change import, write to both
    * waste space, has duplicate records