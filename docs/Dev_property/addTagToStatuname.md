
## What is statUname
1. StatUname holds all the stats data based on recent 3 years properties.  
2. Compute stat data of sale and lease data,metrics on week, month, last month, year and last year.
3. has stat of different lyr
   1. Prov
   2. Region
   3. city
   4. subCity
   5. cmty
   6. zip
   7. uaddr

## Tags to add to statUname:
Only add tag to cmty, zip and uaddr. get tags from the properties of this cmty|zip|uaddr, and do aggregate, get the number of the computed cmty|zip|uaddr
for each cmty, find the props of this cmty as stream
for each prop, accumulate school, census and trnst
   1. school
      1. find best ele|mid|high school of each prop, do Avg score of ele|mid|high
      2. schoolAvg of all three types.
   2. census
      for each census key, compute the following metric
      1. avg: avg of the cmty|zip|uaddr
      2. rnk: rnk in the prov
      3. inrnk: rnk in the keys, only for races. [1325 ... 1337] 
   3. trnst
      1. avg dist of each prop to transit stop
      2. compute by ['goBus','goTrain','sub']
when stream end,compute average of each cmty.




census key dic:
  '1324':
    nm: 'tlMin' #'totalMinority'
    label: 'Total Minority'
  '1325' :
    nm:'sAzn' # southAsian
    label: 'South Asian'
  '1326':
    nm: 'ch' #rmChinese
    label: 'Chinese'
  '1327':
    nm: 'rmBlack'
    label: 'Black'
  '1328':
    nm:'rmFilip' #rmFilipino
    label: 'Filipino'
  '1329':
    nm:'rmLat'  #rmLatin
    label: 'Latin'
  '1330':
    nm:'rmArab'
    label: 'Arab'
  '1331' :
    nm:'rmSeAzn' #SoutheastAsian
    label: 'Southeast Asian'
  '1332' :
    nm:'rmWeAzn'#West Asian
    label: 'West Asian'
  '1333':
    nm: 'rmKore' #rmKorean
    label: 'Korean'
  '1334':
    nm:'rmJapan'
    label: 'Japan'
  '1335': #Visible minority, n.i.e
    nm: 'rmVisMin' #rmVisibleMinor
    label: 'Visible Minor'
  '1336':
    nm: 'rmMultiMin' #'Multiple visible minorities'
    label: 'Multiple visible minorities'
  '1337':
    nm: 'rmNotMin' #Not a visible minority
    label: 'Not a visible minority'
  '0001':
    nm: 'rmPopu' #population
    label: 'Total population'
  '1701':
    nm: 'rmPostSec', #postSecondary
    label: 'Post Secondary'
  
  inComeKey = {
  '0742':
    nm:'rmIncome'
    label: 'Income'
  }
