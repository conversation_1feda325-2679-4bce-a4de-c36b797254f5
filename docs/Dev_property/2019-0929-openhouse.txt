
1. unset mls_treb_master_records 的_i, 以便重新导入。
db.getCollection('mls_treb_master_records').update({Oh_from1:{$regex:/(\d+)\:(\d+)\s([AP])M/i},status:'A'},{$unset:{_i:-1}},{multi:true});

2、properties里的oh是append的，如果需要删除原来错误的数据，执行以下命令，更新properties和prop_parts

db.getCollection('mls_treb_master_records').find({Oh_from1:{$regex:/(\d+)\:(\d+)\s([AP])M/i},status:'A'}).forEach(function(ret){db.getCollection('properties').update({_id:ret._id},{$unset:{oh:-1}});});

3、重新import
./start.sh import/treb_import.coffee

4、同步props_part
./start.sh batch/movePropRecords.coffee init
