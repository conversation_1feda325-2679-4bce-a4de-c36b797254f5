修改cfg下的base.php, 增加extra image的path。

$dir_treb_image2 = Array($dir_treb_base . "mls2",$dir_treb_base . "mls3");
$dir_crea_ddf_image2 = Array($dir_crea_ddf_base . "img2",$dir_treb_base . "img3");



帮助：
mac安装php， mongodb driver note：
BREW INSTAll php 7.1
vi ./bash_profile, 修改当前使用
source ./bash_profile.

sudo ./pecl install mongodb
#./pecl is under /usr/local/Cellar/php@7.1/7.1.30_1/bin

Install composter
composer require mongodb/mongodb
$ echo "extension=mongodb.so" >> `php --ini | grep "Loaded Configuration" | sed -e "s|.*:\s*||"`

command
php --ini
brew services restart php@7.1
composter安装mongodb 在mac ios下可能有问题。ref：
https://medium.com/@rossbulat/macos-install-mongodb-php-driver-and-mongodb-composer-extension-6dc24c9f54a1


如果php升级到7.1， mongo driver升级到1.4，需要修改：
调用mongo前
require 'vendor/autoload.php'; // include Composer's autoloader
mongoClient - > MongoDB\Client
last=》$set: array(last=>)
Update -> upateone , update many,
Monggodate -> new \MongoDB\BSON\UTCDateTime()
aggregatecursor - >aggregate
