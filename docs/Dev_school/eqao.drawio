<mxfile host="65bd71144e">
    <diagram id="LN83iJV1ULtC1tHQcKo6" name="第 1 页">
        <mxGraphModel dx="603" dy="926" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="6" value="" style="edgeStyle=none;html=1;" parent="1" source="2" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="school_board表中获取&lt;br&gt;board name" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="140" y="80" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;" parent="1" source="5" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;根据 name 在(https://www.eqao.com/)&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;中的搜索结果，获取存在的&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;EQAO的学校，得到学校链接&lt;/span&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="120" y="160" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="edgeStyle=none;html=1;" parent="1" source="8" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="保存至school_log" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="120" y="360" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="edgeStyle=none;html=1;" parent="1" source="20" target="22" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="从school_log数据库获取数据" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="80" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="" style="edgeStyle=none;html=1;" parent="1" source="22" target="25" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="&lt;div&gt;根据生成的id，学校名，地址，电话，传真，邮编匹配学校&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="540" y="160" width="240" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="对比schools学校数据，更新学校eqao" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="560" y="240" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="抓取数据" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="105" y="35" width="70" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="&lt;div&gt;打开链接获取学校相关的信息&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="130" y="280" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="更新现有数据" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="530" y="40" width="110" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>