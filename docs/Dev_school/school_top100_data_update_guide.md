# 学校数据更新指南

## 目录

- [学校数据更新指南](#学校数据更新指南)
  - [目录](#目录)
  - [流程图](#流程图)
  - [概述](#概述)
  - [数据处理流程](#数据处理流程)
    - [1. 数据源](#1-数据源)
    - [2. 数据匹配步骤](#2-数据匹配步骤)
    - [3. 数据处理规则](#3-数据处理规则)
      - [3.1 需要处理的数据字段](#31-需要处理的数据字段)
      - [3.2 专业分类统计](#32-专业分类统计)
      - [3.3 大学录取统计](#33-大学录取统计)
- [... 其他大学](#-其他大学)
    - [4. 数据更新操作](#4-数据更新操作)
  - [注意事项](#注意事项)
  - [错误处理](#错误处理)
  - [数据源说明](#数据源说明)
  - [字段命名规则](#字段命名规则)
  - [数据验证规则](#数据验证规则)
  - [更新流程](#更新流程)
    - [1. 数据匹配](#1-数据匹配)
    - [2. 数据转换](#2-数据转换)
    - [3. 数据存储](#3-数据存储)
  - [数据结构](#数据结构)
    - [sch\_rm\_rank表结构](#sch_rm_rank表结构)
  - [更新代码示例](#更新代码示例)
  - [更新注意事项](#更新注意事项)
  - [更新频率](#更新频率)
  - [数据验证](#数据验证)
  - [错误处理](#错误处理-1)
  - [命令行参数说明](#命令行参数说明)
    - [参数格式](#参数格式)
    - [参数说明](#参数说明)
  - [数据验证规则详解](#数据验证规则详解)
    - [1. 基础数据验证](#1-基础数据验证)
    - [2. 百分比数据转换](#2-百分比数据转换)
    - [3. 数值有效性检查](#3-数值有效性检查)
  - [学校名称匹配规则](#学校名称匹配规则)
    - [1. 名称优化处理](#1-名称优化处理)
    - [2. 数据库查询匹配](#2-数据库查询匹配)
  - [数据处理流程详解](#数据处理流程详解)
    - [1. 专业分布数据处理](#1-专业分布数据处理)
    - [2. 大学录取数据处理](#2-大学录取数据处理)
  - [错误处理机制](#错误处理机制)
    - [1. 未匹配学校处理](#1-未匹配学校处理)
    - [2. 处理结果报告](#2-处理结果报告)
  - [数据库更新操作](#数据库更新操作)
    - [1. 数据更新结构](#1-数据更新结构)
    - [2. 更新操作](#2-更新操作)

## 流程图

```mermaid
graph TD
    A[开始] --> B[读取CSV文件]
    B --> C[遍历学校数据]
    C --> D{学校匹配}
    D -->|匹配成功| E[数据处理]
    D -->|匹配失败| F[记录未匹配日志]
    E --> H[数据规范化]
    H --> I[更新数据库]
    I --> J[生成报告]
    J --> K[完成]
    F --> J

    subgraph 数据处理流程
    E --> E1[学术统计]
    E --> E2[专业分类]
    E --> E3[大学录取]
    end

    subgraph 错误处理
    F --> F1[记录错误]
    F --> F2[保存日志]
    end
```

## 概述

本文档说明如何将School-University数据表中的学校统计数据更新到数据库中。

## 数据处理流程

### 1. 数据源
- 源文件: `School-University data - summary major.csv` 和 `summary university.csv`
- 目标数据库表: `sch_rm_rank`
- 关联数据库表: `listing.sch_rm_merged`

### 2. 数据匹配步骤

1. 从CSV文件读取school name列
2. 优化学校名称匹配
   ```coffeescript
   # 使用现有的schoolHelper函数优化学校名称
   optimizeSchoolName = (name) ->
     return null unless name
     
     # 先使用generateSchoolName处理特殊缩写和格式
     name = generateSchoolName(name)
     
     # 再使用delSchoolCommWord移除常见词
     name = delSchoolCommWord(name)
     
     # 移除多余空格并转换为小写
     name = name?.toLowerCase()?.trim()?.replace(/\s+/g, ' ')
     
     return name

   # 查询示例
   optimizedName = optimizeSchoolName(schoolName)
   SchRmMerged.find {
     $or: [
       { name: { $regex: schoolName, $options: 'i' } },
       { name: { $regex: optimizedName, $options: 'i' } }
     ]
   }, {
     projection: { _id: 1, name: 1 }
   }
   ```

3. 记录未匹配的学校
   ```coffeescript
   # 未匹配学校日志记录
   if not matchedSchool
     logger.warn '未找到匹配的学校', {
       original_name: schoolName,
       processed_name: optimizedName,
       source_file: 'School-University data',
       timestamp: new Date()
     }
   ```

### 3. 数据处理规则

#### 3.1 需要处理的数据字段
- total: 学校总人数
- sample_amount: 样本数量
- sample_percentage: 样本百分比
- graduate_year: 毕业年份
- academic_stats: 学术统计
  - 90_plus_count
  - 90_plus_percentage
  - 85_plus_count
  - 85_plus_percentage
  - 80_plus_count
  - 80_plus_percentage

#### 3.2 专业分类统计
- liberal_arts
- fine_arts
- performing_arts
- arts_design
- computer_science
- applied_science
- pure_science
- health_science
- business_school
- other_business

#### 3.3 大学录取统计
- topSchools: [
  { school: 'University of Toronto', pct: 25.5 },
  { school: 'University of British Columbia', pct: 15.2 },
  { school: 'McGill University', pct: 10.8 },
  { school: 'McMaster University', pct: 8.4 },
  { school: 'University of Waterloo', pct: 7.6 },
  { school: 'Western University', pct: 6.9 },
  { school: 'Queen\'s University', pct: 5.7 }
  # ... 其他大学
]

### 4. 数据更新操作

```coffeescript
# 数据有效性检查函数
isValidData = (data) ->
  # 检查是否存在#DIV/0!值
  for key, value of data
    return false if value is '#DIV/0!'
  
  # 检查必要字段是否存在且有效
  requiredFields = [
    'total',
    'sample_amount',
    'sample_percentage',
    'graduate_year'
  ]
  
  for field in requiredFields
    return false unless data[field]? and data[field] > 0
  
  # 检查样本数量是否合理
  return false if data.sample_amount > data.total
  
  # 检查百分比是否合理
  return false if data.sample_percentage > 100
  
  # 检查专业分布数据是否有效
  majorFields = [
    'liberal_arts',
    'fine_arts',
    'performing_arts',
    'arts_design',
    'computer_science',
    'applied_science',
    'pure_science',
    'health_science',
    'business_school',
    'other_business'
  ]
  
  totalMajorPct = 0
  for field in majorFields
    if data[field]?
      totalMajorPct += data[field]
  
  # 专业分布百分比总和应该在合理范围内
  return false if totalMajorPct > 100
  
  return true

# 更新示例
if isValidData(data)
  SchRmRank.updateOne {
    school_id: matchedSchoolId
  }, {
    $set: {
      total_students: data.total,
      sample_size: data.sample_amount,
      sample_percentage: data.sample_percentage,
      graduate_year: data.graduate_year,
      academic_stats: {
        # ... 学术统计数据
      },
      major_distribution: {
        # ... 专业分布数据
      },
      topSchools: [
        # ... 大学录取数据
      ],
      updated_at: new Date()
    }
  }, {
    upsert: true
  }
else
  logger.warn '数据无效，跳过更新', {
    school_id: matchedSchoolId,
    data: data
  }
```

## 注意事项

1. 数据更新前需要验证学校匹配的准确性
2. 保留原始数据以便追踪和验证
3. 记录无法匹配的学校名称，以便后续处理
4. 更新操作建议使用批量更新以提高效率
5. 添加错误处理和日志记录机制

## 错误处理

1. 记录无法匹配的学校
2. 记录数据格式异常的情况
3. 保存更新操作的日志
4. 提供回滚机制

## 数据源说明
- School-University-data-summary-major.csv - 学校专业数据
- School-University-data-summary-university.csv - 学校大学录取数据

## 字段命名规则
1. 使用驼峰命名法（CamelCase）
2. 字段长度超过6个字符时，通过删除元音(aeiou)进行缩写
3. 百分比字段以Pct结尾
4. 示例：
   - university -> unvrsty
   - distribution -> dstrbtn
   - percentage -> prcntg
   - admissions -> dmsns
   - performing -> prfrmng
   - computer -> cmptr

## 数据验证规则
1. 检查数据有效性
   - 跳过包含#DIV/0!的数据行
   - 跳过样本数量为0的数据
   - 跳过总学生数为0的数据
2. 数值范围验证
   - 百分比值应在0-100之间
   - 学生数量应为正整数
   - 年份应为有效年份

## 更新流程

### 1. 数据匹配
- 使用学校名称(nm)作为匹配键
- 在sch_rm_merged表中查找对应的学校记录
- 获取匹配记录的_id

### 2. 数据转换
- 将CSV数据转换为JSON格式
- 主要字段包括:
  - 学校基本信息
  - 专业分布数据
  - 大学录取数据
  - 成绩分布数据

### 3. 数据存储
- 将处理后的数据保存到sch_rm_rank表
- 使用匹配到的_id作为主键
- 保存相关统计数据和百分比信息

## 数据结构

### sch_rm_rank表结构
```json
{
  "_id": "ObjectId", // 来自sch_rm_merged的_id
  "nm": "String", // 学校名称
  "yearmark": "Number", // 数据年份标记
  "total": "Number", // 总学生数
  "smplSz": "Number", // 样本数量
  "smplPct": "Number", // 样本百分比
  "gradYr": "Number", // 毕业年份
  "majors": [ // 专业分布数据
    {
      "major": "String", // 专业名称
      "cnt": "Number", // 学生数量
      "pct": "Number" // 百分比
    }
  ],
  "topSchools": [ // 大学录取数据
    {
      "school": "String", // 大学名称
      "cnt": "Number", // 录取人数
      "pct": "Number" // 录取百分比
    }
  ],
  "abv90": "Number", // 90分以上人数
  "abv90Pct": "Number", // 90分以上百分比
  "abv85": "Number", // 85分以上人数
  "abv85Pct": "Number", // 85分以上百分比
  "abv80": "Number", // 80分以上人数
  "abv80Pct": "Number", // 80分以上百分比
  "_mt": "Date" // 更新时间
}
```

## 更新代码示例

```javascript
/**
 * 检查数据是否有效
 * @param {Object} school - 学校数据对象
 * @returns {boolean} - 数据是否有效
 */
function isValidData(school) {
  // 检查是否包含#DIV/0!
  if (Object.values(school).some(value => value === '#DIV/0!')) {
    console.log(`跳过无效数据: ${school['school name']} - 包含#DIV/0!`);
    return false;
  }
  
  // 检查样本数量
  if (!school['sample amount'] || parseInt(school['sample amount']) === 0) {
    console.log(`跳过无效数据: ${school['school name']} - 样本数量为0`);
    return false;
  }
  
  // 检查总学生数
  if (!school.total || parseInt(school.total) === 0) {
    console.log(`跳过无效数据: ${school['school name']} - 总学生数为0`);
    return false;
  }
  
  return true;
}

/**
 * 处理学校数据并更新到数据库
 * @param {string} majorCsvPath - 专业数据CSV文件路径
 * @param {string} universityCsvPath - 大学录取数据CSV文件路径
 * @param {number} yearmark - 数据年份标记
 */
async function processSchoolData(majorCsvPath, universityCsvPath, yearmark) {
  // 读取CSV文件
  const majorData = await readCsv(majorCsvPath);
  const universityData = await readCsv(universityCsvPath);
  
  // 处理每条记录
  for (const school of majorData) {
    // 检查数据有效性
    if (!isValidData(school)) {
      continue;
    }
    
    // 在sch_rm_merged中查找匹配的学校
    const matchedSchool = await db.sch_rm_merged.findOne({
      nm: school['school name']
    });
    
    if (matchedSchool) {
      // 准备专业分布数据
      const majors = [
        { major: 'Liberal Arts', cnt: parseInt(school['Liberal Arts']), pct: parseFloat(school['Liberal Arts %']) },
        { major: 'Fine Arts', cnt: parseInt(school['Fine Arts']), pct: parseFloat(school['Fine Arts %']) },
        { major: 'Performing Arts', cnt: parseInt(school['Performing arts']), pct: parseFloat(school['Performing arts %']) },
        { major: 'Arts and Design', cnt: parseInt(school['Arts and Design']), pct: parseFloat(school['Arts and Design %']) },
        { major: 'Computer Science', cnt: parseInt(school['Computer Science']), pct: parseFloat(school['Computer Science %']) },
        { major: 'Applied Science and Engineering', cnt: parseInt(school['Applied Science and Engineering']), pct: parseFloat(school['Applied Science and Engineering %']) },
        { major: 'Pure Science', cnt: parseInt(school['Pure Science']), pct: parseFloat(school['Pure Science %']) },
        { major: 'Health Science', cnt: parseInt(school['Health Science']), pct: parseFloat(school['Health Science %']) },
        { major: 'Business', cnt: parseInt(school['Total Business']), pct: parseFloat(school['Total Business %']) }
      ].filter(item => item.cnt > 0 || item.pct > 0); // 只保留有数据的专业

      // 准备大学录取数据
      const topSchools = [
        { school: 'University of Toronto', cnt: parseInt(school['University of Toronto']), pct: parseFloat(school['University of Toronto %']) },
        { school: 'University of British Columbia', cnt: parseInt(school['University of British Columbia']), pct: parseFloat(school['University of British Columbia %']) },
        { school: 'McGill University', cnt: parseInt(school['McGill University']), pct: parseFloat(school['McGill University %']) },
        { school: 'McMaster University', cnt: parseInt(school['McMaster University']), pct: parseFloat(school['McMaster University %']) },
        { school: 'University of Alberta', cnt: parseInt(school['University of Alberta']), pct: parseFloat(school['University of Alberta %']) },
        { school: 'University of Waterloo', cnt: parseInt(school['University of Waterloo']), pct: parseFloat(school['University of Waterloo %']) },
        { school: 'University of Calgary', cnt: parseInt(school['University of Calgary']), pct: parseFloat(school['University of Calgary %']) },
        { school: 'University of Ottawa', cnt: parseInt(school['University of Ottawa']), pct: parseFloat(school['University of Ottawa %']) },
        { school: 'Western University', cnt: parseInt(school['Western University']), pct: parseFloat(school['Western University %']) },
        { school: 'Queens University', cnt: parseInt(school['Queens University']), pct: parseFloat(school['Queens University %']) },
        { school: 'York University', cnt: parseInt(school['York University']), pct: parseFloat(school['York University %']) }
      ].filter(item => item.cnt > 0 || item.pct > 0); // 只保留有数据的学校

      // 准备要保存的数据
      const schoolRankData = {
        _id: matchedSchool._id,
        nm: school['school name'],
        yearmark: yearmark,
        total: parseInt(school.total),
        smplSz: parseInt(school['sample amount']),
        smplPct: parseFloat(school['sample %']),
        gradYr: parseInt(school['graduate year']),
        majors: majors,
        topSchools: topSchools,
        abv90: parseInt(school['90+']),
        abv90Pct: parseFloat(school['90+ %']),
        abv85: parseInt(school['85+']),
        abv85Pct: parseFloat(school['85+ %']),
        abv80: parseInt(school['80+']),
        abv80Pct: parseFloat(school['80+ %']),
        _mt: new Date()
      };
      
      // 保存到sch_rm_rank表
      await db.sch_rm_rank.updateOne(
        { _id: matchedSchool._id, yearmark: yearmark },
        { $set: schoolRankData },
        { upsert: true }
      );
    }
  }
}
```

## 更新注意事项
1. 确保数据匹配的准确性
2. 处理百分比数据时注意数值转换
3. 处理空值和异常数据
4. 保持数据一致性
5. 记录处理日志

## 更新频率
- 建议每学期更新一次数据
- 在重要考试结果公布后及时更新
- 保持数据时效性

## 数据验证
1. 检查数据完整性
2. 验证百分比计算准确性
3. 确保数据格式正确
4. 核对关键字段值

## 错误处理
1. 记录数据更新过程中的错误
2. 设置数据验证检查点
3. 实现数据回滚机制
4. 保存更新日志

## 命令行参数说明

### 参数格式
```bash
sh start.sh -t batch -n school_rank -cmd "lib/batchBase.coffee batch/school_rank/update_school_rank.coffee -f 文件名 -y 年份"
```

### 参数说明
- `-f` 或 `--fileName`: 数据文件路径（必需）
- `-y` 或 `--year`: 数据年份，例如2024（代表2023-2024学年）（必需）
- `-n` 或 `--dryrun`: 试运行模式（可选）

## 数据验证规则详解

### 1. 基础数据验证
- 跳过包含`#DIV/0!`的数据行
- 跳过样本量为0的数据
- 跳过总学生数为0的数据

### 2. 百分比数据转换
- 将百分比值转换为整数（放大10000倍）
- 使用`Math.round(parseFloat(value) * 10000)`进行转换
- 确保数值的精确性和一致性

### 3. 数值有效性检查
- 检查数值是否大于0
- 处理空值和异常数据
- 确保数据格式正确

## 学校名称匹配规则

### 1. 名称优化处理
- 使用`generateSchoolName`处理特殊缩写和格式
- 使用`delSchoolCommWord`移除常见词
- 统一转换为小写并规范化空格

### 2. 数据库查询匹配
- 使用精确匹配和模糊匹配相结合
- 支持大小写不敏感的匹配
- 记录未匹配的学校名称

## 数据处理流程详解

### 1. 专业分布数据处理
- 处理14个专业类别数据
- 包括专业名称、学生数量和百分比
- 只保留有效数据（数量或百分比大于0）

### 2. 大学录取数据处理
- 处理16所主要大学录取数据
- 包括大学名称、录取人数和百分比
- 过滤无效数据（百分比为0的记录）

## 错误处理机制

### 1. 未匹配学校处理
- 记录所有未匹配的学校名称
- 生成详细的未匹配学校列表
- 提供后续处理建议

### 2. 处理结果报告
- 显示处理总时间
- 统计更新的学校数量
- 输出未匹配学校列表

## 数据库更新操作

### 1. 数据更新结构
- 基础信息：年份、总人数、样本量等
- 专业分布数据：各专业学生数量和百分比
- 大学录取数据：各大学录取人数和百分比
- Ivy League相关数据：旧版、新版和总计

### 2. 更新操作
- 使用`updateOne`进行更新
- 支持`upsert`操作
- 保持数据一致性 