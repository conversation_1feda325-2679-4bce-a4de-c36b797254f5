# EQAO数据处理流程文档

## 目录
- [EQAO数据处理流程文档](#eqao数据处理流程文档)
  - [目录](#目录)
  - [一、功能实现说明](#一功能实现说明)
    - [1. 数据抓取模块 (school\_eqao.js)](#1-数据抓取模块-school_eqaojs)
    - [2. 数据处理模块 (school\_eqao.coffee)](#2-数据处理模块-school_eqaocoffee)
    - [3. 排名更新模块 (school\_rank\_from\_xlsx.coffee)](#3-排名更新模块-school_rank_from_xlsxcoffee)
    - [4. 数据导出模块 (schoolEqaoSortAndVcStats\_2024.coffee)](#4-数据导出模块-schooleqaosortandvcstats_2024coffee)
  - [二、数据处理流程](#二数据处理流程)

## 一、功能实现说明

### 1. 数据抓取模块 (school_eqao.js)
**功能实现**:
1. 浏览器控制
   - `newPuppeteerBrowser()`: 创建无头浏览器实例，设置并发数和访问间隔
   - `newPuppeteerPage()`: 创建新页面，设置视口大小和超时时间

2. 数据获取
   - `getAllLinks()`: 
     - 根据搜索条件获取学校/教育局链接
     - 支持分页获取
     - 处理搜索结果重定向
   - `getDetails()`:
     - 解析学校/教育局详细信息
     - 提取EQAO成绩数据
     - 处理特殊学校数据

3. 数据存储
   - 保存到school_log集合
   - 支持断点续传
   - 错误重试机制

**使用方式**:
```bash
# 从Excel文件读取学校信息
./start.sh lib/batchBase.coffee batch/puppeteer/src/school_eqao/school_eqao.js -f 文件路径

# 从数据库集合读取学校信息
./start.sh lib/batchBase.coffee batch/puppeteer/src/school_eqao/school_eqao.js -c school_board

# 搜索所有board信息
./start.sh lib/batchBase.coffee batch/puppeteer/src/school_eqao/school_eqao.js -b true

# 获取特定no_board_urls列表中的学校信息
./start.sh lib/batchBase.coffee batch/puppeteer/src/school_eqao/school_eqao.js -n true
```

### 2. 数据处理模块 (school_eqao.coffee)
**功能实现**:
1. 数据预处理
   - `getCollegeCampus()`: 识别大学和校区信息
   - `deleteEmptyNewEqao()`: 清理无效数据
   - `formatData()`: 标准化数据格式

2. 数据匹配
   - `matchSchoolAndSave()`: 
     - 匹配学校基本信息
     - 处理特殊学校数据
     - 更新学校关联信息

3. 数据存储
   - `saveToEqaoSchool()`: 保存EQAO数据
   - `saveEqaoIdToDB()`: 更新学校ID关联

### 3. 排名更新模块 (school_rank_from_xlsx.coffee)
**功能实现**:
1. 数据解析
   - `isNeedValue()`: 识别需要处理的列
   - `formatData()`: 转换数据格式
   - `buildQuery()`: 构建查询条件

2. 数据更新
   - `findOneByMatchId()`: 查找并更新学校数据
   - `mergeArrayByYearmark()`: 合并历史数据
   - `updateOne()`: 执行数据更新

### 4. 数据导出模块 (schoolEqaoSortAndVcStats_2024.coffee)
**功能实现**:
1. 数据统计
   - `addSchoolsVcToWorkBook()`: 统计学校查看量
   - `addSchoolsByEqaoToWorkBook()`: 统计EQAO成绩
   - `getEqaoPublicData()`: 获取公共数据

2. 数据排序
   - 按查看量排序
   - 按EQAO成绩排序
   - 支持多维度排序

3. 数据导出
   - 生成Excel文件
   - 支持自定义导出条件
   - 数据格式转换

## 二、数据处理流程

```mermaid
graph TD
    A[开始] --> B[数据获取]

    subgraph 数据获取 school_eqao.js
    B --> B1["Excel文件<br>getAllLinks()"]
    B --> B2["数据库集合<br>getAllLinks()"]
    B --> B3["搜索board<br>getAllLinks()"]
    B --> B4["no_board_urls<br>getAllLinks()"]
    B1 --> B5["school_log集合<br>getDetails()"]
    B2 --> B5
    B3 --> B5
    B4 --> B5
    end

    subgraph 数据处理与匹配 school_eqao.coffee
    B5 --> C1["格式化数据<br>formatData()"]
    C1 --> C2["匹配学校信息<br>matchSchoolAndSave()"]
    C2 --> C3["更新sch_eqao表<br>saveToEqaoSchool()"]
    C2 --> C4["更新sch_rm_merged表<br>saveEqaoIdToDB()"]
    C3 --> C5["添加eqaoId关联<br>saveEqaoIdToDB()"]
    end

    subgraph 数据导出 schoolEqaoSortAndVcStats_2024.coffee
    C5 --> D1["统计查看量<br>addSchoolsVcToWorkBook()"]
    D1 --> D2["统计EQAO成绩<br>addSchoolsByEqaoToWorkBook()"]
    D2 --> D3["生成Excel报告<br>main()"]
    end

    subgraph 排名更新 school_rank_from_xlsx.coffee
    D3 --> E1["读取排名Excel<br>main()"]
    E1 --> E2["更新sch_eqao.rmrank<br>updateOne()"]
    E2 --> E3["更新sch_rm_merged排名<br>updateOne()"]
    end

    E3 --> F[结束]
```

