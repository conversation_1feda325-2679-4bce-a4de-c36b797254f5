# 学校数据批处理流程

本文档描述了生成`sch_rm_merged`表的批处理流程。

## 目录
- [学校数据批处理流程](#学校数据批处理流程)
  - [目录](#目录)
  - [概述](#概述)
  - [数据源说明](#数据源说明)
  - [处理流程](#处理流程)
    - [1. 数据准备](#1-数据准备)
    - [2. 数据匹配](#2-数据匹配)
    - [3. 过滤数据并生成ID](#3-过滤数据并生成id)
  - [实现细节](#实现细节)
    - [1. 数据准备实现](#1-数据准备实现)
    - [2. 数据匹配实现](#2-数据匹配实现)
    - [3. 索引创建](#3-索引创建)
      - [基础信息索引](#基础信息索引)
      - [评分数据索引](#评分数据索引)
      - [地理位置索引](#地理位置索引)
      - [组合索引](#组合索引)
    - [4. 重复ID处理](#4-重复id处理)
    - [5. 特殊处理逻辑](#5-特殊处理逻辑)
    - [6. 运行参数](#6-运行参数)
    - [7. 性能优化](#7-性能优化)
    - [8. 错误处理](#8-错误处理)

## 概述

批处理流程将从多个数据源获取学校信息，经过数据准备、匹配、过滤等步骤，最终生成标准化的`sch_rm_merged`表。

```mermaid
%%{
  init: {
    'theme': 'base',
    'themeVariables': {
      'fontSize': '16px',
      'fontFamily': 'arial',
      'primaryColor': '#ff6b6b',
      'primaryTextColor': '#fff',
      'primaryBorderColor': '#ff6b6b',
      'lineColor': '#4a90e2',
      'secondaryColor': '#4a90e2',
      'tertiaryColor': '#50c878'
    }
  }
}%%
flowchart TD
    %% 第一部分：数据准备
    subgraph 数据准备
        A1[sch_findschool表] -->|数据源1| B1[数据合并]
        A2[sch_rm_manual表] -->|数据源2| B1
        B1 -->|根据参数筛选| C1[获取待处理学校]
        C1 -->|保存| D1[中间表]
    end

    %% 第二部分：数据匹配
    subgraph 数据匹配
        D1 -->|读取数据| E1[开始匹配]
        E1 -->|查询| F1[sch_eqao表]
        E1 -->|查询| F2[sch_fraser表]
        
        F1 & F2 -->|匹配检查| G1{年级相同 名字地址匹配或者电话邮编等其他信息匹配}
        G1 -->|匹配成功| G2[记录eqaoId/fraserId]
        G1 -->|匹配失败| G3[不更新eqaoId/fraserId]
        
        G2 & G3 -->|数据过滤| H1[filterMergeFields]
        H1 -->|处理| I1[filterOtherThanTel]
        H1 -->|处理| I2[unifyAddress]
        H1 -->|处理| I3[separateUnitFromAddr]
    end

    %% 第三部分：ID处理与保存
    subgraph ID处理与保存
        I1 & I2 & I3 -->|检查| J1{是否存在}
        J1 -->|是| K1[保持原ID]
        J1 -->|否| K2[生成新ID]
        
        K2 -->|读取| L1[(sysdata表)]
        L1 -->|更新| K2
        
        K1 & K2 -->|写入| M1[sch_rm_merged表]
    end

    %% 样式设置
    classDef primary fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff,font-size:16px
    classDef secondary fill:#4a90e2,stroke:#333,stroke-width:2px,color:#fff,font-size:16px
    classDef external fill:#50c878,stroke:#333,stroke-width:2px,color:#fff,font-size:16px
    classDef process fill:#ffb347,stroke:#333,stroke-width:2px,color:#fff,font-size:16px
    classDef database fill:#ffd700,stroke:#333,stroke-width:2px,color:#333,font-size:16px
    classDef decision fill:#ffb347,stroke:#333,stroke-width:2px,color:#fff,font-size:16px,shape:hexagon
    
    class A1,A2 primary
    class F1,F2 external
    class B1,C1,D1 secondary
    class E1,G2,G3,H1,I1,I2,I3,K1,K2,M1 process
    class G1,J1 decision
    class L1 database

    %% 设置子图样式
    style 数据准备 fill:#f8f9fa,stroke:#333,stroke-width:2px,font-size:18px
    style 数据匹配 fill:#f8f9fa,stroke:#333,stroke-width:2px,font-size:18px
    style ID处理与保存 fill:#f8f9fa,stroke:#333,stroke-width:2px,font-size:18px
```

## 数据源说明

系统从以下数据源获取学校信息：

1. `sch_findschool`表
   - 主要数据源
   - 包含学校基础信息
   - 字段包括：`_id`, `nm`, `addr`, `tel`, `fax`, `prov`, `city`, `zip`等

2. `sch_rm_manual`表
   - 手动维护的数据源
   - 用于补充和修正`sch_findschool`的数据
   - 优先级高于`sch_findschool`

3. 外部数据源
   - `sch_eqao`表：包含EQAO考试数据
   - `sch_fraser`表：包含Fraser排名数据

## 处理流程

### 1. 数据准备

从两个主要数据源获取并合并数据：
- `sch_findschool`表
- `sch_rm_manual`表

处理步骤：
1. 根据传入的参数筛选需要处理的学校
2. 将筛选后的数据保存到中间表
3. 原始数据源的`_id`将被保存为`source_id`字段，用于追踪数据来源

### 2. 数据匹配

将处理后的数据与`sch_eqao`和`sch_fraser`表进行匹配。匹配过程包括：

1. 数据匹配
   - 查询`sch_eqao`和`sch_fraser`表
   - 进行年级匹配（gradeMatched）
   - 进行名称地址匹配（nmAddrMatched）
   - 进行学校信息匹配（schoolMatched）
   - 根据匹配结果决定是否更新`eqaoId`和`fraserId`
     - 如果匹配成功，记录`eqaoId`和`fraserId`
     - 如果匹配不成功，则当前学校没有eqao和fraser网站来源数据
2. 匹配条件
   - if gradeMatched and (nmAddrMatched or schoolMatched)则认为是同一个学校

### 3. 过滤数据并生成ID

对匹配后的数据进行处理并生成ID：

1. 数据过滤
   - 使用`filterMergeFields`函数处理数据
   - 处理电话传真（filterOtherThanTel）
   - 统一地址格式（unifyAddress）
   - 分离单元号（separateUnitFromAddr）

2. ID处理
   - 检查学校是否存在
   - 如果存在，保持原ID
   - 如果不存在，生成新ID
   - 使用`sysdata`表管理ID计数器
   - 将处理后的数据写入`sch_rm_merged`表

## 实现细节

### 1. 数据准备实现

数据准备的主要逻辑是：
1. 将`sch_rm_manual`表的数据转换为Map，便于快速查找
2. 处理`sch_findschool`表的数据，优先使用`sch_rm_manual`的数据
3. 根据参数筛选需要处理的学校
4. 保存到中间表

关键代码：
```coffeescript
# 创建manualSchools的Map
manualMap = new Map()
for school in manualSchools
  manualMap.set(school._id.toString(), {
    ...school,
    source: 'manual',
    sourceId: school._id
  })

# 处理findschool数据
processedSchools = findSchools.map (school) ->
  findSchoolId = school._id.toString()
  manualSchool = manualMap.get(findSchoolId)
  
  if manualSchool
    # 使用manual数据但保留findschool的sourceId
    return {
      ...school,
      ...manualSchool,
      source: 'findschool',
      sourceId: school._id
    }
  else
    return {
      ...school,
      source: 'findschool',
      sourceId: school._id
    }
```

### 2. 数据匹配实现

数据匹配的主要步骤：
1. 准备查询条件（省份、城市、名称等）
2. 匹配EQAO数据
3. 通过EQAO ID匹配Fraser数据
4. 如果没有找到EQAO数据，直接匹配Fraser数据

关键代码：
```coffeescript
# 准备查询条件
baseQuery = {
  prov: school.prov
  city: school.city
}

# 生成名称正则表达式
dealNm = delSchoolCommWord(school.nm.toLowerCase())
nameRegex = new RegExp(dealNm, 'i')

# 构建查询条件
queryOr = [{nm: nameRegex}]
if school.uaddr?.length then queryOr.push({uaddr: school.uaddr})
if school.zip?.length then queryOr.push({zip: school.zip})
if school.tel?.length then queryOr.push({tel: school.tel})
if school.fax?.length then queryOr.push({fax: school.fax})

# 匹配EQAO数据
eqaoSchools = await SchEqaoCol.findToArray(query)
for eqaoSchool in eqaoSchools
  {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, eqaoSchool)
  if gradeMatched and (nmAddrMatched or schoolMatched)
    addEqaoData(school, eqaoSchool._id, eqaoSchool)
    # 尝试匹配对应的Fraser数据
    mident = eqaoSchool._id.replace(/\D/g, '')
    fraserQuery = {
      ...baseQuery
      _id: new RegExp(mident)
    }
    fraserSchool = await SchFraserCol.findOne(fraserQuery)
    if fraserSchool?._id
      addFraserData(school, fraserSchool._id, fraserSchool)
  break
```

### 3. 索引创建

系统为`sch_rm_merged`表创建了以下索引：

#### 基础信息索引
```coffeescript
await SchMergedCol.createIndex({sourceId: 1}, {background: true})
await SchMergedCol.createIndex({IsActive: 1}, {background: true})
await SchMergedCol.createIndex({nm: 1}, {background: true})
```

#### 评分数据索引
```coffeescript
await SchMergedCol.createIndex({g3Rating: 1}, {background: true})
await SchMergedCol.createIndex({g6Rating: 1}, {background: true})
await SchMergedCol.createIndex({g9Rating: 1}, {background: true})
await SchMergedCol.createIndex({fitotal: 1}, {background: true})
await SchMergedCol.createIndex({firate: 1}, {background: true})
```

#### 地理位置索引
```coffeescript
await SchMergedCol.createIndex({prov: 1, city: 1, addr: 1}, {background: true})
await SchMergedCol.createIndex({'bnds.features.geometry': '2dsphere'}, {background: true})
await SchMergedCol.createIndex({lat: 1, lng: 1}, {background: true})
```

#### 组合索引
```coffeescript
await SchMergedCol.createIndex({fraserId: 1, firank: 1}, {background: true})
await SchMergedCol.createIndex({IsActive: 1, prov: 1, city: 1}, {background: true})
await SchMergedCol.createIndex({IsActive: 1, nm: 1}, {background: true})
```

### 4. 重复ID处理

系统会检查并处理重复的`eqaoId`和`fraserId`：

1. 检查重复ID
   - 使用聚合管道检查重复的`eqaoId`
   - 使用聚合管道检查重复的`fraserId`
   - 收集所有重复记录的信息

2. 处理重复记录
   - 按照优先级规则处理重复记录：
     1. 优先删除`source`为`manual`的记录
     2. 如果都不是`manual`，则删除`bns.length`为0的记录
     3. 如果所有记录都没有`bns`，则保留第一条记录

3. 特殊处理
   - 对`St. Michael's Choir`学校进行特殊处理
   - 确保保留正确的学校记录

### 5. 特殊处理逻辑

1. 学校名称处理
   - 删除括号内的信息
   - 使用正则表达式进行模糊匹配
   - 处理特殊字符和格式

2. 年级处理
   - 判断是否为同一学校的不同年级
   - 检查学校类型（小学、中学、高中）
   - 处理年级范围重叠情况

3. 数据合并规则
   - 优先使用`sch_rm_manual`表的数据
   - 保留原始数据源的`sourceId`
   - 处理重复的学校记录

### 6. 运行参数

脚本支持以下运行参数：

1. `init`
   - 初始化模式
   - 清空现有数据
   - 重新生成所有ID
   - 重置计数器

2. `dryrun`
   - 测试模式
   - 不实际修改数据库
   - 用于验证数据处理逻辑
   - 输出处理结果但不保存

使用示例：
```bash
# 初始化模式
sh start.sh -t batch -n generateSchRmMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee init"

# 测试模式
sh start.sh -t batch -n generateSchRmMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee dryrun"

# 正常更新模式
sh start.sh -t batch -n generateSchRmMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee"
```

### 7. 性能优化

1. 数据流处理
   - 使用流式处理大量数据
   - 分批处理避免内存溢出
   - 使用中间表暂存数据

2. 索引优化
   - 创建必要的单字段索引
   - 创建组合索引提高查询效率
   - 使用后台创建索引避免阻塞

3. 内存管理
   - 使用Map结构优化查找
   - 及时清理临时数据
   - 控制批处理大小

### 8. 错误处理

1. 数据验证
   - 检查必要字段是否存在
   - 验证数据格式和类型
   - 处理异常数据

2. 错误恢复
   - 记录详细的错误信息
   - 支持断点续传
   - 提供错误重试机制

3. 日志记录
   - 记录处理进度
   - 记录错误和警告
   - 提供统计信息