分离不同来源的学校
https://www.eqao.com: sch_eqao
https://www.compareschoolrankings.org: sch_fraser
www.findschool.ca: sch_findschool
关键信息合并后保存在sch_rm_merged中

sch_eqao所有字段
  _id:eqao mident id
  addr :''
  city:''
  eqao:[{}]官网获取的数据，详见  /src/batch/puppeteer/src/school_eqao/key.md
  eqaourl: eqao网页url
  fax:''
  nm:''
  prov:''
  status:''
  tel:''
  uaddr:''
  zip:''
  boardIdEqao:''
  #更新表格中排名数据增加的字段
  rmRank:[
        {
            (g3|g6|g9|OSSLT)rmScore : 总分
            (g3|g6|g9|OSSLT)rmRanking : 总排名
            OSSLTrmPt : 参与率
            g9rmMathPt : 房大师Math参与率
            (g3|g6|g9)rmMathLv3 : 房大师Math良率
            (g3|g6|g9)rmMathLv4 : 房大师Math优率
            (g3|g6|g9)rmMathScore : 房大师Math得分
            (g3|g6|g9)rmMathRanking : 房大师Math排名
            (g3|g6)rmReadingLv3 : 房大师Reading良率
            (g3|g6)rmReadingLv4 : 房大师Reading优率
            (g3|g6)rmReadingScore : 房大师Reading得分
            (g3|g6)rmReadingRanking : 房大师Reading排名
            (g3|g6)rmWritingLv3 : 房大师Writing良率
            (g3|g6)rmWritingLv4 : 房大师Writing优率
            (g3|g6)rmWritingScore : 房大师Writing得分
            (g3|g6)rmWritingRanking : 房大师Writing排名
            (g3|g6|g9)total : 排名总数量
            (g3|g6|g9)rmRankChange : 遇上一条数据对比，排名的变化
            (g3|g6|g9)rmRatingChange : 遇上一条数据对比，总分的变化
            yearmark : 2023-2024,
        }
    ]
  rmG3Ranking :number
  rmG6Ranking :number
  rmG9Rating :number

sch_fraser所有字段
  _id:fraser id
  addr:''
  city:''
  fiBoard:''
  fiId:''
  fiurl:''
  fraser:[{}]官网获取的数据，详见  /src/batch/puppeteer/src/school_fraser/school_fraser.js#80
  grd:''
  nm:''
  prov:''
  tel:''
  tp:''
  ts:''
  url:''



sch_findschool所有字段
  _id : NumberInt(1024672),findschool id
  IsActive : NumberInt(1),
  _mt : ISODate(2025-01-21T16:16:05.866+0000),
  addr : '101 Van Horne Ave',
  ap : number 1/0,
  art : number 1/0,
  bndMt : ISODate(2025-01-21T13:12:34.243+0000),
  bnds : {}
  bns : []
  catholic : number 1/0,Catholic
  city : 'North York',
  date : '2025-01-20',
  ef : number 1/0,Extended French
  ele : number 1/0,Elementary
  eng : number 1/0,English
  fax : '',
  fi : number 1/0,French Immersion
  gf : number ,grade from
  gif : number 1/0,Gifted
  gt : number ,grade to
  hgh : number 1/0,Secondary
  ib : number 1/0,IB
  lat : '43.7848574',
  lng : '-79.359484',
  loc : [
      43.7848574,
      -79.359484
  ],
  mid : number 1/0,Middle
  mt : ISODate(2025-01-21T16:15:53.052+0000),
  nation : Canada,
  nm : Divine Mercy Catholic Elementary School,
  prov : 'ON',
  pub : number 1/0,Public
  sbid : 'cc092dae-6966-4599-b9a3-e60cb93fd86c',
  sport : number 1/0,Sport
  tel : '************',
  ts : ISODate(2025-01-21T13:12:34.243+0000),
  url : 'https://www.tcdsb.org/o/divinemercy',
  useSch : number 1/0,
  zip : 'M2J2S8',
  rmG3Rating:number
  rmG6Rating:number
  rmG9Rating:number

sch_rm_merged所有字段，用于查找 排序
  city,
  prov,
  addr,
  IsActive,
  nm,
  fi,
  ele,
  eng,
  ib,
  pub,
  ap,
  catholic,
  mid,
  art,
  ef,
  hgh,
  gif,
  sport,
  tel,
  fax,
  zip,
  loc,
  bns,
  gf,
  gt,
  bnds,
  rmG3Rating,
  rmG6Rating,
  rmG9Rating
  eqaoId
  rmG3Ranking
  rmG6Ranking
  rmG9Ranking
  fraserId
  firank
  fitotal
  firate

修改之后的列表查询逻辑
  1. 查询sch_rm_merged，根据条件返回matched 结果
  2. 如果sch_rm_merged 结果有eqaoId，则查询sch_eqao，添加rmrank信息

修改之后的详细查询逻辑
  1. 查询sch_rm_merged，根据条件返回matched 结果
  2. 查询school对应数据，拿回之前的数据
  3. 查询sch_findschool 找到active的最新数据
  4. 如果sch_rm_merged 结果有eqaoId，则查询sch_eqao，添加eqao，rmrank信息
  5. 如果sch_rm_merged 结果有fraserId，则查询sch_fraser，添加fraser信息
sch_eqa.eqao和sch_findschool.eqao 根据年份合并数据然后展示







