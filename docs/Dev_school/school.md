# 1. Public School
  ## 1.1 School List
  ## 1.2 School Detail
  ## 1.3 Show School in Map
  ## 1.4 Home School
```
{
    "_id" : NumberInt(1021541),
    "nm" : "St. Robert Catholic High School",
    "catholic" : NumberInt(1),
    "pub" : NumberInt(0),
    "prov" : "ON",
    "nation" : "Canada",
    "url" : "http://stro.ycdsb.ca/",
    "date" : "2023-07-31",
    "sbid" : "9b64a6ee-57ab-4756-925b-553cc6afd28b",
    "addr" : "8101 Leslie St",
    "city" : "Thornhill",
    "zip" : "L3T7P4",
    "tel" : "************",
    "fax" : "************",
    "lat" : 43.8340078152468,
    "lng" : -79.3798125,
    "IsActive" : NumberInt(1),
    "Comments" : "",
    "bns" : [
        {
            "bnid" : NumberInt(1022194),
            "bn" : [
                43.8307572,
                -79.4452584,
            ],
            "exclude" : NumberInt(0),
            "exbnid" : NumberInt(-1),
            "gf" : NumberInt(9),
            "gt" : NumberInt(12),
            "useSch" : NumberInt(0),
            "ele" : NumberInt(0),
            "mid" : NumberInt(0),
            "hgh" : NumberInt(1),
            "eng" : NumberInt(1),
            "fi" : NumberInt(0),
            "ef" : NumberInt(0),
            "ap" : NumberInt(0),
            "ib" : NumberInt(0),
            "gif" : NumberInt(0),
            "art" : NumberInt(0),
            "sport" : NumberInt(0),
            "sw" : [
                43.798025,
                -79.4452584
            ],
            "ne" : [
                43.8652043,
                -79.3520034
            ]
        }
    ],
    "SchoolAssocationArray" : [
    ],
    "loc" : [
        43.8340078152468,
        -79.3798125
    ],
    "useSch" : NumberInt(0),
    "gf" : NumberInt(9),
    "gt" : NumberInt(12),
    "ele" : NumberInt(0),
    "mid" : NumberInt(0),
    "hgh" : NumberInt(1),
    "eng" : NumberInt(1),
    "fi" : NumberInt(0),
    "ef" : NumberInt(0),
    "eqao" : [
        {
            "yearmark" : "2022-2023",
            "g9rank" : NumberInt(3),
            "g9total" : NumberInt(259),
            "g9findschoolrating" : NumberInt(3),
            "g9last5avg" : NumberInt(87),
            "g9acstu" : NumberInt(419),
            "g9acm" : 90.7,
            "g10ossltfstua" : NumberInt(403),
            "g10ossltfsuccess" : 97.8,
            "g10ossltrank" : NumberInt(3),
            "g10osslttotal" : NumberInt(258),
            "g10ossltfindschoolrating" : NumberInt(98),
            "g10ossltlast5avg" : NumberInt(98)
        }
    ],
    "fiurl" : "http://ontario.compareschoolrankings.org/secondary/St_Robert_Catholic_High_School/Thornhill/Report_Card.aspx",
    "fraser" : [
        {
            "yearmark" : "2018-2019",
            "school_id" : NumberInt(1021541),
            "fitotal" : NumberInt(739),
            "firank" : NumberInt(18),
            "firate" : "8.7",
            "fiesl" : "12.1",
            "fispecial" : "6.2",
            "fiincome" : null,
            "fivs" : ""
        }
    ],
    "bnds" : {
        "type" : "FeatureCollection",
        "features" : [
            {
                "type" : "Feature",
                "geometry" : {
                    "type" : "Polygon",
                    "coordinates" : [
                        []
                    ]
                },
                "properties" : {
                    "bnid" : NumberInt(1022194),
                    "exclude" : NumberInt(0),
                    "exbnid" : NumberInt(-1),
                    "useSch" : NumberInt(0),
                    "gf" : NumberInt(9),
                    "gt" : NumberInt(12),
                    "ele" : NumberInt(0),
                    "hgh" : NumberInt(1),
                    "eng" : NumberInt(1),
                    "fi" : NumberInt(0),
                    "ef" : NumberInt(0)
                },
                "bbox" : [
                    43.798025,
                    -79.4452584,
                    43.8652043,
                    -79.3520034
                ]
            }
        ]
    },
    "ap" : NumberInt(0),
    "art" : NumberInt(0),
    "gif" : NumberInt(0),
    "ib" : NumberInt(1),
    "sport" : NumberInt(0),
    "favs" : [
        ObjectId("5c98332cef56ba57f221385f")
    ],
    "mt" : ISODate("2023-11-16T21:27:37.909+0000"),
    "_mt" : ISODate("2023-11-24T19:11:41.888+0000"),
    "bndMt" : ISODate("2023-11-16T21:27:37.909+0000"),
    "rmG9Rating" : NumberInt(3)
}
```
# 2. Private School
  ## 2.1 Private School List
  ## 2.2 Private School Detail
  ## 2.3 Show Private School in Map
 
# 3. University,College
  ## 3.1 show in List
  ## 3.2 show in Map
  ## data structure
```
{
    "_id" : ObjectId("5f5aaf9c76d7993168e86039"),
    "nm" : "Aurora College",
    "campus" : [ 
        {
            "nm" : "Aurora College",
            "addr" : "1290-50 Conibear Crescent",
            "city" : "Fort Smith",
            "prov" : "Northwest Territories",
            "zip" : "X0E 0P0",
            "main" : 1,
            "tel" : "************",
            "tollFree" : "************",
            "website" : "http://www.auroracollege.nt.ca/",
            "uaddr" : "CANADA:NORTHWEST TERRITORIES:FORT SMITH:1290-50 CONIBEAR CRESCENT",
            "lat" : 60.0022119,
            "lng" : -111.8981757
        }
    ],
    "href" : "https://www.collegesinstitutes.ca/members/aurora-college/",
    "tp" : "college",
    "logo" : "https://www.collegesinstitutes.ca/wp-content/uploads/2015/11/1_member_logo_aurora.jpg",
    "mt" : ISODate("2021-03-28T04:01:32.363Z")
}
```


## import University 
1. folder: universityimport
2. download data use phantomjs 
  /Users/<USER>/Downloads/phantomjs-2.1.1-macosx/bin/phantomjs ./universitiesNew.js
  /Users/<USER>/Downloads/phantomjs-2.1.1-macosx/bin/phantomjs ./bcCollege.js
  /Users/<USER>/Downloads/phantomjs-2.1.1-macosx/bin/phantomjs ./onCollege.js
3. download college with python
  python ./importColleges.py
/Users/<USER>/Downloads/phantomjs-2.1.1-macosx/bin/phantomjs ./collegesNew.js

4. import json to db
go cfg
./start.sh universityimport/universityAndCollegeimport.coffee

5. add universityGeocoding
./start.sh universityimport/universityGeocoding.coffee

## private school

0. from Gov: https://data.ontario.ca/dataset/private-school-contact-information
1. folder
   1. batch/school
2. download 私校数据 python ./private_schools_scraping.py
3. import private_school.json to db，手动修改没有lng，lat的数据
  ./start.sh lib/batchBase.coffee batch/school/privateSchoolImport.coffee
3. 处理addr和nm相同的学校数据
  ./start.sh lib/batchBase.coffee  batch/school/mergeSameAddrSchool.coffee


## import public school
列表来源
* Or from https://data.ontario.ca/dataset/school-information-and-student-demographics
* Or from https://data.ontario.ca/dataset/ontario-public-school-contact-information
* (achievement is not public) https://data.ontario.ca/dataset/eqao-grade-9-student-achievement-results-by-year 
* gender/sex by board: https://data.ontario.ca/dataset/school-enrolment-by-gender

单个详细：
* Or from https://www.app.edu.gov.on.ca/eng/sift/schoolProfile.asp?SCH_NUMBER=320919
* Or from https://www.tdsb.on.ca/Find-your/School/By-Map/focusonschool/5219

其他：
1. batch using findSchoolV4 from 3rd party
2. Ministry: Education
   https://data.ontario.ca/organization/education?q=+achievement+level&sort=score+desc%2C+metadata_modified+desc
3. EQAO(only available to employee): https://www.eqao.com/login/secure-reports-and-data-files/


## about EQAO webapp/data
1. made using microsoft powerbi
2. web app: https://eqaoweb.eqao.com/EQAOReports/Public?rpt=Grade9_ACH_EN
3. 每次点击出数据，而且经过加密
4. 学校详细页面很简单但是没link https://www.eqao.com/report/?id=242&mident=849626  https://www.eqao.com/page-sitemap.xml
5. search page: https://www.eqao.com/ss?q=Robert+Catholic+High+School  https://www.eqao.com/ss?q=+&b=3

only includes grade passing percentage;
data not included but needed:
[X] gender/sex
[X] archievement level distribution, level1/2/3/4 count
[X] first language is eng (xlsx)
[X] low inclome household (xlsx)
[X] no degree household (xlsx)

## univ/college applications
(not available to public)https://data.ontario.ca/dataset/university-and-college-applications-by-institution-and-submission-period

## import school board stat
https://data.ontario.ca/dataset/school-board-achievements-and-progress

## school.bnds 编辑
http://www.test:8080/school
src/webroot/public/html/inputSchool.html
见： docs/Dev_boundary/boundaryManage.md
TODO: 
1. 能够编辑学校基础信息，如：地址，电话，url,rank等
2. +display in app/web
3. +batch计算排名，或者手动输入 @from @tao
4. +edit school Notes @tao new req


## 统计信息/工作量
8122 学校
2465 = {$or:[{rmG3Rating:{$exists:true}},{'eqao.g3ReadLvl':{$exists:true}}]}
2286 = {$or:[{rmG6Rating:{$exists:true}},{'eqao.g6ReadLvl':{$exists:true}}]}
528 = {$or:[{rmG9Rating:{$exists:true}},{'eqao.g9Lvl':{$exists:true}},{'eqao.g10Osslt':{$exists:true}}]}
2698 = 有bns
80 = boards
有些学校找不到bnds，如: Britannia Public School
https://britannia.peelschools.org/school-boundaries

但是在这里能找到 https://www.peelschools.org/documents/fd871ea9-c296-4f3e-924a-a67fe41660cb/2023-24-APD.pdf

100*3*20min = 100hr
2698*20min = 900hr