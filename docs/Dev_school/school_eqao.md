# 1. school_log collection
## 1.1 province: [isProvince:true]
  ```
  {
      "_id" : ObjectId("670080e3e8c5b98b74d6e682"),
      "nm" : "Ontario",
      "_mt" : ISODate("2024-10-09T18:34:03.739+0000"),
      "eqao" : [
          {
              "yearmark" : "2023-2024",
              "langOfInstruction" : "English",
              "g3Studnts" : "129628",
              "g3Prtcptng" : "95",
              "g3FstLangEn" : "75",
              "g3Ell" : "10",
              "g3SpclEdcton" : "12",
              "g3WrtAtOrAbvProvStdntPct" : "64",
              "g3ReadAtOrAbvProvStdntPct" : "71",
              "g3MathAtOrAbvProvStdntPct" : "61",
              "g3MathProvLvl" : [
                  "1",
                  "8",
                  "30",
                  "45",
                  "16"
              ],
              "g3ReadProvLvl" : [
                  "<1",
                  "1",
                  "27",
                  "57",
                  "14"
              ],
              "g3WrtProvLvl" : [
                  "<1",
                  "2",
                  "34",
                  "63",
                  "1"
              ],
              "g6Studnts" : "135729",
              "g6Prtcptng" : "96",
              "g6FstLangEn" : "72",
              "g6Ell" : "9",
              "g6SpclEdcton" : "19",
              "g6WrtAtOrAbvProvStdntPct" : "80",
              "g6ReadAtOrAbvProvStdntPct" : "82",
              "g6MathAtOrAbvProvStdntPct" : "50",
              "g6MathProvLvl" : [
                  "1",
                  "6",
                  "43",
                  "42",
                  "8"
              ],
              "g6ReadProvLvl" : [
                  "<1",
                  "1",
                  "17",
                  "70",
                  "12"
              ],
              "g6WrtProvLvl" : [
                  "<1",
                  "1",
                  "18",
                  "62",
                  "19"
              ],
              "g9Studnts" : "147212",
              "g9Prtcptng" : "90",
              "g9FstLangEn" : "73",
              "g9Ell" : "6",
              "g9SpclEdcton" : "18",
              "g9AtOrAbvProvStdntPct" : "54",
              "g9ProvLvl" : [
                  "<1",
                  "10",
                  "36",
                  "46",
                  "8"
              ],
              "g10OssltStudnts" : "143089",
              "g10OssltPrtcptng" : "92",
              "g10OssltFstLangEn" : "72",
              "g10OssltEll" : "3",
              "g10OssltSpclEdcton" : "19",
              "g10OssltProv" : "85"
          },
          {
              "yearmark" : "2023-2024",
              "langOfInstruction" : "French",
              "g3Studnts" : "8689",
              "g3Prtcptng" : "97",
              "g3FstLangFr" : "42",
              "g3Alf" : "30",
              "g3Pana" : "3",
              "g3SpclEdcton" : "14",
              "g3WrtAtOrAbvProvStdntPct" : "67",
              "g3ReadAtOrAbvProvStdntPct" : "84",
              "g3MathAtOrAbvProvStdntPct" : "74",
              "g3MathProvLvl" : [
                  "<1",
                  "3",
                  "23",
                  "51",
                  "23"
              ],
              "g3ReadProvLvl" : [
                  "0",
                  "<1",
                  "16",
                  "47",
                  "37"
              ],
              "g3WrtProvLvl" : [
                  "1",
                  "5",
                  "27",
                  "60",
                  "8"
              ],
              "g6Studnts" : "8496",
              "g6Prtcptng" : "97",
              "g6FstLangFr" : "41",
              "g6Alf" : "19",
              "g6Pana" : "3",
              "g6SpclEdcton" : "20",
              "g6WrtAtOrAbvProvStdntPct" : "79",
              "g6ReadAtOrAbvProvStdntPct" : "97",
              "g6MathAtOrAbvProvStdntPct" : "58",
              "g6MathProvLvl" : [
                  "<1",
                  "4",
                  "38",
                  "47",
                  "11"
              ],
              "g6ReadProvLvl" : [
                  "0",
                  "0",
                  "3",
                  "54",
                  "43"
              ],
              "g6WrtProvLvl" : [
                  "<1",
                  "4",
                  "17",
                  "60",
                  "19"
              ],
              "g9Studnts" : "6968",
              "g9Prtcptng" : "97",
              "g9FstLangFr" : "48",
              "g9Alf" : "9",
              "g9Pana" : "2",
              "g9SpclEdcton" : "18",
              "g9AtOrAbvProvStdntPct" : "61",
              "g9ProvLvl" : [
                  "<1",
                  "7",
                  "32",
                  "52",
                  "8"
              ],
              "g10OssltStudnts" : "7058",
              "g10OssltPrtcptng" : "96",
              "g10OssltFstLangFr" : "48",
              "g10OssltAlf" : "5",
              "g10OssltPana" : "2",
              "g10OssltSpclEdcton" : "19",
              "g10OssltProv" : "92"
          }
      ],
      "eqaourl" : "/results/",
      "isProvince" : true,
      "prov" : "ON"
  }

  ```


## 1.2 board: [isBoard:true]
```
{
    "_id" : ObjectId("67008303e8c5b98b74d6f977"),
    "nm" : "Penetanguishene Protestant SSB (79910)",
    "_mt" : ISODate("2024-10-05T06:12:31.061+0000"),
    "addr" : "2 Poyntz Street, Unit 49  , Box 107",
    "city" : "Penetaguishene",
    "eqao" : [
        {
            "yearmark" : "2023-2024",
            "g3Studnts" : "53",
            "g3Prtcptng" : "92",
            "g3FstLangEn" : "71",
            "g3Ell" : "N/R",
            "g3SpclEdcton" : "20",
            "g3WrtAtOrAbvStdntPct" : "80",
            "g3ReadAtOrAbvStdntPct" : "78",
            "g3MathAtOrAbvStdntPct" : "71",
            "g3MathLvl" : [
                "0",
                "0",
                "29",
                "45",
                "27"
            ],
            "g3ReadLvl" : [
                "0",
                "0",
                "22",
                "63",
                "14"
            ],
            "g3WrtLvl" : [
                "0",
                "0",
                "20",
                "80",
                "0"
            ],
            "g6Studnts" : "28",
            "g6Prtcptng" : "89",
            "g6FstLangEn" : "N/R",
            "g6Ell" : "N/R",
            "g6SpclEdcton" : "N/R",
            "g6WrtAtOrAbvStdntPct" : "92",
            "g6ReadAtOrAbvStdntPct" : "100",
            "g6MathAtOrAbvStdntPct" : "64",
            "g6MathLvl" : [
                "0",
                "4",
                "32",
                "48",
                "16"
            ],
            "g6ReadLvl" : [
                "0",
                "0",
                "0",
                "80",
                "20"
            ],
            "g6WrtLvl" : [
                "0",
                "0",
                "8",
                "88",
                "4"
            ],
            "g9Studnts" : "28",
            "g9Prtcptng" : "89",
            "g9FstLangEn" : "N/R",
            "g9Ell" : "N/R",
            "g9SpclEdcton" : "N/R",
            "g9AtOrAbvStdntPct" : "64",
            "g9Lvl" : [
                "0",
                "4",
                "32",
                "48",
                "16"
            ],
            "g10OssltStudnts" : "28",
            "g10OssltPrtcptng" : "89",
            "g10OssltFstLangEn" : "N/R",
            "g10OssltEll" : "N/R",
            "g10OssltSpclEdcton" : "N/R"
        }
    ],
    "eqaourl" : "/results/?orgType=B&mident=79910&yearnum=2024",
    "fax" : "(*************",
    "isBoard" : true,
    "langOfInstruction" : "English",
    "numberOfSchool" : NumberInt(1),
    "prov" : "ON",
    "schools" : [
        {
            "nm" : "Burkevale Protestant Sep S",
            "link" : "/results/?orgType=S&mident=439738&n=Burkevale%20Protestant%20Sep%20S&lang=&yearnum=2024",
            "mident" : "439738"
        }
    ],
    "tel" : "(************* ",
    "zip" : "L9M1M2"
}
```

## 1.3 school
```
{
    "_id" : ObjectId("6700f23be8c5b98b74daf906"),
    "nm" : "Walter Zadow PS (90557)",
    "_mt" : ISODate("2024-10-05T08:00:59.394+0000"),
    "addr" : "79 Ottawa St",
    "board" : "Renfrew County DSB (66214)",
    "city" : "Arnprior",
    "eqao" : [
        {
            "yearmark" : "2023-2024",
            "g3Studnts" : "47",
            "g3Prtcptng" : "100",
            "g3FstLangEn" : "98",
            "g3Ell" : "N/R",
            "g3SpclEdcton" : "N/R",
            "g3WrtAtOrAbvStdntPct" : "55",
            "g3ReadAtOrAbvStdntPct" : "67",
            "g3MathAtOrAbvStdntPct" : "53",
            "g3MathLvl" : [
                "0",
                "9",
                "38",
                "49",
                "4"
            ],
            "g3ReadLvl" : [
                "0",
                "0",
                "33",
                "50",
                "17"
            ],
            "g3WrtLvl" : [
                "0",
                "2",
                "43",
                "53",
                "2"
            ],
            "g6Studnts" : "62",
            "g6Prtcptng" : "98",
            "g6FstLangEn" : "97",
            "g6Ell" : "N/R",
            "g6SpclEdcton" : "23",
            "g6WrtAtOrAbvStdntPct" : "77",
            "g6ReadAtOrAbvStdntPct" : "84",
            "g6MathAtOrAbvStdntPct" : "46",
            "g6MathLvl" : [
                "0",
                "11",
                "43",
                "43",
                "3"
            ],
            "g6ReadLvl" : [
                "0",
                "0",
                "16",
                "75",
                "8"
            ],
            "g6WrtLvl" : [
                "2",
                "0",
                "21",
                "67",
                "10"
            ]
        }
    ],
    "eqaourl" : "/results/?orgType=S&mident=90557&lang=&yearnum=2024",
    "fax" : "(*************",
    "langOfInstruction" : "English",
    "prov" : "ON",
    "tel" : "(************* ",
    "zip" : "K7S1X2"
}


{
    "_id" : ObjectId("6700f25ae8c5b98b74dafa2b"),
    "nm" : "Valour JK to 12 School - Secondary (902747)",
    "_mt" : ISODate("2024-10-05T08:01:30.603+0000"),
    "addr" : "19 Leeder Lane",
    "board" : "Renfrew County DSB (66214)",
    "city" : "Petawawa",
    "eqao" : [
        {
            "yearmark" : "2023-2024",
            "g9Studnts" : "107",
            "g9Prtcptng" : "84",
            "g9FstLangEn" : "96",
            "g9Ell" : "N/R",
            "g9SpclEdcton" : "21",
            "g9AtOrAbvStdntPct" : "34",
            "g9Lvl" : [
                "0",
                "12",
                "53",
                "33",
                "1"
            ],
            "g10OssltStudnts" : "129",
            "g10OssltPrtcptng" : "95",
            "g10OssltFstLangEn" : "93",
            "g10OssltEll" : "N/R",
            "g10OssltSpclEdcton" : "16",
            "g10Osslt" : "86"
        }
    ],
    "eqaourl" : "/results/?orgType=S&mident=902747&lang=&yearnum=2024",
    "fax" : "(*************",
    "langOfInstruction" : "English",
    "prov" : "ON",
    "tel" : "(************* ",
    "zip" : "K8H 0B8"
}
```

# 2. school collection

```
add school_log data into eqao and add boardIdEqao

{
  "_id" : "S-ON-NORTHBAY-MARSHALL-PARK-P",
  "IsActive" : NumberInt(1),
  "eqao" : [
      {
          "yearmark" : "2023-2024",
          "g3Studnts" : "63",
          "g3Prtcptng" : "97",
          "g3FstLangEn" : "93",
          "g3Ell" : "N/R",
          "g3SpclEdcton" : "33",
          "g3WrtAtOrAbvStdntPct" : "39",
          "g3ReadAtOrAbvStdntPct" : "52",
          "g3MathAtOrAbvStdntPct" : "30",
          "g3MathLvl" : [
              "0",
              "15",
              "56",
              "28",
              "2"
          ],
          "g3ReadLvl" : [
              "0",
              "2",
              "46",
              "51",
              "2"
          ],
          "g3WrtLvl" : [
              "0",
              "11",
              "49",
              "39",
              "0"
          ],
          "g6Studnts" : "68",
          "g6Prtcptng" : "93",
          "g6FstLangEn" : "95",
          "g6Ell" : "N/R",
          "g6SpclEdcton" : "30",
          "g6WrtAtOrAbvStdntPct" : "57",
          "g6ReadAtOrAbvStdntPct" : "56",
          "g6MathAtOrAbvStdntPct" : "8",
          "g6MathLvl" : [
              "3",
              "26",
              "62",
              "8",
              "0"
          ],
          "g6ReadLvl" : [
              "0",
              "0",
              "44",
              "56",
              "0"
          ],
          "g6WrtLvl" : [
              "0",
              "3",
              "40",
              "54",
              "3"
          ]
      },
      {
          "yearmark" : "2022–2023",
          ...
      }]
  "boardIdEqao" : "15202"
}
```


# 3. school_board collection
add school_log:board and province into school_board collection
```
add eqao info 

{
    "_id" : "092be798-6935-4334-ba59-a26c96d1a359",
    "_mt" : ISODate("2024-10-09T19:02:55.671+0000"),
    ...
    "eqao" : {
        "nm" : "Halton DSB",
        "_mt" : ISODate("2024-10-05T05:53:41.450+0000"),
        "addr" : "2050 Guelph Line",
        "city" : "Burlington",
        "eqao" : [
            {
                "yearmark" : "2023-2024",
                "g3Studnts" : "4359",
                "g3Prtcptng" : "97",
                "g3FstLangEn" : "60",
                "g3Ell" : "11",
                "g3SpclEdcton" : "11",
                "g3WrtAtOrAbvStdntPct" : "71",
                "g3ReadAtOrAbvStdntPct" : "77",
                "g3MathAtOrAbvStdntPct" : "69",
                "g3MathLvl" : [
                    "1",
                    "6",
                    "25",
                    "48",
                    "21"
                ],
                "g3ReadLvl" : [
                    "0",
                    "1",
                    "22",
                    "58",
                    "19"
                ],
                "g3WrtLvl" : [
                    "<1",
                    "2",
                    "27",
                    "69",
                    "2"
                ],
                "g6Studnts" : "4849",
                "g6Prtcptng" : "97",
                "g6FstLangEn" : "58",
                "g6Ell" : "18",
                "g6SpclEdcton" : "17",
                "g6WrtAtOrAbvStdntPct" : "87",
                "g6ReadAtOrAbvStdntPct" : "88",
                "g6MathAtOrAbvStdntPct" : "62",
                "g6MathLvl" : [
                    "<1",
                    "3",
                    "35",
                    "49",
                    "13"
                ],
                "g6ReadLvl" : [
                    "0",
                    "1",
                    "11",
                    "70",
                    "18"
                ],
                "g6WrtLvl" : [
                    "<1",
                    "<1",
                    "12",
                    "63",
                    "24"
                ],
                "g9Studnts" : "4798",
                "g9Prtcptng" : "92",
                "g9FstLangEn" : "63",
                "g9Ell" : "9",
                "g9SpclEdcton" : "17",
                "g9AtOrAbvStdntPct" : "70",
                "g9Lvl" : [
                    "0",
                    "4",
                    "26",
                    "53",
                    "17"
                ],
                "g10OssltStudnts" : "4933",
                "g10OssltPrtcptng" : "97",
                "g10OssltFstLangEn" : "61",
                "g10OssltEll" : "8",
                "g10OssltSpclEdcton" : "16",
                "g10Osslt" : "92"
            }
        ],
        "eqaourl" : "/results/?orgType=B&mident=66133&yearnum=2024",
        "fax" : "(*************",
        "langOfInstruction" : "English",
        "numberOfSchool" : NumberInt(104),
        "prov" : "ON",
        "schools" : [
            {
                "nm" : "Abbey Lane PS",
                "link" : "/results/?orgType=S&mident=779&n=Abbey%20Lane%20PS&lang=&yearnum=2024",
                "mident" : "779"
            },
        ],
        "tel" : "(************* Ex 2219",
        "zip" : "L7R3Z2",
        "boardIdEqao" : "66133"
    }
}
```
