# AdjFactors数据更新指南

## 目录

- [AdjFactors数据更新指南](#adjfactors数据更新指南)
  - [目录](#目录)
  - [概述](#概述)
  - [流程图](#流程图)
  - [数据处理流程](#数据处理流程)
    - [1. 数据源](#1-数据源)
    - [2. 数据匹配步骤](#2-数据匹配步骤)
    - [3. 数据处理规则](#3-数据处理规则)
      - [3.1 数据清洗](#31-数据清洗)
      - [3.2 数据转换](#32-数据转换)
      - [3.3 数据验证](#33-数据验证)
    - [4. 数据更新操作](#4-数据更新操作)
  - [数据结构](#数据结构)
  - [注意事项](#注意事项)
  - [私立学校处理](#私立学校处理)
    - [1. 私立学校名称映射](#1-私立学校名称映射)
    - [2. 私立学校匹配流程](#2-私立学校匹配流程)
  - [数据验证规则](#数据验证规则)
    - [1. 数据有效性检查](#1-数据有效性检查)
    - [2. 学校名称匹配规则](#2-学校名称匹配规则)
  - [错误处理](#错误处理)
    - [1. 未匹配学校处理](#1-未匹配学校处理)
    - [2. 错误日志记录](#2-错误日志记录)
    - [3. 试运行模式](#3-试运行模式)
  - [性能优化](#性能优化)
    - [1. 批量处理](#1-批量处理)
    - [2. 数据缓存](#2-数据缓存)
  - [监控和报告](#监控和报告)
    - [1. 处理统计](#1-处理统计)
    - [2. 日志记录](#2-日志记录)

## 概述

本文档说明如何将AdjFactors.xlsx中的学校排名和分数数据更新到数据库中。处理流程包括数据匹配、清洗、转换和更新。

## 流程图

```mermaid
graph TD
    A[开始] --> B[读取AdjFactors文件]
    B --> C[遍历学校数据行]
    C --> D{验证数据有效性}
    D -->|无效| C
    D -->|有效| E{学校名称匹配}
    E -->|匹配失败| F[记录未匹配学校]
    F --> C
    E -->|匹配成功| G[构建年度分数数据]
    G --> H[构建综合统计数据]
    H --> I{是否试运行模式?}
    I -->|是| J[仅记录日志]
    I -->|否| K[更新到数据库]
    J --> C
    K --> C
    C -->|所有学校处理完成| L[生成报告]
    L --> M[保存未匹配学校列表]
    M --> N[结束]

    subgraph 数据处理流程
    G --> G1[处理2018-2024年度数据]
    G --> G2[计算分数差异和排名]
    G --> G3[判断排名方向(top/bottom)]
    H --> H1[计算平均分数差异]
    H --> H2[计算上榜次数]
    H --> H3[计算综合排名]
    H --> H4[设置排名方向]
    end

    subgraph 数据验证
    D --> D1[检查学校名称]
    D --> D2[检查分数数据]
    D --> D3[检查排名数据]
    D --> D4[检查差异值合理性]
    end

    subgraph 错误处理
    F --> F1[记录原始名称]
    F --> F2[记录优化后名称]
    F --> F3[记录城市信息]
    F --> F4[保存未匹配日志]
    end
```

## 数据处理流程

### 1. 数据源
- 源文件: 传入文档路径
- 目标数据库表: `sch_rm_rank`
- 关联数据库表: `listing.sch_rm_merged`

### 2. 数据匹配步骤

1. 从CSV/Excel文件读取Ontario Secondary School Name列
2. 优化学校名称匹配
   ```coffeescript
   # 使用schoolHelper函数优化学校名称
   optimizeSchoolName = (name) ->
     return null unless name
     name = generateSchoolName(name)
     name = delSchoolCommWord(name)
     name = name?.toLowerCase()?.trim()?.replace(/\s+/g, ' ')
     return name

   # 查询示例
   query = 
     $or: [
       { nm: { $regex: schoolName, $options: 'i' } },
       { nm: { $regex: optimizedName, $options: 'i' } }
     ]
   ```

3. 记录未匹配的学校
   ```coffeescript
   # 未匹配学校日志记录
   if !matchedSchool
     unmatchedSchools.push({
       name: schoolName,
       optimized_name: optimizedName,
       city: schoolData['City']
     })
   ```

### 3. 数据处理规则

#### 3.1 数据清洗
- 排除平均值行（包含"Average"的记录）
- 移除空值和无效数据
- 处理特殊字符和格式
- 移除#N/A值

#### 3.2 数据转换

1. 年度分数数据处理
   ```coffeescript
   yearlyScores = []
   
   for year in [2018..2024]
     if schoolData["#{year} Score"]?
       score = parseFloat(schoolData["#{year} Score"])
       diff = parseFloat(schoolData["#{year} and Average Difference"])
       rankFull = schoolData["#{year} Ranking"]
       
       # 处理排名和侧边信息
       side = rankFull?.includes('Top') ? 'top' : 'bottom'
       ranking = rankFull?.replace('Top ', '')?.replace('Bottom ', '')
       
       yearlyScores.push({
         year, score, diff, ranking, side
       })
   ```

2. 综合排名数据处理
   ```coffeescript
   rankFull = schoolData['Ranking(2018-2024)']
   summary = {
     times_on_list: parseInt(schoolData['Number of times on the list (2018-2024)']),
     total_score_diff: parseFloat(schoolData['Total Score Difference(2018-2024)']),
     average_diff: parseFloat(schoolData['Average Difference(2018-2024)']),
     overall_ranking: rankFull?.replace('Top ', '')?.replace('Bottom ', ''),
     side: rankFull?.includes('Top') ? 'top' : 'bottom'
   }
   ```

#### 3.3 数据验证
- 检查数据完整性
- 确保必要字段存在且有效
- 验证数值有效性（分数通常为负值）
- 检查统计数据是否存在

### 4. 数据更新操作

```coffeescript
# 更新数据库
updateData = {
  yearlyScores: yearlyScores,
  summary: summary,
  updated_at: new Date()
}

await SchoolRankCol.updateOne(
  { _id: matchedSchool._id },
  { $set: updateData },
  { upsert: true }
)
```

## 数据结构

```json
{
  "_id": "ObjectId", // 学校ID，与sch_rm_merged表中的_id一致
  "yearlyScores": [ // 各年度分数数据
    {
      "year": 2018,
      "score": -12.1, // 原始分数
      "diff": 4.0, // 与平均分数的差异
      "ranking": "17", // 排名(不包含Top或Bottom前缀)
      "side": "top" // 排名方向，top或bottom
    }
    // ... 2019-2024年数据
  ],
  "summary": { // 综合统计数据
    "times_on_list": 7, // 上榜次数
    "total_score_diff": 28.4, // 总分数差异
    "average_diff": 4.057, // 平均分数差异
    "overall_ranking": "23", // 总体排名(不包含Top或Bottom前缀)
    "side": "top" // 排名方向，top或bottom
  },
}
```

## 注意事项

1. 数据更新前需要验证学校匹配的准确性
2. 保留原始数据以便追踪和验证
3. 记录无法匹配的学校名称，以便后续处理
4. 更新操作建议使用批量更新以提高效率
5. 添加错误处理和日志记录机制

## 私立学校处理

### 1. 私立学校名称映射
```coffeescript
PrivateSchoolNameMap = {
  'Tanenbaum Chat Wallenberg Campus':'Tanenbaum Community Hebrew Academy of Toronto',
  'Crescent School':'Crescent School',
  'Hamilton District Christian High School':'Hamilton District Christian High',
  'Columbia International College of Canada':'Columbia International College',
  'The Country Day School':'The Country Day School'
  'Upper Canada College':'Upper Canada College'
  'Woodland Christian High School':'Woodland Christian High School'
  'Niagara Christian Collegiate':'Niagara Christian Collegiate'
  "King's Christian Collegiate":"King's Christian Collegiate"
  'Great Lakes College of Toronto':'Great Lakes College of Toronto - Toronto, The'
  'Southern Ontario Collegiate':'Southern Ontario College'
  'Philopateer Christian College':'Philopateer Christian College'
}
```

### 2. 私立学校匹配流程
1. 首先检查学校名称是否在`PrivateSchoolNameMap`中
2. 如果在映射表中，使用映射后的标准名称查询`private_schools`集合
3. 如果找到匹配的私立学校，使用其ID进行数据更新
4. 如果未找到匹配的私立学校，则继续在公立学校中查找

## 数据验证规则

### 1. 数据有效性检查
```coffeescript
isValidData = (data) ->
  # 基本检查
  return false unless data['Name']
  
  # 特殊处理平均值数据
  if data['Name'].includes('Ontario Secondary School Average')
    return true

  # 检查分数数据
  foundScores = false
  for year in years
    if data["#{year} Score"]?
      foundScores = true
      scoreValue = parseFloat(data["#{year} Score"])
      if isNaN(scoreValue)
        return false
      # 分数通常为负值
      if scoreValue > 0
        debug.warn "警告：学校#{data['Name']}的#{year}年分数为正值: #{scoreValue}"
  
  # 检查综合统计数据
  hasOverallData = false
  if yearRange
    hasOverallData = data["Total Score Difference(#{yearRange})"]? or 
                    data["Average Difference(#{yearRange})"]? or 
                    data["Ranking(#{yearRange})"]?
  
  return foundScores || hasOverallData
```

### 2. 学校名称匹配规则
1. 使用`schoolHelper`函数优化学校名称
2. 支持多种匹配方式：
   - 完全匹配
   - 忽略大小写匹配
   - 优化后名称匹配
3. 优先匹配顺序：
   - 私立学校映射
   - 完全匹配的学校名称
   - findschool来源的学校
   - 其他来源的学校

## 错误处理

### 1. 未匹配学校处理
```coffeescript
unmatchedSchools.push {
  name: schoolData['Name']
  city: schoolData['City']
}
```

### 2. 错误日志记录
- 记录未匹配的学校信息
- 记录数据格式异常
- 记录数据库操作错误
- 记录私立学校查询错误

### 3. 试运行模式
```coffeescript
if dryrun
  debug.info "DRYRUN: 将更新学校 #{school.nm}"
  debug.info "匹配ID: #{school._id}"
  debug.info "数据示例: #{JSON.stringify(yearlyScores[0])}" if yearlyScores.length > 0
  return true
```

## 性能优化

### 1. 批量处理
- 使用`updateOne`而不是`insertMany`来避免重复数据
- 使用`upsert: true`选项自动处理插入和更新

### 2. 数据缓存
- 缓存Ontario Secondary School Average数据
- 缓存年份范围和可用年份信息

## 监控和报告

### 1. 处理统计
```coffeescript
reportMsg = """
处理完成
总耗时: #{processTime.toFixed(2)}秒
处理学校总数: #{processedCount}
成功匹配学校数: #{matchedCount}
未匹配学校数: #{failedCount}
更新数据库记录数: #{totalUpdateCount}
年份范围: #{yearRange}
处理的年份: #{years.join(', ')}
安大略省平均值数据: #{JSON.stringify(ontarioAverages)}
"""
```

### 2. 日志记录
- 记录处理开始和结束时间
- 记录每个学校的处理状态
- 记录异常和警告信息
- 记录未匹配学校列表

