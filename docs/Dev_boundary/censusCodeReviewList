1.import transit
batch/transit/albertaTransitImport （no use)
batch/transit/osmTransitImport (for montreal)
batch/transit/transitRouteImport

2.import boundary
batch/boundary/importBoundary (new compress logic)

2.import census2016
batch/census2016/processKeys
batch/census2016/processNotes
batch/census2016/processCensus

3.movePropRecords
batch/addTagToBoundary.coffee
batch/addTagToProperty.coffee
batch/addTagToBoundary.coffee
/libapp/propertyTagHelper.coffee
/libapp/cmtyTagHelper.coffee
/libapp/schoolHelper
/libapp/censusHelper
/libapp/transitHelper
/prop/propBatchHelper.coffee. common with movePropRecords.

4.refactor
batch/movePropRecords,

split /libapp/mongoTableHelper.coffee
