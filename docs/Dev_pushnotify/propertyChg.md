# Property new or update for pushnotify

## files:
watch batch: /src/batch/prop/watchPropAndPushNotify.coffee
lib: /src/batch/prop/libPushNotifyWhenPropChanged.coffee
pn message: /src/libapp/pushNotifyMsg.coffee

## types
savedsearch, location, building, community, showing, fav
see on App home page feeds tag

## trigger pushnotify
1. prop spcts should be within last three days
2. last his ts of prop should be greater or equeal to spcts
3. user does not turn off pushnotify on notification settings

## saved search matches
Every 5 mins, new or updated properties will be filtered by user's savedsearch
if new or sold property matches user's savedsearch, prop change record will be saved to user_log

## saved search limitation
Save search modal subscribe button will check prop search count, if count > 7000, savedsearch can not be subscribed.

## user watch address/community
User can add or update watch from prop detail page in community/location watch

## DB storage
If updated prop matches user favorites or community/location/building user watching,
prop chagne record will be saved to user_log

## user profile update
Update user profile propLogCnt when new user_log record is added,
if propLogCnt > 300 and user_log pushnotify update records > 300,
delete old 300 user logs


user_log data structure:
{
  "_id" : ObjectId("62d08c7db546659c1d2eda7e"),
  "date" : 20220714,
  "id" : "TRBN5659571",
  "uid" : ObjectId("61731a31a2a36402f06e7006"),
  "_mt" : ISODate("2022-07-14T21:37:01.473Z"),
  "exp" : ISODate("2022-07-29T21:37:01.473Z"),
  "src" : [ 
    {
      "k" : "Saved Search",
      "v" : "2021-10-27"
    },
    {
      "k" : "Saved",
      "v" : "Default"
    },
    {
      "k" : "Community",
      "v" : "Iroquois Ridge North"
    },
  ],
  "status" : "new",
  "tp" : "propChg",
  "ts" : ISODate("2022-07-14T21:37:01.473Z")
}

## develop on local
### run batch 
./start.sh lib/batchBase.coffee batch/prop/watchPropAndPushNotify.coffee preload-model init
### set watch condition on prop
for example: go to prop detail page and set watch on location or community
### update prop on db
set last his ts to future date, set spcts less or equal to ts
### check db or terminal
should see new record on user_log and pushnotify message on terminal or your phone can receive pn if user db has your phone's pn