# 任务批处理推送工作流程

## Tasks 数据库结构

Tasks集合用于存储推送任务的完整信息，每个任务文档包含以下字段：

```json
{
  "_id": ObjectId("..."),          // 唯一标识符
  "priority": 100,                 // 任务优先级，数值越大优先级越高
  "title": "",                     // 任务标题
  "msg": "",                       // 推送内容
  "url": "",                       // 跳转链接
  "from": "",                      // 发送人
  "targetUser": {},                // 目标人群筛选条件
  "ts": ISODate("..."),            // 时间戳，任务创建时间
  "status": "pending",             // 任务状态，可为 pending | processing | done
  "exp": ISODate("..."),           // 过期时间，1年后自动过期
  "pushedStat": {},                // 推送统计信息对象，仅在状态为 done 时设置
}
```

### 字段说明
- **priority**: 调度器根据此字段降序排列，优先处理高优先级任务
- **targetUser**: 包含gid，uids，realtorOnly，groupUnread，isDev 等
- **status**: 任务生命周期状态管理，防止重复处理
- **pushedStat**: 任务完成后记录详细的推送统计信息对象，包含成功/失败数量、设备类型统计等，用于效果分析

## Tasks Model API

系统核心的三个数据库操作函数：

- **`createTask(taskData)`** - 创建新的推送任务，自动设置状态和过期时间
- **`findOneAndUpdateHighestPriority()`** - 查询并更新优先级最高的任务状态，调度器核心函数
- **`markTaskAsDone(taskId, pushedStat)`** - 标记任务完成，记录详细推送统计信息

详细的API参数、返回值和使用示例请参考：[tasksModelAPI.md](tasksModelAPI.md)

## 工作流程图

```mermaid
graph TD
    A["定时器触发<br/>BATCH_INTERVAL_MINUTES"] --> B["查询并更新优先级最高任务状态<br/>findOneAndUpdateHighestPriority()"]
    B --> C{任务是否存在?}
    C -->|是| D["根据targetUser查询用户"]
    C -->|否| A
    D --> E["用户分组"]
    E --> F["iOS用户按locale+pnBadge分组"]
    E --> G["Android用户按locale分组"]
    F --> H["并发推送处理<br/>p-limit控制并发数"]
    G --> H
    H --> I{推送结果}
    I -->|成功| J["推送成功"]
    I -->|失败| K["推送失败"]
    J --> L{设备类型}
    L -->|iOS| M["批量更新iOS用户pnBadge<br/>PushNotifyModel.updateBadgeCount()"]
    L -->|Android| N["记录Android推送成功<br/>无需更新pnBadge"]
    K --> O["记录失败推送凭证<br/>removeFailedPn 存入pn_errors集合"]
    M --> P["任务处理完成"]
    N --> P
    O --> P
    P --> Q["更新任务状态<br/>markTaskAsDone(taskId, pushedStat)"]
    Q --> R["打印任务统计信息<br/>speedMeter.toString()"]
    R --> S["清除统计数据<br/>speedMeter.reset()"]
    S --> A
```




## 1 任务调度器启动

- 任务调度器每 `BATCH_INTERVAL_MINUTES`（如1分钟）触发一次
- 调度器执行 `findOneAndUpdateHighestPriority()` 查询并更新优先级最高的任务状态
- 如果任务存在，将任务传递给**推送工作器**进行推送

## 2 推送工作器执行

### 用户查询与分组
- **用户查询**：根据任务的 `targetUser` 从 `users` 集合中获取目标用户
- **内存分组**：将用户按设备（iOS/Android）和语言区域（locale）分组
  - iOS用户按 `locale` 和 `pnBadge` 分组：`iosGroups: {locale: {pnBadge: []}}`
  - Android用户按 `locale` 分组：`andGroups: {locale: []}`

## 3 并发推送

- 使用 `p-limit` 控制并发推送数量 (`MAX_CONCURRENT_PUSHES`)
- 对每个用户组进行并发推送
- **iOS**：成功推送后，更新对应用户的 `pnBadge`
- **Android**：无需更新 `pnBadge`，但仍进行失败记录

## 4 Badge 更新与错误处理

对成功推送的用户，批量执行 `PushNotifyModel.updateBadgeCount()` 更新其 `pnBadge`。

对失败的推送凭证（例如 token 无效），将其信息存入 `pn_errors` 集合，以便后续处理。

```javascript
/**
 * Badge更新 - 批量更新成功推送的ios用户的徽章数
 */
PushNotifyModel.updateBadgeCount()

/**
 * 错误记录 - 清理失败的推送凭证
 */
PushNotifyModel.removeFailedPn()
```

## 5 任务状态更新与统计

推送完成后：
- 更新任务状态（`markTaskAsDone(taskId, pushedStat)`），同时记录详细推送统计信息到 `pushedStat` 字段
- 打印详细的统计信息，包括：
  - 任务处理耗时
  - 用户查询统计
  - iOS/Android 设备数量统计  
  - 推送成功/失败统计
  - Badge 更新统计
  - 错误处理统计
- 清除统计数据，准备处理下一个任务

## 6 统计信息监控

**实时统计指标包括：**
- `queryUserCount`: 查询到的用户数量
- `iosDeviceCount` / `androidDeviceCount`: iOS/Android 设备数量
- `iosPushSuccess` / `iosPushFailed`: iOS 推送成功/失败数量
- `androidPushSuccess` / `androidPushFailed`: Android 推送成功/失败数量
- `totalPushSuccess` / `totalPushFailed`: 总推送成功/失败数量
- `updatedBadgeCount`: 更新的 badge 数量
- `taskCompleted`: 完成的任务数量
- `workerFailed`: 工作器处理失败数量

每个任务完成后自动打印统计信息并清除，确保每次任务的统计数据独立。

## 7 系统配置

全局参数控制任务调度的间隔、单任务的最大用户数、并发推送数等关键设置。

```javascript
/**
 * 文件顶部设置参数
 */

BATCH_INTERVAL_MINUTES: 5,        // 调度器轮询间隔（分钟）
MAX_PN_PER_TASK: 1000,          // 单任务最大用户数
MAX_CONCURRENT_PUSHES: 20,       // 最大并发推送数
// ...其他配置

```

## 8 系统风险与优化措施

### 待优化风险分析

#### 资源管理风险  
- **内存泄漏隐患**：处理大量用户时，用户分组对象占用大量内存，垃圾回收不及时
- **推送失败无重试**：网络异常或服务临时不可用时，推送失败直接丢弃，缺乏重试机制


### 进一步优化建议

基于系统架构分析，以下五个方面需要关注：

#### 1. 外部服务依赖和降级策略缺失

**现状问题：**
- 完全依赖APNs和FCM外部推送服务，缺乏降级处理
- 第三方服务不可用时整个批处理停止运行
- 没有推送失败的补偿和重试队列机制
- 缺乏对第三方服务限流和配额的监控

**改进建议：**
- 实现熔断器模式，外部服务异常时自动切换到降级策略
- 建立推送失败任务的延迟重试队列，支持指数退避重试
- 监控第三方服务的配额使用情况，提前预警
- 实现多推送服务提供商支持，提高服务可用性

#### 2. 推送频率控制和配额管理

**现状问题：**
- 只控制并发推送数量，未限制推送频率
- 可能触发APNs/FCM的频率限制导致推送失败
- 缺乏用户推送频率保护，可能造成用户骚扰
- 没有推送时间窗口控制，可能在不合适时间推送

**改进建议：**
- 实现推送频率限制（如每分钟最大推送数量）
- 添加用户推送频率保护（如同一用户24小时内最大推送次数）
- 监控推送成功率，动态调整发送策略
- 实现推送时间窗口控制，避免深夜或早晨推送
- 建立推送优先级队列，重要消息优先发送

#### 3. 配置管理和运维监控增强

**现状问题：**
- 系统配置硬编码，修改需要重启服务
- 缺乏动态配置调整能力，无法实时优化性能
- 监控指标不够全面，难以进行问题诊断
- 缺乏完善的告警机制和健康检查

**改进建议：**
- 实现热更新配置机制，支持运行时动态调整参数
- 增加更多关键业务监控指标：
  - 平均任务处理时间和推送成功率
  - 数据库查询耗时和第三方API延迟
  - 内存使用情况和待处理任务队列长度
- 集成APM（应用性能监控）工具进行全链路监控
- 实现智能告警机制，关键指标异常时及时通知运维团队
- 添加健康检查接口，支持负载均衡器和监控系统调用
- 建立完善的日志分级和归档策略

这些改进建议涉及**服务可用性**、**业务合规性**和**运维监控**等多个关键维度，建议根据业务优先级和资源情况逐步实施，以进一步提升系统的健壮性和可维护性。
