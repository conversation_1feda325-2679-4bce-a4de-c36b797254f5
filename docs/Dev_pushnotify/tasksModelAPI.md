# Task Model API 设计文档

## 创建任务

### 函数名称
`createTask`

### 描述
用于创建一个新的推送任务，并保存到数据库中。在函数内部，将 status 字段设置为 'pending'，exp 字段根据当前时间计算，365天后过期，ts 字段为当前时间

### 参数

```javascript
/**
 * 创建推送任务
 * @param {Object} taskData - 任务数据对象
 * @param {string} taskData.title - 任务标题
 * @param {string} taskData.msg - 推送消息内容
 * @param {string} taskData.url - 跳转链接
 * @param {string} taskData.from - 发送人
 * @param {string} taskData.targetUser - 目标用户
 * @param {number} taskData.priority - 任务优先级
 * @returns {error} 成功时无返回值，失败时返回错误信息
 */
createTask({
    title,           // 任务标题
    msg,             // 推送消息
    url,             // 跳转链接
    from,            // 发送人
    targetUser,      // 目标用户
    priority,        // 任务优先级
})
```

### 返回值

```javascript
// 成功创建时：无返回值 (void)

// 创建失败时：返回错误信息
throw error
```

## 查询并更新优先级最高的任务状态

### 函数名称
`findOneAndUpdateHighestPriority`

### 描述
查询并更新优先级最高的任务状态，用于任务调度器获取下一个要执行的任务。

### 参数

```javascript
/**
 * 查询并更新优先级最高的任务状态
 * @returns {Object|null} 优先级最高的任务对象，如果没有任务则返回null
 */
findOneAndUpdateHighestPriority()
```

### 返回值

```javascript
// 成功获取到任务时返回任务对象
{
  "_id": ObjectId("..."),          // 唯一标识符
  "priority": 100,                 // 任务优先级，数值越大优先级越高
  "title": "",                     // 任务标题
  "msg": "",                       // 推送内容
  "url": "",                       // 跳转链接
  "from": "",                      // 发送人
  "targetUser": "all_user",        // 目标人群
  "ts": ISODate("..."),            // 时间戳，任务创建时间
  "status": "processing",          // 任务状态：pending, processing, done
  "exp": ISODate("...")            // 过期时间
}

// 没有任务时返回 null
null

```

## 更新任务状态为完成

### 函数名称
`markTaskAsDone`

### 描述
根据任务ID将任务的 status 字段更新为 'done'，同时记录详细的推送统计信息到 pushedStat 字段

### 参数

```javascript
/**
 * 根据任务ID将任务状态标记为 'done'
 * @param {string|ObjectId} taskId - 要标记为完成的任务ID
 * @param {Object} pushedStat - 推送统计信息对象
 * @param {number} pushedStat.totalPushSuccess - 成功推送的总数
 * @param {number} pushedStat.totalPushFailed - 失败推送的总数
 * @param {number} pushedStat.iosDeviceCount - iOS设备数量
 * @param {number} pushedStat.androidDeviceCount - Android设备数量
 * @param {number} pushedStat.queryUserCount - 查询到的用户数量
 * @returns {void} 成功时无返回值，失败时抛出错误
 */
markTaskAsDone(taskId, pushedStat)
```

### 返回值

```javascript
// 成功时：无返回值 (void)
// 同时会在数据库中更新以下字段：
// - status: 'done'
// - pushedStat: pushedStat（详细推送统计信息对象）

// 失败时：抛出错误信息
throw error
```
