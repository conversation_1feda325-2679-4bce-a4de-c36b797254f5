## Elasticsearch基本概念

### Files
lib: /src/lib/elasticsearch_async.coffee
watch: /src/batch/prop/watchPropAndUpdateElasticSearch.coffee
query: /src/libapp/propElasticSearch.coffee
admin: /src/batch/prop/adminElasticSearch.coffee


### 节点 & 集群（Node & Cluster）
  Elasticsearch 本质上是一个分布式数据库，允许多台服务器协同工作，每台服务器可以运行多个Elasticsearch实例。单个Elasticsearch实例称为一个节点（Node），一组节点构成一个集群（Cluster）。


### Shard ####


### 索引（Index）

  Elasticsearch 数据管理的顶层单位就叫做 Index（索引），相当于关系型数据库里的数据库的概念。另外，每个Index的名字必须是小写。

### 文档（Document）

  Index里面单条的记录称为 Document（文档）。许多条 Document 构成了一个 Index。Document 使用 JSON 格式表示。同一个 Index 里面的 Document，不要求有相同的结构（scheme），但是最好保持相同，这样有利于提高搜索效率。

### 类型（Type）

  Document 可以分组，比如employee这个 Index 里面，可以按部门分组，也可以按职级分组。这种分组就叫做 Type，它是虚拟的逻辑分组，用来过滤 Document，类似关系型数据库中的数据表。
  不同的 Type 应该有相似的结构（Schema），性质完全不同的数据（比如 products 和 logs）应该存成两个 Index，而不是一个 Index 里面的两个 Type（虽然可以做到）。

### 文档元数据（Document metadata）

  文档元数据为_index, _type, _id, 这三者可以唯一表示一个文档，_index表示文档在哪存放，_type表示文档的对象类别，_id为文档的唯一标识。

### 字段（Fields）

  每个Document都类似一个JSON结构，它包含了许多字段，每个字段都有其对应的值，多个字段组成了一个 Document，可以类比关系型数据库数据表中的字段。字段必须是小写或者用_链接


## Elasticsearch入门

Install: https://www.elastic.co/guide/en/elasticsearch/reference/current/targz.html

start server:
cd elasticsearch fold
./bin/elasticsearch

server start at localhost:9200

http://localhost:9200
http://localhost:9200/_cat/indices?v
http://localhost:9200/_mapping

GET _cat/health 查看集群的健康状况
GET _all
PUT 类似于SQL中的增
DELETE 类似于SQL中的删
POST 类似于SQL中的改
GET 类似于SQL中的查

### 更新mapping
https://www.cnblogs.com/createboke/p/********.html

### ElasticSearch从入门到精通,详解
https://www.jianshu.com/p/60b242cbd8b4


### Sample Query:
index name : search

GET /search/_count

GET /search/_doc/TRBN4918780

### Disk-based Shard Allocationedit
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.disk.watermark.low": "100gb",
    "cluster.routing.allocation.disk.watermark.high": "50gb",
    "cluster.routing.allocation.disk.watermark.flood_stage": "10gb",
    "cluster.info.update.interval": "1m"
  }
}

query:

use term for keyword exact match. terms for array match.
match for find in text
bool:
  OR is spelled should
  AND is spelled must
  NOR is spelled should_not

curl -X POST --cacert /home/<USER>/rmcfg/keys/http_ca.crt -u elastic  -H 'Content-Type: application/json' https://*************:9219/properties_alias/_search?pretty -d '
{
  "query": {
    "match": {
      "_id": "TRBN5936805"
    }
  }
}
'

curl -X POST --cacert /home/<USER>/es/config/certs/http_ca.crt -u elastic  -H 'Content-Type: application/json' https://localhost:9218/properties_alias/_search?pretty -d '
{
  "query": {
    "match": {
      "_id": "TRBE626625"
    }
  }
}
'
curl -XGET --cacert /home/<USER>/es/config/certs/http_ca.crt -u elastic  -H 'Content-Type: application/json' https://localhost:9218/properties_5/_mapping?pretty
