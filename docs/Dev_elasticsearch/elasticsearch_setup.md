BranchName: elasticsearch
Author: <PERSON>ame: ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee
BatchDetail: See inside batch file

# Install ES
https://www.elastic.co/guide/en/elasticsearch/reference/current/targz.html

## On Linux
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.1.0-linux-x86_64.tar.gz
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.1.0-linux-x86_64.tar.gz.sha512
shasum -a 512 -c elasticsearch-8.1.0-linux-x86_64.tar.gz.sha512 
tar -xzf elasticsearch-8.1.0-linux-x86_64.tar.gz
cd elasticsearch-8.1.0/

## On Mac
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.1.0-darwin-x86_64.tar.gz
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.1.0-darwin-x86_64.tar.gz.sha512
shasum -a 512 -c elasticsearch-8.1.0-darwin-x86_64.tar.gz.sha512 
tar -xzf elasticsearch-8.1.0-darwin-x86_64.tar.gz
cd elasticsearch-8.1.0/

# Config JVM heap size + Machine Learning
vi elasticsearch-8.1.0/config/jvm.options
+ -Xmx2g #below ####################
+ -Xms2g
+ xpack.ml.enabled: false

in mac: do brew info elasticsearch find the config file path
eg:
Data:    /usr/local/var/lib/elasticsearch/
Logs:    /usr/local/var/log/elasticsearch/elasticsearch_yufengyw.log
Plugins: /usr/local/var/elasticsearch/plugins/
Config:  /usr/local/etc/elasticsearch/

# check compatibility with older ver eg. 8.1.1->8.1.0->7.1.0
# in unittest test, setup another instance of ES(1g), + test server ES(4g)

# Setup username password
  ## after initial run store password
  ##  change passwords using following command when needed
  bin/elasticsearch-reset-password interactive -u elastic
  ## restart Elastic Search
    kill service by pid
    restart:
    in mac:
      brew services restart elasticsearch-full
    Linux:
    cd elasticsearch-8.1.0/
    ./bin/elasticsearch -d
    
  ## set user password:
    cd elasticsearch-8.1.0/
    bin/elasticsearch-setup-passwords interactive
    set password for all serveice in commandline
  
  ## add to base.yml
  elasticSearch:
    host: https://localhost:9200
    user: 'elastic'
    password: '[PASSWORD_FROM_INIT]'
    cert: '[$ES_PATH_CONF]/certs/http_ca.crt'
    <!-- host: http://[username]:[password]@localhost:9200 -->

  ## add to config.coffee if confirm to use ES
  useSearchEngine: 'ES'
  elastic:
    host: '<%this.elasticSearch.host%>'
    user: '<%this.elasticSearch.user%>'
    password: '<%this.elasticSearch.password%>'
    cert: '<%this.elasticSearch.cert%>'
    verbose: 2
  
  ## if need to use mongo
    useSearchEngine: 'Mongo'

  # check es status:
    
    <!-- curl -XGET *************************************/_cluster/health?pretty=true
    curl -XGET http://[username]:[password]@localhost:9200/_cluster/health?pretty=true -->
    curl --cacert ./http_ca.crt -u elastic  https://***********:9219/_cat/indices
    curl --cacert ./http_ca.crt -u elastic  https://***********:9219/properties_2/_stats
 

# start Elastic Search
  cd elasticsearch-8.1.0/
  ./bin/elasticsearch -d


# Install node module
go src
npm install

# sync properties to elastic search
go cfg
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force> ./logs/watches.log 2>&1 &
# check indexes
<!-- curl -XGET http://[username]:[password]@localhost:9200/_cat/indices -->
curl --cacert $ES_PATH_CONF/certs/http_ca.crt -u elastic https://localhost:9200/_cat/indices
show alias
curl -XGET http://[username]:[password]@localhost:9200/_alias
# test elasticsearch
go cfg
./start.sh lib/batchBase.coffee stats/propSearchCompare.coffee showTss testES


## install kibana

### on linux:

wget https://artifacts.elastic.co/downloads/kibana/kibana-8.1.0-linux-x86_64.tar.gz
tar -xzf kibana-8.1.0-linux-x86_64.tar.gz
cd kibana-8.1.0

### on Mac:
wget https://artifacts.elastic.co/downloads/kibana/kibana-8.1.0-darwin-x86_64.tar.gz
tar -xzf kibana-8.1.0-darwin-x86_64.tar.gz

cd kibana-8.1.0

./bin/kibana -d


## ssh tunnel to connect server kibana

ssh -i /Users/<USER>/.ssh/id_rsa appd1@*************** -L 5602:127.0.0.1:5601 -N -v -p 1023


login in to http://localhost:5602/app/home

a key is require here， to generate:
cd elasticsearch-8.1.0/
./bin/elasticsearch-create-enrollment-token --scope kibana


## create readonly user

POST /_security/user/dbReader
{
  "password" : "readOnly$",
  "enabled": true,
  "roles" : [ "viewer" ],
  "full_name" : "dbReader",
  "email" : "<EMAIL>"
}

## note: appd1的elasticsearch 和 kibana 的security没有打开，待添加。