### install ES
see 20210805-elasticsearch_setup.md

### query build module
in elasticsearch.coffee
[] implement advance filter feature for prop search
[] implement school map search (pending)
[] implement transit search (pending)
[] implment stat_uname search (pending)

### sync data to ES
in sync2ElasticSearch.coffee
[] implement watch property change feature
[] implement update mapping feature

### update config.coffee if using ES
[] add useElasticsearch:true to config
[] implment model to use config.useElasticsearch



# 同步数据到ES
## init
1. if redoMapping
  读当前索引name，version，生成新索引名字name_(version+1)
  创建新索引，导入全部数据
  设置alias properties 到当前索引，删除旧的索引和旧的alias
2. if not redoMapping
  导入全部数据到alias为properties的index里。
## routine
read watch token, import data from resume token.