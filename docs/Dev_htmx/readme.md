## desscription
在不同的框架中，需要使用同一个html片段，可以使用 htmx，将公用片段生成一个单独的页面，加载到父页面中

## 使用方法
1. 在需要使用页面中引入 '/libs/htmx-1.7.0.min.js'
2. 在需要加载页面的地方使用以下内容
  <div id="content"
     hx-get="/path/to/your/api" <!-- 指定请求的 URL -->
     hx-trigger="load" <!-- 设置触发事件为页面加载 -->
     hx-target="#content"> <!-- 指定响应内容应该插入到哪个元素中 -->
    <!-- 这里的内容，加载时会被替换 -->
  </div>

## htmx 参数
  ‌hx-get‌：指定请求URL，自动发起GET请求。（hx-post,hx-put同理）
  ‌hx-trigger‌：定义触发请求的事件，如load表示页面加载时触发。（常用的还有click，change，input等）
  ‌hx-target‌：指定响应内容插入的目标元素。（会替换目标元素的内容）
