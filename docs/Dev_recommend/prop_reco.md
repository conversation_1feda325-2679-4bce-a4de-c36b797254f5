### 需求 统计以下字段
1. Push open Rate, EDM open
2. Reco Expo曝光 and click
3. user propDetail stay seconds(需要更新native和判断秒数)
4. User click action record(in propDetail)


### API 定义
## bulk operation
1. push message 
2. EDM mails
使用kafka.sendToTopick
```coffee
set =
  uaddr: prop.uaddr,
  addr: prop.addr,
  unt: prop.unt,
  sid:prop.sid,
  city:prop.city,
  prov: prop.prov,
  cmty: prop.cmty,
  ptype2: prop.ptype2,
  mt: new Date()

await Kafka.produceToTopic {
  topic: gSysNotifyEdmTopic,
  key: prop._id,
  value: JSON.stringify({ _id: prop._id, ...set })
}
```

## user action
前端访问 GET https://stat.realmaster.com/[A00]/[Buid]/[Cprop._id]/[D1].jpg 
A00: 统计类型，见*类型列表
Buid: user._id, mongoid in string
Cprop._id prop._id, prop._id in string, eg. TRBN1312313
D1: 统计值，见*类型表
