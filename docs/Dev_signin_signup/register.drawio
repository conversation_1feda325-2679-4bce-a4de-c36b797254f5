<mxfile host="65bd71144e">
    <diagram id="FjACOe-9B6KTRSx_mtX2" name="第 1 页">
        <mxGraphModel dx="2020" dy="622" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="147" value="&lt;font color=&quot;#ff0000&quot;&gt;&lt;b&gt;&lt;font style=&quot;font-size: 15px;&quot;&gt;vcode操作：&lt;/font&gt;&lt;/b&gt;&lt;br&gt;&amp;nbsp;- 注册时创建记录，24h自动删除&lt;br&gt;&amp;nbsp;- 验证成功删除记录&lt;br&gt;&amp;nbsp;- 第三方注册设置emlV时删除记录&lt;br&gt;&amp;nbsp;- 删除用户时删除记录&lt;/font&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#000000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=west;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=0;pointerEvents=1;fontSize=12;verticalAlign=top;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-190" y="10" width="200" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="167" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-817" y="10" width="610" height="830" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="&lt;b&gt;&lt;font style=&quot;font-size: 20px;&quot; color=&quot;#ffffff&quot;&gt;用户注册流程&lt;/font&gt;&lt;/b&gt;" style="shape=internalStorage;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#FF0000;verticalAlign=top;dx=0;dy=40;align=center;" parent="167" vertex="1">
                    <mxGeometry width="610" height="830" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="用户注册填写邮箱，密码&lt;br&gt;(&lt;span style=&quot;color: rgb(106, 153, 85); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;1.5/user/register)&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="55" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="前端验证邮箱有效?" style="rhombus;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="155" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" parent="167" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontColor=#FFFFFF;rounded=0;" parent="167" source="5" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="247" y="235" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="472" y="235"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="邮箱自动纠错" style="rounded=0;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="397" y="155" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;记录ip与email&lt;br&gt;user_register_ip&lt;/font&gt;&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=8;" parent="167" vertex="1">
                    <mxGeometry x="157" y="255" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="用户存在?" style="rhombus;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="355" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontColor=#FFFFFF;" parent="167" source="8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="237" y="730" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="577" y="380"/>
                            <mxPoint x="577" y="730"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="提示已经注册过，前去登录" style="rounded=0;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="397" y="355" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="hardBounce/complaint？" style="rhombus;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="455" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontColor=#FFFFFF;" parent="167" source="11" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="237" y="730" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="577" y="480"/>
                            <mxPoint x="577" y="730"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="提示邮箱被bounce，建议修改邮箱或联系RealMaster" style="rounded=0;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="397" y="455" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="创建用户，发送验证邮件" style="rounded=0;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="653" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="返回注册结果" style="rounded=1;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="753" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="19" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" parent="167" source="6" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="167" source="3" target="6" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="277" y="245" as="sourcePoint"/>
                        <mxPoint x="377" y="245" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="17" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="167" source="7" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="277" y="425" as="sourcePoint"/>
                        <mxPoint x="377" y="425" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="20" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="167" source="3" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="347" y="225" as="sourcePoint"/>
                        <mxPoint x="357" y="275" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="25" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="167" source="7" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="327" y="355" as="sourcePoint"/>
                        <mxPoint x="417" y="355" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="28" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" parent="167" source="12" target="15" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="167" source="10" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="317" y="455" as="sourcePoint"/>
                        <mxPoint x="407" y="455" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="30" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="&lt;font color=&quot;#ff0000&quot;&gt;验证信息存在vcode,&lt;br&gt;用户24h内未验证邮箱，会自动清除(vcode,user,login)&lt;/font&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#000000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=south;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=20;pointerEvents=1;align=left;verticalAlign=top;" parent="167" vertex="1">
                    <mxGeometry x="27" y="603" width="150" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="158" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="167" source="10" target="162" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="262" y="520" as="sourcePoint"/>
                        <mxPoint x="307" y="545" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="159" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="158" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="160" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="167" source="162" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="327" y="603" as="sourcePoint"/>
                        <mxPoint x="372" y="628" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="161" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="160" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="162" value="proxy email?" style="rhombus;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="157" y="553" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="163" value="提示邮箱不可用，请更换邮箱" style="rounded=0;whiteSpace=wrap;html=1;" parent="167" vertex="1">
                    <mxGeometry x="397" y="550" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="164" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="167" source="162" target="163" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="337" y="540" as="sourcePoint"/>
                        <mxPoint x="427" y="540" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="165" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="164" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="166" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontColor=#FFFFFF;" parent="167" source="163" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="237" y="730" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="577" y="575"/>
                            <mxPoint x="577" y="730"/>
                        </Array>
                        <mxPoint x="547" y="530" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="185" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="20" y="10" width="560" height="730" as="geometry"/>
                </mxCell>
                <mxCell id="77" value="&lt;font style=&quot;font-size: 20px;&quot;&gt;&lt;b&gt;重新发送验证邮件流程&lt;/b&gt;&lt;/font&gt;" style="shape=internalStorage;whiteSpace=wrap;html=1;backgroundOutline=1;fontSize=12;fontColor=#FFFFFF;dx=0;dy=40;verticalAlign=top;" parent="185" vertex="1">
                    <mxGeometry width="560" height="730" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="重新发送验证邮件&lt;br&gt;(&lt;span style=&quot;color: rgb(106, 153, 85); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;1.5/user/resendEmail)&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="185" vertex="1">
                    <mxGeometry x="50" y="60" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="hardBounce/complaint？" style="rhombus;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="50" y="245" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="59" value="可以发送邮件?" style="rhombus;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="50" y="348" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="发送验证邮件,提示用户进行验证" style="rounded=0;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="50" y="459" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="返回结果" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="185" vertex="1">
                    <mxGeometry x="50" y="565.5" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="185" source="62" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="140" y="539" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="530" y="270"/>
                            <mxPoint x="530" y="539"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="提示邮箱被bounce，建议修改邮箱或联系RealMaster" style="rounded=0;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="310" y="245" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="185" source="63" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="140" y="539" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="530" y="373"/>
                            <mxPoint x="530" y="539"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="提示发送频繁，15min后可以再次发送" style="rounded=0;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="310" y="348" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="66" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="185" source="57" target="59" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="90" y="449" as="sourcePoint"/>
                        <mxPoint x="190" y="449" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="67" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="66" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="65" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="185" source="60" target="61" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="185" source="59" target="60" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="90" y="449" as="sourcePoint"/>
                        <mxPoint x="190" y="449" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="68" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="185" source="57" target="62" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="220" y="275" as="sourcePoint"/>
                        <mxPoint x="220" y="305" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="71" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="70" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="185" source="59" target="63" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="230" y="338" as="sourcePoint"/>
                        <mxPoint x="230" y="408" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="73" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="72" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="148" value="用户存在?" style="rhombus;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="50" y="150" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="149" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="185" source="56" target="148" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="150" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="185" source="148" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="280" y="190" as="sourcePoint"/>
                        <mxPoint x="280" y="251" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="151" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="150" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="156" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;rounded=0;" parent="185" source="152" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="140" y="540" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="530" y="175"/>
                            <mxPoint x="530" y="540"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="152" value="提示用户不存在" style="rounded=0;whiteSpace=wrap;html=1;" parent="185" vertex="1">
                    <mxGeometry x="310" y="150" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="153" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="185" source="148" target="152" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="270" y="130" as="sourcePoint"/>
                        <mxPoint x="350" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="154" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="153" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="246" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-817" y="853" width="1627" height="897" as="geometry"/>
                </mxCell>
                <mxCell id="140" value="第三方注册流程" style="shape=internalStorage;whiteSpace=wrap;html=1;backgroundOutline=1;fontSize=20;fontColor=#FFFFFF;dx=0;dy=40;verticalAlign=top;fontStyle=1" parent="246" vertex="1">
                    <mxGeometry width="1627" height="897" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="第三方注册" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="246" vertex="1">
                    <mxGeometry x="230" y="69" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="81" value="微信注册?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="230" y="159" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="98" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="246" source="80" target="81" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="82" value="第三方返回信息有邮箱?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="230" y="249" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="83" value="存在邮箱用户?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="230" y="339" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="邮箱已验证?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="230" y="439" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="85" value="设置emlV=now()/eml,&lt;br&gt;删除vcode记录，&lt;br&gt;绑定第三方ID，&lt;br&gt;重置随机密码" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="20" y="427.5" width="150" height="73" as="geometry"/>
                </mxCell>
                <mxCell id="87" value="登录成功" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="246" vertex="1">
                    <mxGeometry x="230" y="831" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="88" value="创建账号，绑定第三方id，生成随机密码" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="632" y="533" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="89" value="发送携带账号密码信息的注册成功邮件" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="632" y="623" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="237" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;" parent="246" source="90" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="307" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="707" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="90" value="跳转绑定成功通知页面&lt;br&gt;(&lt;span style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;&lt;font color=&quot;#bbe77e&quot;&gt;/1.5/user/oauth?d=/1.5/user/login&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="582" y="713" width="250" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="93" value="session中保存用户信息&lt;br&gt;跳转绑定邮箱页面（已有账号/新注册账号）&lt;br&gt;&lt;span style=&quot;color: rgb(187, 231, 126); background-color: rgb(32, 38, 49); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;（apple/google/fackbook:&lt;/span&gt;&lt;span style=&quot;color: rgb(187, 231, 126); background-color: rgb(32, 38, 49); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;/1.5/user/bindThirdParty?uTp=type)&lt;br&gt;&lt;/span&gt;&lt;div style=&quot;color: rgb(217, 215, 206); background-color: rgb(32, 38, 49); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: rgb(187, 231, 126);&quot;&gt;(wechat: /1.5/user/bind/&lt;/span&gt;&lt;span style=&quot;color: rgb(240, 112, 118);&quot;&gt;wxuser.unionid&lt;/span&gt;&lt;span style=&quot;color: rgb(187, 231, 126);&quot;&gt;)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="849" y="523" width="440" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="94" value="已有账号?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="994" y="618" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="238" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;" parent="246" source="95" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="307" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="944" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="95" value="登录，绑定第三方id" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="869" y="703" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="239" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;" parent="246" source="96" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="307" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1184" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="96" value="注册，绑定第三方id" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="1109" y="703" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="102" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="81" target="82" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="399" as="sourcePoint"/>
                        <mxPoint x="350" y="399" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="103" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="102" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="104" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="82" target="83" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="399" as="sourcePoint"/>
                        <mxPoint x="350" y="399" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="105" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="104" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="99" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="246" source="88" target="89" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="106" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="83" target="84" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="593" as="sourcePoint"/>
                        <mxPoint x="350" y="593" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="107" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="106" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="100" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="246" source="89" target="90" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="110" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="246" source="84" target="85" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="593" as="sourcePoint"/>
                        <mxPoint x="210" y="613" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="111" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="110" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="139" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="246" source="112" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="300" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="95" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="112" value="发送携带账号密码信息的注册成功邮件" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="20" y="637" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="101" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="246" source="93" target="94" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="122" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="246" source="94" target="95" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="899" y="613" as="sourcePoint"/>
                        <mxPoint x="999" y="613" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="944" y="643"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="123" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="122" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="113" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="246" source="85" target="112" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="124" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="246" source="94" target="96" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="919" y="633" as="sourcePoint"/>
                        <mxPoint x="999" y="613" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1184" y="643"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="125" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="124" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint y="-10" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="172" value="存在第三方id用户?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="450" y="339" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="174" value="proxy email" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="632" y="441" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="176" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="174" target="88" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="819" y="491" as="sourcePoint"/>
                        <mxPoint x="819" y="543" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="177" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="176" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="178" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="246" source="83" target="172" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="367" y="421" as="sourcePoint"/>
                        <mxPoint x="367" y="473" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="179" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="178" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="126" value="邮箱已验证?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="1413" y="491" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="240" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;" parent="246" source="131" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="307" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1488" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="131" value="跳转验证邮箱页面&amp;nbsp;&lt;br&gt;重新发送验证码，验证邮箱 (&lt;span style=&quot;color: rgb(187, 231, 126); background-color: rgb(32, 38, 49); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;/1.5/user/success?eml= user.eml&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="1365" y="703" width="246" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="199" value="存在第三方id用户?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="994" y="249" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="207" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="246" source="126" target="131" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1569" y="591" as="sourcePoint"/>
                        <mxPoint x="1569" y="633" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="208" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="207" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="211" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="81" target="199" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="467" y="131" as="sourcePoint"/>
                        <mxPoint x="537" y="131" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1069" y="181"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="212" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="211" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="217" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="172" target="174" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="657" y="341" as="sourcePoint"/>
                        <mxPoint x="727" y="341" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="707" y="364"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="218" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="217" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="15" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="219" value="邮箱已验证?" style="rhombus;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="450" y="441" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="220" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="172" target="219" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="437" y="411" as="sourcePoint"/>
                        <mxPoint x="507" y="411" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="221" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="220" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="236" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;" parent="246" source="222" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="307" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="525" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="222" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;跳转验证邮箱页面,重新发送验证码，验证邮箱 &lt;br&gt;(&lt;span style=&quot;color: rgb(187, 231, 126); background-color: rgb(32, 38, 49); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;/1.5/user/success?eml= user.eml&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;)&lt;/span&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="246" vertex="1">
                    <mxGeometry x="455.75" y="551" width="138.5" height="63" as="geometry"/>
                </mxCell>
                <mxCell id="223" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="219" target="222" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="517" y="511" as="sourcePoint"/>
                        <mxPoint x="517" y="563" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="224" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="223" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="225" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="199" target="93" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1087" y="331" as="sourcePoint"/>
                        <mxPoint x="1087" y="373" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="226" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="225" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="227" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="246" source="174" target="93" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="787" y="461" as="sourcePoint"/>
                        <mxPoint x="807" y="483" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1069" y="466"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="228" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="227" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="229" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="246" source="199" target="126" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1247" y="291" as="sourcePoint"/>
                        <mxPoint x="1247" y="333" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1487" y="274"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="230" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="229" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="233" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="246" source="219" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="387" y="501" as="sourcePoint"/>
                        <mxPoint x="307" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="427" y="466"/>
                            <mxPoint x="427" y="631"/>
                            <mxPoint x="427" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="234" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="233" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="241" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="246" source="126" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1360" y="511" as="sourcePoint"/>
                        <mxPoint x="306" y="803" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1337" y="516"/>
                            <mxPoint x="1337" y="676"/>
                            <mxPoint x="1337" y="803"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="242" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="241" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="334" y="-287" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="245" value="prox email domain由人工维护在数据库中。&lt;br&gt;collection:data.sysdata&lt;br&gt;_id:proxyEmail" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#FF0000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=west;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=2;pointerEvents=1;fontSize=12;verticalAlign=top;align=left;" parent="246" vertex="1">
                    <mxGeometry x="729" y="377" width="178" height="72" as="geometry"/>
                </mxCell>
                <mxCell id="247" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="246" source="82" target="199">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="657" y="397" as="sourcePoint"/>
                        <mxPoint x="757" y="397" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="248" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="247">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="251" value="已绑定第三方id?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="246">
                    <mxGeometry x="230" y="543" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="258" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="246" source="252" target="87">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="252" value="绑定第三方id，&lt;br&gt;发送邮件" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="246">
                    <mxGeometry x="230" y="663" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="254" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="246" source="251" target="252">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="517" y="647" as="sourcePoint"/>
                        <mxPoint x="617" y="647" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="255" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="254">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="256" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;rounded=0;" edge="1" parent="246" source="251" target="87">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="517" y="647" as="sourcePoint"/>
                        <mxPoint x="617" y="647" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="187" y="568"/>
                            <mxPoint x="187" y="757"/>
                            <mxPoint x="305" y="757"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="257" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="256">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint y="-169" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="259" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="246" source="84" target="251">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="517" y="647" as="sourcePoint"/>
                        <mxPoint x="617" y="647" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="260" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="259">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>