<mxfile host="65bd71144e">
    <diagram id="JiHZkGI8N7gEgn1AWwOl" name="Page-1">
        <mxGraphModel dx="905" dy="-638" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="50" value="" style="edgeStyle=none;html=1;" parent="1" source="48" target="49" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="添加uuid" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="440" y="1320" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="" style="edgeStyle=none;html=1;exitX=0.165;exitY=0.988;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="49" target="60" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360" y="1450" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="" style="edgeStyle=none;html=1;exitX=0.833;exitY=1.088;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="49" target="61" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="560" y="1450" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="在session中生成并添加uuid&lt;br&gt;同时保存至user_uuid 表中" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="1400" width="480" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="75" value="" style="edgeStyle=none;html=1;entryX=0.82;entryY=-0.1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="54" target="56" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="680" y="1730" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="54" value="删除当前session中的uuid字段&lt;br&gt;(登出之后会redirect到首页，会生成新的uuid)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="1560" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="使用uuid统计用户操作" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="1960" width="440" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="67" value="" style="edgeStyle=none;html=1;" parent="1" source="58" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360" y="1760" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="70" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="67" vertex="1" connectable="0">
                    <mxGeometry x="-0.275" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="" style="edgeStyle=none;html=1;entryX=1;entryY=0;entryDx=0;entryDy=0;" parent="1" source="58" target="68" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="232" y="1680" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="58" value="登录的user是否存在uuid" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="300" y="1560" width="120" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="" style="edgeStyle=none;html=1;" parent="1" source="60" target="58" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="登录" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="1480" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="" style="edgeStyle=none;html=1;" parent="1" source="61" target="54" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="登出" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="640" y="1480" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="" style="edgeStyle=none;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="1" source="68" target="71" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="更新user表的uuid字段" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="40" y="1680" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="73" value="" style="edgeStyle=none;html=1;entryX=0.091;entryY=-0.125;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="71" target="56" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="71" value="更新user表的b4luuid字段&lt;br&gt;user_uuid表的uid字段&lt;br&gt;&lt;br&gt;如果user表中已经存在uuid则更新cookie中的uuid为表里保存的值" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="240" y="1760" width="240" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="76" value="uuid为后端生成的12位的nanoid，保存至cookie，过期时间3个月" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="40" y="1205" width="360" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>