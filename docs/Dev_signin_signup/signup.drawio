<mxfile host="65bd71144e">
    <diagram id="qF0EEgk_MAy0ZbNFC0cz" name="Page-1">
        <mxGraphModel dx="978" dy="535" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="12" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="2" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="new &lt;br&gt;Visitor User register" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="330" y="40" width="110" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="Yes" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="No" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="5" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="Success" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="345" y="140" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="edgeStyle=none;html=1;" parent="1" source="6" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Send Verify Email/Code" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="440" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="edgeStyle=none;html=1;" parent="1" source="7" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="7" target="16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="User click verify Email/Enter Vertify Code" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="440" y="390" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="go to App Login" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="440" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="Display Error Message" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="16" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="Click Verify Email" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="640" y="390" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="20" style="edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="18" target="9">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="20">
                    <mxGeometry x="-0.1816" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="No" style="edgeStyle=none;html=1;" edge="1" parent="1" source="18" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="Open In App" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="660" y="520" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="go To Website" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="810" y="590" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>