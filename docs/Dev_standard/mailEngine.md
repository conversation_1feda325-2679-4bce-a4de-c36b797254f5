# Mail Engine

## RM Mail API
GET /list/[email]
Get list of emails summary information for the address.
_id,from,to,subject

GET /last/[email]
Get the detail of last email for the address.

GET /detail/[id]
Get the detail of specified _id

POST /send
body: {
  "from": "<EMAIL>",
  "to": "<EMAIL>,<EMAIL>",
  "subject": "第一次发邮件",
  "text": "再試一下這回可以不"
}
create email with parameter

email接受API设置了。写unit test可以用。比如第一个API，可以这样用 https://ml1.realmaster.cc/list/<EMAIL>

只支持 realmaster.cc 域名，用这个检查接受的内容。

#SES
coming soon