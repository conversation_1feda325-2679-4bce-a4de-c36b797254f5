# Review Procedure

## Preconditions:(如果是大型PR必须有设计文档和配套测试)

1. < 4 weeks code; (otherwise hard to review) 2020-08-18
2. try keep each 300 > PR > 50 lines (no consecutive logic) unreviewable, except hot fix;  block func preferred, review before push
3. nothing personal改进代码为目的

## review steps:

### [技术] syntax(including potential performance issue);

- understandability(i,j,j_i,fn->tranlate) / architecture(MVC)
- follow standard/guidelines(format camel case etc...view naming rule)
- DRY/existing modules, no webpack .vue->.js
- unit test/debugbility/edge case (potencial bug)(if/else)
- func too big?
- comments/descs/documents?  debug code? ./start dryRun
- memory leak/storage related? lazy-loading/caching/for loop re init? for(i in [0..5]){JSON.parse(localStorage.a)}
- global vars/cfgs(DRY)?

- DEF deprecated , propdtail , list, DEF list , TODO: deprecated
- async & cb DEF, refactor ->lib db->model
- documentation, header+function+(handle/process)+param+NOTE:+ TODO: https://developer.aliyun.com/article/379430
- naming. CONS_GLOBAL_VAL, gCounterValue++,
- Git Commit message: gitCommitFormat.md type(scope):
- db 相关的修改要考虑 现有的 数据和index, business logic也收到影响
- 翻译应该最后一步执行 
- 命名 handleProps -> translateAndAddFavToProps



###  [完备]Integrity Error/crash handling
- 是否有不需要的console.log/debug.info, 以及debug.info/error/log 的使用
- input verify/3rd party(user/hacker input) null, parseInt->NaN try JSON.parse
- no major db issue -> (ret,err)-> for loop db.updateOne, cb()  
- ! no major security issue* -> /oauth -> verify user  string->nonce+token(2step verification), email unsubscribe//sql injection
- （第三方调用 或 者填表 或 fuza地图检索）要limit access rate 100b*1kw vs 100k*picReg*1kw
- run on server no crash; (after merge del/cfg/npm read undefined or null)

### *安全性参考以下列表：
1. 用户输入进入数据库（sql injection），是否前端展示（xss）
2. hardcode key/token, expose to frontend, config key useage domain/ip
3. API 权限管理，能否通过接口调用户数据/图片，包括内部接口 eg. geoService/proxy
4. API 调用次数管理，内部调用是否限制了ip, 次数,API 是否过于predictable 
4. css/js 甚至class name加密压缩等处理
5. (需要lib session处理) Authentication hack, 撞库， session hijacking
6. 3rd party libs loophole, eg. java.journel.logger
7. unvalidate redires, /oauth?redirect=1&code=1&eml=<EMAIL>
8. API 是否有不用的，内部的、敏感数据给到了前端
9. webroot/public(nginx) 是否有隐私文件


###  [功能]functional, related to business model;
- share (encode&decore) should consider already shared links & support
- @allen/@fred test
- batch test
- page not respond
- layout/btn  not included

### 完成本地测试和unit test
- 包括cfg的修改是否上传
- unit test 能否pass
