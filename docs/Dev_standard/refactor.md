### Dev:(don't trust user input)
# TODO: after [julie_dev(import)]
0. no req in model , req -> business; isAllowed -> can
1. all API use get/post, no put/del for compatibility(unless wordpress)
2. 普通用户req user login? userInfo.json for after share userinfo,  url:/detail?id=123&uid=1234; 传uid导致用户数据泄露,检查roles(realtor);
3. unify json return value {success:true, error:err} -> {ok:0,err:err,needlogin:1}
4. use 'err'->MSG_STRINGS, fix warning, remove unused
5. appAuth or return callback functions
6. use debug.error
7. hide unnecessary detail in JSON.
8. if block code has format issues, ignore
9. DEV: post-> redirect? or json? https://restfulapi.net/resource-naming/ -> use stdFunction

Refactor:
0. db not always exists;
1. db selection fields, used only
2. global/local/function naming/rules
3. minify js
4. mapbox pk accessToken
5. feature grouping: WebRM
6. coffeescript2:
    1. null <> undefined,
    2. https://coffeescript.org/#breaking-changes-default-values
    3. JSX and the < and > operators
    4. Add type checking when easy. https://coffeescript.org/#type-annotations
7. :ctx: , getPageContent
8. native callback(err,val) postCallback
9. 长期不用的log要定期删除（errorLog/edmLog/pn_error/mls_crea_ddf_available/mls_treb_availables）
10. Url-vars.js 放到了app.js里。
11. properties里school相关的字段，现在存bnds和schs，可以和其他字段统一，bndCity, bndSch
12.  Uaddr， picurl，前后台重复。时间，货币format，libapp下做一个common file，brewcoffee 打包放到public/js
13. Import 房源，set available ids , 需要两次操作， 将所有available的id status = A, query  {status:’A’, ids: {$nin: ids} , set status =U
14. school related bnds/schs code
15. fields => project
16. Formalize View=>Controller=>Business Logic=>Data Model model. Put Data Model layer to separate js file, and basically One-Model-One-Collection. Read/Write/Update, field format check/array retrieve...

TODO: mongoError in l10n

1.搞不清楚的代码, 问一下原来的开发者, 不要去猜; 
2. 搞清楚了后, 加注释; 
3. 写代码时加注释, 不要让别人去猜; 
4. 不用的代码去掉, 用git管理,不要用comment掉的原始方法. 
5. git diff before commit; 
6. 尽量写unit test, cover 修改的code; 
7. cover不了的部分, 每一行要看一下,是否写的对, 记录没有cover的行数到on line step 里面. 
看到了,回我一下, 知道了 @all