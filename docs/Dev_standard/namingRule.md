#### general:
google js: https://google.github.io/styleguide/jsguide.html#naming-rules-common-to-all-identifiers
speeling: https://developers.google.com/style/spelling
wording: https://developers.google.com/style/word-list
naming: https://developer.apple.com/library/archive/documentation/Cocoa/Conceptual/CodingGuidelines/Articles/NamingBasics.html
js: http://www.sourceformat.com/pdf/javascript-coding-standard-apple.pdf
api: https://cloud.google.com/apis/design/naming_convention
## function:

REQUIRES: function desc(what when where), input/output
NOTE: camelcase, not short name
getXXX: returns value, a = getXXX(b)
hasSth->bool: hasRole/hasChinese
canDoSth->bool: canEditOpenHouse
(gen[includes multi computations]/set[])XXX: may have no return value, modifies param; genXXX(a), a.xxx=someValue

## mongodb:

collection: use _, eg. listing_data, user_name, underscroe_table_name
collection in coffee: `CollUser = COLLECTION 'vow','user'`
fields: camelcase (NOW: _ are from MLS, new ones use camelcase <- SQL)
NOTE: use short name, remove /aeiou/

## mysql:

NOTE: not support camelcase, use _
EG: (NOW: ddfId -> ddf_id,   agent._id -> agent_iid,  agent.id -> agent_id, la_iid)
    (FUTURE: agent._id -> agent__id)

## variable:

NOTE: camelcase, fullname
isNoun: bool, isAdmin/isRealtor/isZh;
global: uppercase with underscore GLOBAL_CONSTANT_VALUE , gTotalCount += 1;
inside function: can use short name;
(DATA_TYPE/SCOPE)_(ROLE/DIFF)_(EVENT/)
LIMIT,MSG(should use MSG_STRINGS),
eg.

```coffeescript
gLobalVarThatNeedChange = setInterval;

CONST_GLOBAL_VAR = 1000;
LIMIT_VIP_PRED_SHOWS = 100;
LIMIT_USER_PRED_SHOWS = 50;
LIMIT_USER_FAV_GROUP_COUNT = 10;
LIMIT_VIP_FAV_GROUP_COUNT = 20;


DataBaseColl = COLLECTION 'vow','db';
```

## filename:

theme/output file: use _, lowercase
coffe source code: camelcase
prefix source code: use _ -> 00_dotLoader.coffee

## CSS:

reference: http://bdavidxyz.com/blog/how-to-name-css-classes/