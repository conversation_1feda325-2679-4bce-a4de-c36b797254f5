#css:
###
what is CSS?
integrate CSS?

example:
left, right
20px, rest
.left{
  20px;
}
!important
flex layout
white-space: nowrap;
overflow:hidden;
margin vs paddign;
###

# python  js css java


# bad code:
# 1 naming
# 2 (for)access localStorage and JSON.parse


Is_Blocked = (posts)->
  for p in posts
    for c in p.comments
      blockUids = JSON.parse(localStorage.blkUids)
      if (c.uid in blockUids)
        c.blckd = true
        c.msg = 'post '+p.id+' is blocked by'+c.uid



