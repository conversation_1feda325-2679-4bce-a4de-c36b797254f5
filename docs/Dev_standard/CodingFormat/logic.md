## logic
1. check input, return if wrong.
2. return condition that met first.

eg.
```javascript
function toAccounting(n) {
  if (n < 0) {
    return '(' + Math.abs(n) + ')'
  } else {
    return n.toString()
  }
}
```
```coffeescript
numberToAccountingString = (number)->
  return if not number
  if number < 0
    return `(${Math.abs(number)})`
    return "(#{Math.abs(number)})"
  return number.toString()
  return ''+number
```