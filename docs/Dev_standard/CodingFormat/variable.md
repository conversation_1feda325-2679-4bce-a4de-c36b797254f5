## variable manipulation
1. use full name instead of t/q/i/j
2. use global vars
3. use es6 default and deconstruction

eg.
```coffeescript
const TAX_RATE = 1.13
SHIPING_DEFAULT = 5
calculateTotal = (items = [],{shipping = SHIPING_DEFAULT, discount = 0} = {})->
  return 0 if not items.length
  itemCost = items.reduce( (total,item)->
    return total + item.price * item.quantity
  ,0)
  discountRate = 1 - discount
  return itemCost * discountRate * TAX_RATE + shipping
```