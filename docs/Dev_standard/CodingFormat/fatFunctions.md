#fat functions
1. each func do one job
2. DRY
3. simple function name and return (validateUser -> {valid:boolean, errors:[]})
4. API


bad example:
```coffeescript
saveUser = (user)->
  errors = []
  if user.username
    if user.username.length < 3
      errors.push ' > 3 chats'
  else
    errors.push 'username required'
  if user.password
    if user.password.length < 8
      errors.push '>8 pwds'
  else
    errors.push 'password required'
  if errors.length
    errors.forEach (err)-> console.error err
    return
  if not user.id
    console.log 'User created'
    createUser user
  else
    console.log 'User updated'
    update user
```

good example:
file1 lib.validator.js
```javascript
function validationMessages(validations, object) {
  return Object.entries(validations).reduce((errors, [property, requirements]) => {
    errors[property] = []
    if (requirements.required) {
      const errorMessage = validateRequiredMessage(object[property])
      if (errorMessage) errors[property].push(errorMessage)
    }

    if (requirements.length) {
      const errorMessage = validateLengthMessage(object[property], requirements.length)
      if (errorMessage) errors[property].push(errorMessage)
    }

    return errors
  }, {})
}

function validateLengthMessage(value, length) {
  if (value == null) return
  if (value.length >= length) return

  return `must be ${length} or more characters`
}

function validateRequiredMessage(value) {
  if (value) return

  return 'is required'
}

function printErrors(errors) {
  Object.entries(errors).forEach(([property, messages]) => {
    messages.forEach(message => {
      console.error(`${property} ${message}`)
    })
  })
}

module.exports = {
  validationMessages,
  printErrors
}

```

file2 userBusinessLogic.coffee
```coffeescript
validateUser = (user)->
  validations = {
    username:{
      required:true
      length:3
    }
    password:{
      required:true
      length:8
    }
  }
  errors = validationMessages validations,user
  return {
    valid:Object.values(errors).every((msg)->msg.length is 0),
    errors
  }
```