## async,cb
1. avoid callback hell

bad eg.
```coffeescript
readline = require 'readline'
readlineInterface = readline.createInterface {
  input: process.stdin
  output: process.stdout
}
readlineInterface.question 'what is ur name?',(name)->
  readlineInterface.question 'what is ur job ?',(job)->
    readlineInterface.question 'what is ur age?',(age)->
      console.log "#{name},#{job}"
      readlineInterface.close()
```

## good example1.

file1 business.coffee
```coffeescript
askQuestion = require './lib/lib.coffee'
async main = ()->
  name = await askQuestion('what is ur name?')
  job  = await askQuestion('what is ur job ?')
  age  = await askQuestion('what is ur age?')
  debug.info "#{name},#{job}"
main()
```

file2 lib.coffee
```coffeescript
readline = require('readline')
askQuestion = (question) ->
  readlineInterface = readline.createInterface {
    input: process.stdin
    output: process.stdout
  }
  return new Promise (resolve, reject) ->
   readlineInterface.question question, (answer)->
      resolve answer
      readlineInterface.close()
module.exports = askQuestion
```

## example2 using jobs
```coffeescript
main = ()->
  jobs = [
    'what is ur name?'
    'what2'
    'what3'
  ]
  answers = []
  doOne = (cb)->
    job = jobs.pop()
    return cb null unless job
    readlineInterface.question job,(answer)->
      answers.push answer
      doOne()
  doOne ()->
    debug.info answers
```