# 微信分享
在 index.coffe layout 里添加wechat.js

else if @req.isWeChat()
  js '/js/wx/jweixin-1.3.2.js'
  if wxcfg = @wxcfg or @vctx?.wxcfg
    script -> "var wxConfig = #{JSON.stringify wxcfg};"
  js '/js/wechatsrv.min.js'

测试服上微信二次分享有问题，配置问题。
服务器端：
getWXConfig req,req.fullUrl(),(err,wxcfg)->
  wxcfg 需要加到layout里。

2. 使用时在需要分享的页面，以及分享之后的页面里都要加
span#share-title-en
span#share-desc-en
span#share-title
span#share-desc
span#share-image

如果需要给weichat share使用不一样的内容
span#wx-title-en
span#wx-desc-en
span#wx-title
span#wx-desc
span#wx-image

wechat share 优先读wx- tag的内容，

wechat.js
