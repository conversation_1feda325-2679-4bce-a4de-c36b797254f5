text """ """
三个引号里是支持换行，不需要换行的地方用"" 既可以。

for loop:
  STATIC_VALS:static value init first
  init STATIC_VALS:if func return static value, dont for loop in func
  consider use {},obj[key] has higher efficiency

use "if" must have "else"
#not good practise:
  if a:
    doSth(
      cb()
    )
  else
    cb()

# change to ->
  return cb() if not a
  doSth(
    cb()
  )

# bad
  if a or b #edge case
    return cb()
  doSth()

#
{ connectionString, formatter }  = configObject
=>
var connectionString;
  ({connectionString, formatter} = configObject);

字符串和数字不能比较，永远都是false。做数字比较之前要parseInt
'b'>4
false
'b'<4
false
'b'==4
false
'b'=='4'
false


es6:
filterBanners = (req, user, l=[], vcinc)->
does not work for l=null, see https://stackoverflow.com/questions/32725034/passing-in-null-as-a-parameter-in-es6-does-not-use-the-default-parameter-when-on/32725086
change to ->  l ?= []


coffee2

is not !== isnt
b = ''
a is not b -> a === !b
a isnt b -> a !== b