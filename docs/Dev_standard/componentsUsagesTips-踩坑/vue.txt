0. 新开发用vue3， 但是别用emit, 别用jade/pug 作为template engine(https://pughtml.com/), 别用webpack， 用dot engine
1. vue javascript 里翻译需要先在页面上定义调用过一次翻译，在缓存里存在以后在使用。
div(style="display:none")
  span(v-for="(v,k) of strings") {{_(v.key, v.ctx)}}
2. 翻译里带有数据的格式。
{{sprintf(_("%d out of 5 stars",'forum'), post.avgRating)}}
3. 不要init两遍urlvars
如果在不同文件里调用多次UrlVars.init()， vars里的参数会变成数组。
4 wx-url 使用fullUrl，是用于二次分享，直接使用docoument.url就可以。share用的是share-url
5.vue.js会在加载时渲染dom，如果dom其他地方做了改变，会在加载vue时重置。
6. npm run build:dev 有bug，无法处理window.vars
7. inline style 会保留，即使是不同element，virtual dom的bug
8. vue2.7.16 和vue-l10n 冲突，鉴于vu2EOL，不予更新，继续使用2.6.14