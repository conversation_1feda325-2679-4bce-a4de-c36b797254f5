## npm error
### Quick fix 
  remove node-sass and sharp from package.json then npm i



### Error related to sharp
  update it to at least 0.29.0

### No Xcode or CLT version detected
  - https://medium.com/@mr<PERSON><PERSON><PERSON><PERSON><PERSON>/how-to-resolve-no-xcode-or-clt-version-detected-d0cf2b10a750
  - https://reactgo.com/gyp-xcode-or-clt-version-detected/
  - If that doesn't show you any updates, run:
    sudo rm -rf /Library/Developer/CommandLineTools
    sudo xcode-select --install
    or
    softwareupdate --all --install --force
    or
    manually download them from:
      https://developer.apple.com/download/more/.

### node-gyp error (not working for me)
  brew update && brew upgrade
  maybe? (not tested)
    [reinstall brew using Rosetta and reinstall node using Terminal in Rosetta](https://stackoverflow.com/questions/66427623/error-installing-native-node-js-add-on-with-node-gyp-on-apple-m1-big-sur)
    https://medium.com/macoclock/fix-broken-node-gyp-issue-for-mac-os-596e4a8bcffd


