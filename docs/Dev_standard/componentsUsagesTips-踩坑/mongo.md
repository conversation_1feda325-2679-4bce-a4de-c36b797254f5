{$ne:null} 需要谨慎
object 的 array 下query用{$ne:null} 有错。

insertMany [], returns error in node; but not Robo 3T
insert {}, will not return error in Robo 3T
RMProperties.count {} returns 0,
use RMProperties.countDocuments {}

### index 
1. Generally, MongoDB only uses one index to fulfill most queries. However, each clause of an $or query may use a different index, and in addition, MongoDB can use an intersection of multiple indexes.
  * $or query may not use expected index 
  * if $or query is extremely slow, consider split it two two stage to make sure index is used.
  * Index Intersection is documented to be supported, but most query plan won't choose the Intersection index.
2. When create index , follow the esr standard: 
   1. Equality predicates should be placed first
   2. Sort predicates follow Equality 
   3. Range predicates follow Equality and Sort predicates
   4. This rule may not correct. if query range by ts,but sort by another better put ts before sort.
3. * Low cardinality fields (so, fields with very few unique values across your dataset) should usually not be in the index since their selectivity is very limited.
4. Only first Match in aggregation pipe line use index
   1. * Place the $match as early in the aggregation pipeline as possible. Because $match limits the total number of documents in the aggregation pipeline, earlier $match operations minimize the amount of processing down the pipe.
5. Avoid Regular Expressions That Are Not Left Anchored or Rooted,Avoid Case Insensitive Regular Expressions, regex with /cat/, /^cat/i can use index. otherwise try $text index.

6. Avoid using large array for log (eg:slice:100), it will very slow when in db migration/sync between servers. 

## mongorestore 带index时，会等所有replica完成才算完成， 否则cannot perform operation: a background operation is currently running for collection listing.geo_cacheNe， 应该 --noIndexRestore， 再手动创建index with {backgound:true}

## mongo.createIndex({},{background:true}) 还是会block colloction 操作{
	"ok" : 0,
	"errmsg" : "cannot perform operation: an index build is currently running for collection with UUID: fad8f8a8-cddc-4523-b7f8-1c45b59dd634",
	"code" : 12587,
	"codeName" : "BackgroundOperationInProgressForNamespace",
	"$clusterTime" : {
		"clusterTime" : Timestamp(1631119063, 13),
		"signature" : {
			"hash" : BinData(0,"sG0GKEw2S2UlfHbLvl8z3N0jUto="),
			"keyId" : NumberLong("6986560121434275841")
		}
	},
	"operationTime" : Timestamp(1631119063, 13)
}
## mongo >5.0.0 读取security keyfile需要权限, 否则会hang shell而且无错误提示
https://www.mongodb.com/community/forums/t/mongo-console-freezes-in-attempt-to-create-a-new-index-on-a-populated-collection-within-a-replica-set/14205/5


https://www.alexbevi.com/blog/2020/05/16/optimizing-mongodb-compound-indexes-the-equality-sort-range-esr-rule/


## watch
For change streams opened up against a collection, a drop event, rename event, or dropDatabase event that affects the watched collection leads to an invalidate event.
You cannot use resumeAfter to resume a change stream after an invalidate event (for example, a collection drop or rename) closes the stream. Starting in MongoDB 4.2, you can use startAfter to start a new change stream after an invalidate event.(not working, current solution is update token again when receive a close event @julie 20211104)

### transaction
for mongo 6.0
transaction只支持同一个db下的操作，不同db的会报错，错误没有相关提示；
同db不同tables是可以支持的；