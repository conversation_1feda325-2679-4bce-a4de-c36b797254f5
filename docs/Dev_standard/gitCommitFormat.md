git commit message:
<type>(<scope>): subject

type(*):
  feature
  fix/to
  docs/todo
  style
  refactor
  performance
  test
  chore
  revert
  merge
  sync

scope(?):
  general
  doc
  school
  model
  view
  ctrl
  db
  batch
  migrate
  html
  web
  app
  native

subject(*):
  desc about update content


eg.
  fix(DA0): user no id
  feature(ctrl): api for userInfo
  fix(ctrl): Close #45


TODO:
  1 git commit watch/hook and auto test commit matches standard;
  2 watch msg sync to dingTalk
  3 auto match bug report and close #