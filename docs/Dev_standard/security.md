security considerations as the following order:

1. Do not expose unnecessary variables or data to front end
2. Must check user AND permissions/roles to access private resources
3. Limit raws, amount of information to front end for each request
4. Log/block excessive and critical access when needed
5. Encrypt data and/or urls when needed (encryption itself is not enough, must follow the above rules first)
6. Check ALL data come from web/frontend. Log and protect when needed.
7. Return error that can identify problem, but not the source trace. Log error trace into error log or security log


localstorage for diffuser
personal info <-> server