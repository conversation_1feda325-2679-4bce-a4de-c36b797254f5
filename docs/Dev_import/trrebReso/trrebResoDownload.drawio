<mxfile host="65bd71144e">
    <diagram name="第 1 页" id="x22dp5NrSYZhoWMRteRF">
        <mxGraphModel dx="2411" dy="820" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="561" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="3" value="prop download" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="501" y="150" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="save in log" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="502" y="434" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="10" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6" vertex="1" connectable="0">
                    <mxGeometry x="-0.2217" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="10" target="18" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="8" vertex="1" connectable="0">
                    <mxGeometry x="0.1517" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="has prop in raw or newer" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="522" y="532" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="insert raw and filtered" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="502" y="652" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="16" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="12" vertex="1" connectable="0">
                    <mxGeometry x="0.0249" y="-3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="16" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="14" vertex="1" connectable="0">
                    <mxGeometry x="-0.2035" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="same id, same hash(delete ts)?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="505.25" y="260" width="111.5" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="save in dup log" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="303" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="log" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="353" y="542" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="旧的 没有log" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="598" y="268" width="90" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="情况1: init fetch 的时候更新，fetch到旧的，init到新的，只修改了时间戳&lt;br&gt;情况2: routine 的时候已经获取过" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="248" y="434" width="230" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="https://developer.ampre.ca/docs/timestamps" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="314" y="780" width="260" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="36" target="73" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="34" vertex="1" connectable="0">
                    <mxGeometry x="-0.2481" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="&lt;font color=&quot;#050505&quot;&gt;prop&lt;/font&gt;&lt;div&gt;&lt;font color=&quot;#050505&quot;&gt;Queue&lt;/font&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="-760" y="447" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="50" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-845" y="1050"/>
                        </Array>
                        <mxPoint x="-825" y="1050" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="108" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#f4f1f1;" parent="49" vertex="1" connectable="0">
                    <mxGeometry x="-0.316" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="&lt;font color=&quot;#0a0a0a&quot;&gt;media Queue&lt;/font&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="-876" y="884" width="66" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="52" target="83" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-730" y="1010"/>
                        </Array>
                        <mxPoint x="-610" y="1010" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="52" value="&lt;font color=&quot;#0f0f0f&quot;&gt;openhouse Queue&lt;/font&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="-768" y="884" width="76" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="54" target="85" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-408" y="924" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="54" value="&lt;font color=&quot;#1d1b1b&quot;&gt;propertyRoom Queue&lt;/font&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="-656" y="884" width="74.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="57" target="63" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-972" y="247" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-865" y="187"/>
                            <mxPoint x="-729" y="187"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="55" vertex="1" connectable="0">
                    <mxGeometry x="-0.7302" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="57" value="media query by ModificationTimestamp" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-995" y="67" width="260" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="60" target="63" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-972" y="247" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-575" y="187"/>
                            <mxPoint x="-729" y="187"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="58" vertex="1" connectable="0">
                    <mxGeometry x="-0.8177" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="60" value="openhouse query by ModificationTimestamp" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-715" y="67" width="260" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="63" target="93" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="61" vertex="1" connectable="0">
                    <mxGeometry x="-0.4291" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="112" value="No&lt;br&gt;id" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#f4f1f1;" parent="1" source="63" target="36" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="prop?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-785.75" y="247" width="111.5" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="68" target="77" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="log" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="-533" y="505" width="60" height="74" as="geometry"/>
                </mxCell>
                <mxCell id="69" value="filtered" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="-124" y="662" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="99" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=none;" parent="1" source="70" target="36" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="114" value="8" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#f5f6f9;" parent="99" vertex="1" connectable="0">
                    <mxGeometry x="-0.3802" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="70" value="outside manulID" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1047" y="457" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="100" style="edgeStyle=none;html=1;fontColor=none;" parent="1" source="73" target="89" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-606" y="710" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-731" y="702"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="104" style="edgeStyle=none;html=1;fontColor=none;" parent="1" source="73" target="52" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="105" style="edgeStyle=none;html=1;entryX=0.45;entryY=0.334;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=none;" parent="1" source="73" target="54" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-731" y="800"/>
                            <mxPoint x="-622" y="800"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="106" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=none;" parent="1" source="73" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-731" y="800"/>
                            <mxPoint x="-843" y="800"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="107" value="&lt;font color=&quot;#f4f1f1&quot;&gt;id&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=none;" parent="106" vertex="1" connectable="0">
                    <mxGeometry x="-0.3963" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="73" value="download prop use id&lt;div&gt;save prop use [routine mode], add id to media,openhouse, propertyRoom Queue&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-810" y="572" width="157" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="109" style="edgeStyle=none;html=1;fontColor=#f4f1f1;" parent="1" source="75" target="79" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="75" value="raw" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="505" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="77" target="75" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="77" value="replaceOne" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-553" y="412" width="100" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="79" target="69" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="79" value="remove empty and replaceOne, reserve openhouse,media, propertyroom" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-329" y="670.75" width="130" height="62.5" as="geometry"/>
                </mxCell>
                <mxCell id="80" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="98" target="69" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-459" y="1050" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="82" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="83" target="69" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="83" value="download using id,get whole openhouses for one prop and save into readable" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-498" y="970" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="85" target="69" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="85" value="download using id,get whole propertyrooms for one prop and save into readable" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-274" y="894" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="89" target="68" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="88" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="87" vertex="1" connectable="0">
                    <mxGeometry x="-0.1398" y="-2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="89" value="sameHash[without ts]?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-540" y="670.75" width="80" height="62" as="geometry"/>
                </mxCell>
                <mxCell id="91" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="93" target="69" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="92" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="91" vertex="1" connectable="0">
                    <mxGeometry x="-0.5443" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="93" value="newer than before or not exists before?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-149.75" y="247" width="111.5" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="111" style="edgeStyle=none;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontColor=#f4f1f1;" parent="1" source="96" target="89" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="113" value="7" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#f5f6f9;" parent="111" vertex="1" connectable="0">
                    <mxGeometry x="0.0852" y="-3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="96" value="prop query by ModificationTimestamp&lt;div&gt;&amp;nbsp;[init + routine mode]&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22prop%3F%22%20style%3D%22rhombus%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22-399%22%20y%3D%22247%22%20width%3D%22111.5%22%20height%3D%22100%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-573" y="810" width="146" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="97" value="&lt;ul style=&quot;box-sizing: border-box; margin: 0px; padding: 0px; caret-color: rgb(42, 43, 46); font-family: &amp;quot;PingFang SC&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Arial, &amp;quot;Microsoft YaHei&amp;quot;, 微软雅黑, 宋体, &amp;quot;Malgun Gothic&amp;quot;, sans-serif; font-size: 14px; text-align: start; white-space-collapse: preserve-breaks;&quot;&gt;&lt;li style=&quot;box-sizing: border-box; margin: 0px; padding: 0px; list-style: none;&quot;&gt;&lt;p style=&quot;box-sizing: border-box; margin: 0px 0px 2px; padding: 0px; line-height: 21px; display: inline; font-size: var(--main-font-size);&quot; class=&quot;src grammarSection&quot; data-group=&quot;1-1&quot;&gt;&lt;font color=&quot;#f5f6f9&quot;&gt;Eight threads in total&lt;/font&gt;&lt;/p&gt;&lt;/li&gt;&lt;/ul&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-792" y="8" width="164" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="98" value="download using id, get whole medias for one prop and save into readable" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-826" y="1020" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>