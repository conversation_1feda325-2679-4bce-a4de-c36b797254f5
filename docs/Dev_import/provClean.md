## Purpose
   1. Change prov in all collection To abbr Format. total 24 collections
      1. process all col，insert to colNew, when finish，Create index according to old collection
   2. Clean geo_cache collection, merge duplicate record, decrease q of bad records.
   3. Clean properties. 
      1. Compute onD,offD, sldd
      2. Find original addr, fix apt_num, unt_num
      3. Format address, basement, price, saletp, sqft, bltyr
      4. Find duplication props, add tag merged for duplicated props
      5. redo geocoding for active or sold props in two years which is not find in geoCache（143338）
      6. keep geocoding for condo /Apartment|Townhouse/
      7. add Tags for all properties.
   
## Two Step Clean 
   1. provClean.coffee First run batch, get New collections.
   2. ./built/job_provclean.sh Second run batch and rename. clean updated records. Rename table after all is finished. ReBuild ProvCityCmty under sysdata.

## ptype 修改目前只改数据库，从数据库到前台都转成fullName
## prov 前台有部分直接只用简写的，需要增加翻译。
## 

## Related featured affected
   1. user 订阅城市，sas服务城市，wesite 显示，user 默认城市，订阅城市，rst_prov,经纪服务城市都可以正确显示（订阅城市）
   2. forum, forum home, forum edit, forum detail, city and prov
   3. prop, list, map, detail, history. city and prov, sale type, on date, off date, homeschool
   4. web prop list and detail
   5. project, city and prov
   6. evaluation, sold props compare
   7. school, search, detail, prov and city translation
   8. cpm, 广告出现在正确的城市
      1. cpm ptype没有改，数据库存还是Residentail.查询给的也是Residentail （房源列表里房源的ptype是转换过的）
      2. cpm sas.prov client 传的还是全称。后台query之前改成简写。
   9.  yellow page，选择过滤正确
   10. rmlisting 正确promote
   11. edm notify, daily push notify
       1.  
   12. getAgentWithListings
       1. fix onD,offD.
   

### Geo cache clean strategy
geo_cache 数据整理，合并相同地址，使用geocoding返回的city，addr。descrease 错误数据的q
#有的数据没有addr，从_id里split出来addr, check, st, st_number
faddr有些格式也不对，清理faddr。
针对geo_cache ->geo_cacheNew
1. noLatLng，没有经纬度的不要
2. src =manual 的 设置q=130,(item.src is 'treb') or (item.src is 'TRB') 设置q=105， 统一src=TRB
   1. 如果没有addr，从_id 中取或者用st_num+st
   2. 重新build faddr
3. 没有faddr的，build faddr，copy
   1. q>=100的，copy
   2. q<100的， decrease
4. addr是数字的，删掉，按照没有addr处理
5. 从faddr和uaddr里解析出addr，
   1. faddr 第一段和record的addr相同或者faddr第一段是正确的addr格式
      1. 如果faddr长度是4段，保存
         1. 如果没有addr，从faddr里copy，保存
      2. faddr 长度不对，直接copy，标记badFaddr，通常为osm的数据。
   2. 没有addr
      1. decrease。
6. 没有处理st,st_num






改动的feature
user
forum
project
webprop
properties
verifycity


共修改了23个collection
** change prov to abbr , eg:'Ontario'->ON
'census2016',
'census',
'coophouses',
'transit_route',
'transit_route_region',
'transit_stop'
school,
'private_schools',
'school_board',
evaluation,
cpm,
form_input,
pre_constrct,
forum
user_listing



** change more then one fields
*geo_cache
_id,
prov

** cpm_stat,cpm_stat_view：
_id.prov 

** user:
city.prov
sas.prov
rst_prov

** user_profile:
savedSearch.prov

**boundary
  format cmty
  prov
  _id

university:
   campus.prov
   
**properties
  prov ->abbr
  ptype-> b|r|o Commercial,Residential,Other
  cmty,city,addr,uaddr,unify
  onD
  offD
  origId -> if ddf,has treb id, origid [ddfid], 
  ddfID->save last one
  age1
  age2
  bltYr1
  bltYr2
  


sysdata下的ProvCityCmty 重新build

expect result
1. 所有的prov-> abbr.
2. onD,offD
3. geoCache 
import之后，prop的数量和error的数量 total = properties的数量
4.测试所有user，user注册功能，project，forum，form


db mirgrate,修改所有数据库里的prov -> abbr format
策略：
process all col，insert to colnew with fixed record。 when finish，get index from old table
create index to new table（save index to file）
重新导入properties表，修改
city，prov，cmty，bltyr1，bltyr2，onD,offD,uaddr,unt, addr, agnt.prov, addtags.
check bndcmty, bndprov,bndcity with prop, save error in impPropError(src collection)
after all finished, add proeprties indexes.
rename all table.

修改properties表


生成新表。
sysdate，时间。batch跑一遍，init，生成新表，加index。如果有routine，直接做查分。

migrate放到todo下面。后上线，跑最后一轮查分，做rename。migrate时停掉watch，重做init。

import 改成watch

重新跑aggregate 重新生成sysdata下的city和prov collection