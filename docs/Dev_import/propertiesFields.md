name,   desc,   type, eg

### deprecated,
slddt,  sold time,  date,   replaced with sldd
unavail_dt, unavailable time,   date,   replaced with offD
cd, ctrdt,  date,   replaced with onD
xd -> exp
seller -> owner
lstd,   listing date,   number, 20170131,   replaced with onD
ctrdt,  listing date,   number, 20170131,   replaced with onD
ld, listing date,   strings(字符串格式2016-09-19 00,00,00.0,9/19/2016),    replaced with onD
Lease, lease price, number
pclass, , array,["b","c","f","r"], replaced with ptype 
pcls, ,string, ["b","c","f"] #biness, condo, freehold, replace with ptype
idx,'idx' #treb
vow,'vow' #
dta,'dta' ['d‘]
lsdp, leased price
"llq" , , 100,  'Lat/Lng Quality, 0-100',
area, replaced with region
kit_total, kitchen total,number, replaced with kit 
br: replaced with bdrms
stno: replaced with st_num
stp,  mylisting has stp, duplicate with saletp, stp is deleted, check in all code
[
    249000.0,
    "For Rent",
    "Lease",
    "Sale",
    "Stone",
    "Sub-Lease",
    "Sub_lease"
]
style, BCRE ONLY #TODO,change to pstyl
[
    "1 1/2 Storey",
    "1 Storey",
    "1 Storey, Rancher/Bungalow",
    "2 Storey",
    "2 Storey w/Bsmt.",
    "2 Storey w/Bsmt., 3 Storey",
    "2 Storey w/Bsmt., Laneway House",
    "2 Storey, Corner Unit",
    "2 Storey, Laneway House",
    "2 Storey, Rancher/Bungalow w/Bsmt.",
    "3 Level Split",
    "3 Level Split, Split Entry",
    "3 Storey",
    "3 Storey, Basement Entry",
    "3 Storey, Laneway House",
    "4 Level Split",
    "Basement Entry",
    "Manufactured/Mobile",
    "Rancher/Bungalow",
    "Rancher/Bungalow w/Bsmt.",
    "Split Entry"
]

region # from bcre, replaced with area
[
    "BC Northern",
    "Chilliwack",
    "Fraser Valley",
    "Greater Vancouver"
]


###fields
origId: merge prop的时候，如果有_id,ddfID之外的id，存在origId里。
dpred,  display predciton,  string, [N]
pred_sp,    predict sold price, number,
olp,    original listing price
"trbtp",    used for update data,array, ["br","dta",  "idx","manual","ml","vow"] br is treb manual import
"coop", #买家公司
    {
        "id" , "202602",
        "_id" , "TRB202602",
        "nm" , "KELLER WILLIAMS REALTY SOLUTIONS, BROKERAGE",
        "agnt" , [
            {
                "id" , "9521500",
                "_id" , "TRB9521500",
                "nm" , "Brian Master, Salesperson"
            }
        ]
    },

### types
#TODO, if lp or lpr can used for query sale or lease，sale or lease not need。
saletp, sale type, array, ["Lease","Sale"]
sale,1 set 1 if for sale
lease, 1
ptype, property type, string,b|r|o Commercial,Residential,Other
ptype2, ,array,[
    "1/2 Duplex",
    "2-Storey",
    "3-Storey",
    "Accommodation",
    "Agricultural",
    "Agriculture",
    "Apartment",
    "Apartment/Condo",
    "Arts and Entertainment",
    "Automobile",
    "Bachelor/Studio",
    "Bungalow",
    "Business",
    "Commercial Condo",
    "Condo Apt",
    "Condo Townhouse",
    "Construction",
    "Cottage",
    "Detached",
    "Duplex",
    "Education Services",
    "Farm",
    "Fishing and Hunting",
    "Food Services and Beverage",
    "Forestry",
    "Fourplex",
    "Free Standing",
    "Freehold Townhouse",
    "Health Care and Social Assistance",
    "Highway Commercial",
    "Hospitality",
    "House",
    "House with Acreage",
    "House/Single Family",
    "Industrial",
    "Industrial Condo",
    "Institutional",
    "Land",
    "Link",
    "Locker",
    "Loft",
    "Manufactured",
    "Manufactured with Land",
    "Manufacturing",
    "Mining and Oil and Gas",
    "Multi-Unit",
    "Multi-Use",
    "Office",
    "Other",
    "Parking",
    "Personal Services",
    "Professional",
    "Real Estate",
    "Recreation",
    "Recreational",
    "Remediation Services",
    "Rental",
    "Residential",
    "Resort",
    "Restaurant",
    "Retail",
    "Retail and Wholesale",
    "Room",
    "Row House (Non-Strata)",
    "Scientific and Hi Tech Services",
    "Semi-Detached",
    "Service",
    "Special Purpose",
    "Split",
    "Stacked",
    "Store With Apt/Office",
    "Townhouse",
    "Transportation and Warehousing",
    "Triplex",
    "Unknown",
    "Utilities",
    "Waste Management",
    "With Property",
    "Without Property"
]
lpunt, list price unit , array,  [
    "For Lease",
    "For Sale",
    "Gross Lease",
    "Month",
    "Monthly",
    "Net Lease",
    "Other",
    "Per Acre",
    "Per Month",
    "Per Sq Ft",
    "Plus Stock",
    "Sq Ft Gross",
    "Sq Ft Net",
    "Sq M Gross",
    "Sq M Net",
    "acres",
    "square feet",
    "square meters"
]
ltp, listing type, string, ["MLS", "assignment","exlisting", "mlslisting","mlsrent","rent"]
ptp, mapping from treb type_own1_out? vs ptype2 pstyle?
[
    "",
    "Apartment",
    "Att/Row/Street",
    "Att/Row/Twnhouse",
    "Attached",
    "Attached/Row/Town",
    "Bungalow-Raised",
    "Business",
    "Co-Op Apt",
    "Co-Ownership Apt",
    "Comm Elem Condo",
    "Comm Element Condo",
    "Commercial/Retail",
    "Condo",
    "Condo Apartment",
    "Condo Apt",
    "Condo Townhouse",
    "Cottage",
    "Det Condo",
    "Det W/Com Elements",
    "Detached",
    "Duplex",
    "Farm",
    "Fourplex",
    "Freehold Townhouse",
    "Industrial",
    "Investment",
    "Land",
    "Leasehold Condo",
    "Link",
    "Linked",
    "Locker",
    "Lower Level",
    "Mobil/Trailer",
    "Mobile/Trailer",
    "Multiplex",
    "Office",
    "Other",
    "Parking Space",
    "Phased Condo",
    "Room",
    "Rural Res",
    "Rural Resid",
    "Rural Residential",
    "Rural Residential ",
    "Sale Of Business",
    "Semi-Det Condo",
    "Semi-Detached",
    "Shared Room",
    "Store W/Apt/Offc",
    "Store W/Apt/Office",
    "Store w/Apt/Offc",
    "Time Share",
    "Townhouse",
    "Triplex",
    "Upper Level",
    "Vacant Land",
    "Vacant Land Condo"
]
pstyl,mapping from treb style
[
    "",
    "1 1/2 Storey",
    "1 1/2-Storey",
    "1-1/2 Storey",
    "2 1/2 Storey",
    "2 Storey",
    "2-1/2 Storey",
    "2-Storey",
    "2.5 Storey",
    "3 Storey",
    "3- Storey",
    "3-Storey",
    "Apartment",
    "Apparel",
    "Apts-13 To 20 Units",
    "Apts-2 To 5 Units",
    "Apts-6 To 12 Units",
    "Apts-Over 20 Units",
    "Art Gallery",
    "Art Supplies",
    "Automotive Related",
    "Bachelor/Studio",
    "Backsplit 3",
    "Backsplit 4",
    "Backsplit 5",
    "Backsplt-All",
    "Bakery",
    "Bank",
    "Banquet Hall",
    "Bar/Tavern/Pub",
    "Beauty Salon",
    "Bed & Breakfast",
    "Bungaloft",
    "Bungalow",
    "Bungalow-Raised",
    "Bush",
    "Butcher/Meat",
    "Cabins/Cottages",
    "Cafe",
    "Campgrounds",
    "Car Wash",
    "Cash Crop",
    "Cater/Cafeteria",
    "Caterer/Cafeteria",
    "Church",
    "Coffee/Donut Shop",
    "Coin Laundromat",
    "Convenience/Variety",
    "Cooler/Freezer/Food Inspect",
    "Copy/Printing",
    "Crafts/Hobby",
    "Dairy Products",
    "Day Care",
    "Delicatessen",
    "Delivery/Courier",
    "Distributing",
    "Drugstore/Pharmacy",
    "Dry Clean/Laundry",
    "Electronics",
    "Entertainment",
    "Factory/Manufacturing",
    "Fast Food/Take Out",
    "Fitness/Training",
    "Florist",
    "Food Court Outlet",
    "Footwear",
    "Fruit/Vegetable Market",
    "Funeral Home",
    "Furniture",
    "Garden/Landscaping",
    "Gas Stations",
    "Golf",
    "Golf Course",
    "Golf Driving Range",
    "Gravel Pit/Quarry",
    "Grocery/Supermarket",
    "Hair Salon",
    "Hardware/Tools",
    "Health & Beauty Related",
    "Hobby",
    "Home Improvement",
    "Horse",
    "Hospitality",
    "Hospitality/Food Related",
    "Hotel/Motel/Inn",
    "Industrial",
    "Industrial Loft",
    "Institutional",
    "Jewellery",
    "Laboratory",
    "Livestock",
    "Loft",
    "Manufacturing",
    "Marina",
    "Medical/Dental",
    "Multi-Level",
    "Office",
    "Other",
    "Parking Lot",
    "Pizzeria",
    "Professional Office",
    "Real Estate Office",
    "Recreational",
    "Residential",
    "Restaurant",
    "Restricted",
    "Retail",
    "Retail Store Related",
    "Schools",
    "Self Storage",
    "Seniors Residence",
    "Service Related",
    "Sidespilt 4",
    "Sidesplit 3",
    "Sidesplit 4",
    "Sidesplit 4 ",
    "Sidesplit 5",
    "Sidesplt-All",
    "Spa/Tanning",
    "Split",
    "Sporting Goods",
    "Sports/Entertainment",
    "Stacked Townhse",
    "Transportation",
    "Travel Agency",
    "Warehouse Loft",
    "Warehousing",
    "Waterfront",
    "Woodworking"
]

#所有卖掉的都有lst，treb，bcre
#add to abbreviate
lst, last status,（有30%数据没有lst字段）ddf没有lst。显示的对应表，前后台都可以用。
### from bcre, bcre 没有Pc, 我们导入的时候会加入Pc
status-> lst
[
     "Active",
     "Cancel Protected",
     "Collapsed",
     "Expired",
     "Hold all Action",
     "Sold",
     "Terminated"
] -->
### from treb
lsc -> lst
[
    "",
    "Dft",
    "Exp",
    "Ext",
    "Lc",
    "Lsd",
    "New",
    "Pc",
    "Sc",
    "Sce",
    "Sld",
    "Sus",
    "Ter"
]
[
    "",
    "Active", #New
    "Cancel Protected", #Cp
    "Collapsed", #Clp
    "Dftp",
    "Exp",
    "Expired", #Exp
    "Ext",
    "Hold all Action", #Haa
    "Lc",
    "Lsd",
    "New",
    "Pc",
    "Sc",
    "Sce",
    "Sld",
    "Sus",
    "Ter",
    "Terminated" #Ter
]
status, ,string,    ["A", "U"]
src, source, string, [
    "BRE",
    "DDF",
    "RM",
    "RMTRB",
    "TRB"
]
##from bcre ONLY
tpdwel, bcre ptype2 is from this fields
[
    "1/2 Duplex",
    "Apartment/Condo",
    "Duplex",
    "Fourplex",
    "House with Acreage",
    "House/Single Family",
    "Manufactured",
    "Manufactured with Land",
    "Other",
    "Recreational",
    "Row House (Non-Strata)",
    "Townhouse",
    "Triplex"
]


###ids
_id  
sid
ddfID,  ,array,[ddfID from all souce]
id, 
uid,,
topuids,,
favU, favorite user ids,array
aid, agent id, string,
aid2,,
bid, branch id, string
ml_num	sid	ml Number	

### times
ts,record created date/time, set when insert to mls_record, or new Date() when import
mt, record change ts, or new Date() when import
pcTs: price change ts
spcts: price or status change ts
mt,modified date/time
exp, expiry date(number,20161123),replace with offD.(src.xd or src.exp, from bcreman,trebman)
sldd,sold date,(number, 20170131.src.solddate or src.cd src.unavail_dt)
onD,on market date.(number, 20170131.  src.ld or src.input_date or ret.ts or src.ts_sql)
offD,off market date(number, 20170131 sldd or unavail_dt or dt_ter or mt or ts_sql), unset when status is A.
topTs,1
topup_pts,1 #Confrim with rain
ohz, openhouse array of all open house date, 
    [   
        {
        "f" , "2020-06-13 12,00",
        "t" , "2020-06-13 14,00",
        "tp" , "L",
        "l" , "https,//us02web.zoom.us/j/85408620840?pwd=RzFpYS9iOU9idSsrVFlPUWxEL1lFZz09"
    }
        {
            "f" , "2018-08-31 17,00",
            "t" , "2018-08-31 19,00",
            "tp" , "P" physical
        }
    ]
psn, occ  possession date eg,30Days/Tba

##price and status related field
lp,listing price
lpr, lease price
status, 'U' | 'A'
sp, sold price
mfee,1
tax,

his,
 [{
            "s" , "New",
            "ts" , ISODate("2016-09-24T03,31,42.870Z")
        }, 
        {
            "s" , "Pc",
            "ts" , ISODate("2019-07-18T16,33,33.104Z"),
            "o" , 314900,
            "n" , 379000,
            "c" , 64100
        }, 
        {
            "s" , "Chg",
            "ts" , ISODate("2019-07-18T16,33,33.104Z"),
            "o" , "Ext",
            "n" , "Pc"
        }, 
        {
            "s" , "Chg",
            "ts" , ISODate("2020-01-10T23,33,24.802Z"),
            "o" , "Pc",
            "n" , "Ext"
        },
        {
            "s" , "Chg",
            "ts" , ISODate("2020-10-03T00,12,42.105Z"),
            "o" , "Ext",
            "n" , "Ext"
        }
    ]

comm, commission
lcTp, last change tp 'Pc'/'Chg', string,
lcO, last change old value,
lcN, # last change new value
pc , obj.lcN - obj.lcO,new price - old price
pcPct, obj.pc/obj.lcO,


##address related
addr,1
st ,street "Lakeshore",
st_dir ,street direction "W",
st_num , 2269,
st_sfx , "Blvd",
citycd , "01.W06",
city_d , "Toronto W06",
cmtycd
unt , unit number,
apt_num, unt number, mapping to unt
lat/lng;loc,{type,'Point',coordinates,[lng,lat]}, position
city,
cmty,
daddr, disp addr
prov,
zip,
geoq, geo Quality

cnty,country



### rooms
"kch" , 1,(kit_total or 'num_kit'+'kit_plus',treb) total of kitchen #add to format
num_kit
kit_plus,kitchen plus
kchntp,[Separate,Shared]

bthrms, bathrooms
bdrms, bedrooms
tbdrms, total bedrooms
gr, garage
tgr, total garage
br_plus, bedroom plus
bsmt:
[
    "",
    "Apt",
    "Crw",
    "Entrance",
    "Fin",
    "Finish/Walkout",
    "NON",
    "Non",
    "Prt",
    "Sep",
    "Slab",
    "Unfin",
    "W/O",
    "W/U", walkup
    "Walkout"
]
den



### amenity
dyr, Dryer
entr,    Entrance
'fmlyfrnd' Family Kid Friendly		
fpl,  Fireplace
fpop,    Free Parking On Premise			
frdg,     Fridge
gatp,   Parking Type	
grdn,     Garden
gym,      Gym
heat,  'heating
htb,    httub，Hot Tub			
kchntp, kit_tp,  Kitchen Type			
lkr, lkr	Locker	
ldrytp, ldry_tp, Laundry Type	
laundry,
laundry_lev,
lease_term,
mintrm, minrenttrm,,Min Rent term	
movn,	Micro Oven,mcovn
tv, cable,   
vturl, tour_url, 
lvl, stories,  
central_vac, 'vac'
pets	pets	Pets	
ac,a_c
pool,
fce,nwse,comp_pts or condo_exp
blcny,patio_ter
crsst,cross_st
constr,consrtction type, brick/Concrete/log/.....
bm, broker remark
m,PublicRemarks
seller, from owner
rltr,realtor name
"la" , treb agent 
la2, ddf agent
{
    "id" , "087013",
    "_id" , "TRB087013",
    "nm" , "ROYAL LEPAGE YOUR COMMUNITY REALTY, BROKERAGE",
    "tel" , "************",
    "fax" , "************",
    "agnt" , [
        {
            "id" , "1724282",
            "_id" , "TRB1724282",
            "nm" , "MONA SEDFAWI, Salesperson",
            "tel" , "************"
        }
    ]
},

bltYr, from ddf ConstructedDate, number eg,2004,
bltYr1, compute from age or bltYr
bltYr2

age, from treb yr_built
age1,
age2,

sqft, '1000-1500' ｜ 500+ ｜ <600
"sqft1" , 1000,
"sqft2" , 1500,
taxyr, tax year ( from treb yr.)

flt,Front_ft

lsp,  last sold price
lspDifPct, 本次售价(sp)与上次售价(sp)差额百分比(两次均为成交房源)
llpDifPct, 本次价格(lp)与上次售价(sp)差额百分比(本次为active房源，上次为成交房源)
  
## photo related
### from bcre
pho,Piccount
picUrl,Pics

### ddf image，baseimageurl,count,picUrl
picTrb:{}
picDdf:{}
rmlisting 
phosrc,1
phomt,1
picUrl,1
pic,1
photonumbers,1,
phodl, photo download time
PhotoDlDate, photo download date。



ytvid,1
vurlcn,1,
vimgcn,1,
market,1
_sysexp,1
project,


feat,[
    "  ",
    "          ",
    "Arts Centre",
    "Beach",
    "Bush",
    "Campground",
    "Clear View",
    "Cul De Sac",
    "Cul De Sac/Deadend",
    "Cul Desac/Dead End",
    "Cul-De-Sac",
    "Dead End",
    "Electric Car Charg",
    "Equestrian",
    "Fenced Yard",
    "Garden Shed",
    "Geenbelt/Conser.",
    "Golf",
    "Greenbelt/Conse",
    "Greenbelt/Conserv",
    "Greenbelt/Conserv.",
    "Greenblt/Conser",
    "Grnbelt/Conserv",
    "Grnbelt/Conserv.",
    "Hospital",
    "Island",
    "Lake Access",
    "Lake Backlot",
    "Lake Pond",
    "Lake/Pond",
    "Lake/Pond/River",
    "Lakefront/River",
    "Level",
    "Library",
    "Major Highway",
    "Marina",
    "Other",
    "Park",
    "Part Cleared",
    "Part Cleared ",
    "Place Of Workship",
    "Place Of Workshop",
    "Place Of Worship",
    "Public",
    "Public Transit",
    "Ravine",
    "Rec Centre",
    "Rec Rm",
    "Rec/Comm Centre",
    "Rec/Commun Centre",
    "Rec/Commun Ctr",
    "Rec/Commun.Ctr",
    "River/Stream",
    "Rolling",
    "School",
    "School Bus Route",
    "Security System",
    "Skiing",
    "Sking",
    "Sloping",
    "Slopping",
    "Stucco/Plaster",
    "Terraced",
    "Tiled",
    "Tiled/Drainage",
    "Treed",
    "Waterfront",
    "Wood",
    "Wood/Treed",
    "Wooded/Treed"
]
spec,[
    "  ",
    "Accessibility",
    "Accessibity",
    "Crcd-Certif Granted",
    "Expropriation",
    "Heritage",
    "Landlease",
    "Other",
    "Unknow",
    "Unknown",
    "Unkown"
]
amen,[
    "  ",
    "B B Q's Allowed",
    "Bbq's Allowed",
    "Bbq's Allowed ",
    "Bbqs Allowed",
    "Bike Storage",
    "Bus Ctr (Wifi Bldg)",
    "Bus Ctr Wifi Bldg",
    "Business Centre(Wi Fi Bldg)",
    "Car Wash",
    "Conciege",
    "Concierge",
    "Exercise Rm",
    "Exercise Room",
    "Games Room",
    "Guest Suite",
    "Guest Suites",
    "Guest Surface",
    "Gym",
    "Indoor Pool",
    "Lap Pool",
    "Media Room",
    "Outdoor Pool",
    "Party Rm/Meeting Rm",
    "Party Room",
    "Party Room/Meeting Room",
    "Party/Meeting Room",
    "Recreation Room",
    "Recreational Room",
    "Roof Top Deck/Garden",
    "Rooftop Deck/Garden",
    "Satellite Dish",
    "Sauna",
    "Security Guard",
    "Security System",
    "Squash/Racquet Court",
    "Tennis Court",
    "Visitor Parking",
    "Vistor Parking"
]
area_infl,[
    "Golf",
    "Green",
    "Grnbelt/Conserv",
    "Lake/Pond/River",
    "Major Highway",
    "Other",
    "Park",
    "Public Transit",
    "Rec Centre",
    "Skiing",
    "Subways",
    "Wooded"
]
flt, lotsize

dpred,['N']
rmdaddr,[ "N","Y"] rmlisting dispaly full addr
rmdpho,["N", "Y']rmlisting dispaly pic

access_prop1,
[
    "Highway",
    "Marina Docking",
    "No Road",
    "Other",
    "Private Docking",
    "Private Road",
    "Public Docking",
    "R.O.W. (Deeded)",
    "R.O.W. (Not Deeded)",
    "Seasonal Municip Rd",
    "Seasonal Priv Rd",
    "Water Only",
    "Yr Rnd Municipal Rd",
    "Yr Rnd Municpal Rd",
    "Yr Rnd Private Rd"
]