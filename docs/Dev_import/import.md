# import from original data to Properties

## Work Flow Summary

1. Watch 5 different source collection
2. Do mapping and transform for different source
3. Format and unify (eg:address, bltYr, basement, sqft, lp, onD,offD etc.),
4. skip import cases:
  1. `saveToMaster.coffee checkProp` will check required fields, go to next if missing required fields. (save toerror log)
5. Do geocoding
6. Find and merge with old properties.
  1. if has old property and \_id is same, update property.
  2. if has duplicate property (find condition is define at `saveToMaster.coffee buildQuery`), need to do propertymerge.
7. Add Tags (sldDom, school, transit, boundary, census, sldDom, bltYr1, bltYr2)

8. save to db

## 1. Watch

- watchAndImportToProperties.coffee

### 1.1 watched collection

- rni
  - mls_treb_master_records (including idx and evow data)
  - mls_treb_dta_records
  - mls_crea_ddf_records
  - mls_bcre_manual_records
  - mls_treb_manual_records

### 1.2 Watch steps for each source.

#### 1.2.1 init:

1. force import from back hours
2. init 时必须给出-h(query back hours)参数,
3. get new resume token for each collection
4. import props with in back hours
5. save resume token, 启动 watch
6. Update token (default every 5min), save token and mt in Sysdate: srcTp+'ToProp' (eg:'bcreToProp'), update processStatus when update token

#### 1.2.1 Routine:

1. Find resume token and mt in Sysdata
2. Start Watch with token
   1. if no token or no mt require init
   2. if token is invalid (too old)
      1. get new token
      2. query back from (token mt - half a hour) and do import.
      3. save resume token watch from new token.
   3. Update token (default every 5min)

## 2. Mapping and transform

Collection field definition: `docs/Collection_definitions/properties.md`

### 2.1 Common fields Mapping

- Photo
  - phoMt -> phomt, phoMt is date
  - phoDl -> phodl phoDl is date
  - picNum -> pho
  - phosrc ->phosrc
  - picTrb|picDdf -> {picNum,maxPicSz,avgPicSz}

### 2.2 impMappingBcreManual.coffee

- Date related

  - 'SoldDate' -> 'slddt', # '4/12/2018' : sldd 5/23/2018
  - 'ListDate' -> 'ld' # '10/16/2015', : lstd/ctrdt/ts
  - 'ExpiryDate' -> 'exp' #

- ListPrice->lp,
- 'Price -> if no list price but active use price as lp else as sp

### 2.3 impMappingDdf.coffee

- Date related, DDF Date formate is Day first

  - ListingContractDate, 31/3/2018
  - @@attributes.LastUpdated->lup, mt
    - "Thu, 17 Sep 2015 02:04:13 GMT",转换成日期会有时区问题
  -
  - 'Photo.PropertyPhoto.PhotoLastUpdated'-> calculted to ,phoMt
  - picTs: in old logic, earliest PhotoLastUpdated
  - phoMt: in new logic, latest PhotoLastUpdated

- no sp, no sold date, no exp,xd,no unavailable_date.

- Agent 的地址只 format prov
- OpenHouse 下有可能是 object 或者 array
- AgentDetails.Office.Address.Province 可能为空
- AgentDetails 下不同的 agent 如果是同一个 office，copy 到 la2 下，不同的，在 la2.agnt 下各自存 office。
- mls_crea_ddf_records 里有 10%的房源有 lat，lng 信息，依照 ddf 来的 city 与 bndCity 是否 match, geoq 给 90 or 100

### 2.4 impMappingTreb.coffee

- used Date Fields:

  - "Input_date"->input_date : "12/4/2015" or "2015-12-14 00:00:00.0",
  - "Ld" -> ld : "12/3/2015" or "2015-12-14 00:00:00.0",
  - "Xd" -> exp : "5/19/2016", or "2016-10-31 00:00:00.0"
  - "Unavail_dt" -> unavail_dt : "2/21/2016" or "2016-10-22 00:00:00.0",
  - "Dt_ter" -> dt_ter: "2016-10-22 00:00:00.0",
  - "Lud" : "5/5/2016", # do not save to properties. Not used in recent treb_master_records
  - Pix_updt"->phomt : "2016-01-11 09:12:23.0",
  - phodl ->phodl
  - cd -> sldd
  - Timestamp_sql (evow, idx) -> lup : "2022-11-05 16:06:44.0"
  - Timestamp (dta) -> lup : 20221031.23242

- Unused date fields

  - lcdt??
  - xdtd??
  - Cndsold_xd
  - "Cond" : "Lease/Permits",
  - Scdt
  -

- src -> trbtp
- Style -> pstyl,Bus_type->pstyl, eg:"1 1/2 Storey",Bungalow,Loft,Multi-Level, no detached,
- type_own1_out -> ptp,eg:Condo Townhouse,"Condo Apt"
- ptype2 里包括了 ptp，prop_type，pstyle 的内容
- area->region
- "age": "New" keep new, age1 =0 ,age2=0

### 2.5 impMappingTrebManual.coffee

- Date related
  - "cd" : "5/6/2016", or /Date(1606280400000-0500)/
  - "ld" :
  - 'xd':
  - 'unavail_dt'

### 2.6 impMappingProperty.coffee

- convert all date related fields to number
- check original address from source
- noGeocoding for property more then 2 years old
- keepPropGeocoding for condo and apartment with mfee
- VideoLink ->vturl
- change agent prov to abbr format

## 3. Format and Unify

impFormat.coffee

- add empty fields to unset
- setOrigId, eg: [_id,'bcre'+displayId],[trb...]
- format ProvAndCity, address,unt, zip.
  - /House|Detached/ has unt, save to metaInfo
- format basement
- format sqft # sqft is mapping from manual import only
- format OwnershipType,ptype,trbtp
  - add OwnershipType for treb listing if not from source
    - condo, freehold
- checkLp
  - 只检查 residential 的，lpunt 是 for sale 或者 forlease 的
  - 如果 lp=lpr，根据数值，去掉一个
  - 如果只有 lp
    - 如果 lp>MAX_LEASE_PRICE，set sale tp as sale
    - 其他情况，应该 warning，不修改数据
  - 如果只有 lpr
    - 如果 lpr<SAFE_LEASE_PRICE，set sale tp as Lease
    - 其他情况，应该 warning，不修改数据
- check onD, sldd and offD
  - if they are a future date
  - if sldd is changed

## 4. Check prop

saveToMaster.coffee

- Do not import if has following problem
  - no \_id or \_id is null
  - required fields missing: 'saletp', 'ptype', 'src','cnty', 'prov', 'city'
  - cnty is not in Canada
  - prov is not in Canada
  - no lp or lpr
  - prop.ptype is not string

## 5. Geocoding

new_geocoder.coffee

- 通过房源的 Uaddr 检查 geo_cache，判断使用 cache 或者 prop 或者 do real geocoding
  - 进入到 geocoding 的房源都是 format 过地址的。对于没有 format 过的，geocoding 里会再次 format，同时查询两个地址。
- 在 cache 里找到 q >= 100 或者半年以内的地址，和房源比较，选择更优先的使用。如果是手动导入的 source，保存到 cache 里

  - geocoding score
    - q = 130, manual fixed
    - q = 120, computed by stat with condos of same uaddr
    - q = 105, from treb manual import
    - q = 100, from ddf Address, and city name match boundary city name.
    - q = 90, from ddf Address, and city name NOT match boundary city name.
    - q = 100, from geocoding engine.
    - q < 100, from geocoding engine. redo geocoding when over half years.
  - geocoding 里，对于输入的 lat,lng <= 100 的，采取不信任原则。
    - 如果在 cache 里没找到，输入房源的 q = 100, 且狀態是 active 的或者 sold 的，重做 geocoding，否則 q = 90

- 定期统计 condo，更新 geoCache

## 6. Merge Prop

Purpose: 1. 更新已有的 listing，添加 his,la 等信息 2. 去掉重复 listings,两天之内上市的同一个房子，相同的价格和描述，认为是同一个 listing，去掉重复的一个。

### 6.1 Query existing or duplicated listing

- onD within 2 days and same uaddr, lp/lpr, unt, status, ptype, tot_area

### 6.2 Merge fields

- merge fields:
  - origId: all id related with this prop (eg:)
  - origSrc: all src
  - trbtp
  - recompute onD ,sldd, offD
    - onD: ld or listingContractDate or onD or input_date or picTs or picMt or lup or lud or mt
    - sldd: SoldDate or cd or unavail_dt or lud or "exp/mt which is small".
    - offD, for status = U only: sldd or unavail_dt or dt_ter or xd(exp) or lud or lup or "exp/mt which is small".

### 6.3 Update Strategy

#### 6.3.1 If new and old have same \_id

Update existence property

1. if new property src is dta, but old property is not dta (trbtp not has dta, or has dta but also has other value), only update `TREB_DTA_ONLY_FIELDS`.
2. else update all fields.
3. if merged field is existing, that mean this property already merged with another property, so we need run mergeProp again to make sure updated value also update to another property.

#### 6.3.2 Property Merge Priority
When two properties has same src, the new one will be keep, old one will be merge, the property import later is the "new" one.

| Priority | A                 | B   | prop has high priority |
| -------- | ----------------- | --- | ---------------------- |
| 1        | DDF               | TRB | B                      |
| 2        | DDF               | BRE | B                      |
| 3        | DDF               | X   | A                      |
| 4        | TRB && prov is BC | BRE | B                      |
| 5        | TRB && dta        | TRB | B                      |
| 6        | TRB               | X   | A                      |
| 7        | BRE && prov is ON | TRB | B                      |
| 8        | BRE               | X   | A                      |

#### 6.3.3 Properties Merge Process

`saveToMaster.coffee mergeProp` is the function that do merge, it will merge two property then return propToKeep as final property.  
for `OVERWRITE_FIELDS_WHEN_IS_EMPTY` fields, if propToKeep doesn't has and propToMerge has, it will copy from propToMerge.  
for `TREB_DTA_ONLY_FIELDS` fields, if propToKeep isn't dta and propToMerge is, it will copy from propToMerge.  
for `DDF_ONLY_FIELDS` fields, if propToKeep isn't DDF and propToMerge is, it will copy from propToMerge.

Chain merge:  
in following example, DDF25067843 had merged by DDF25067992 first, then TRBS5827499 came, so TRBS5827499 merged with DDF25067992, and add field `mergedTop: 'TRBS5827499'` to DDF25067843.
```
  {
    _id: 'DDF25067843',
    ddfID: 'DDF25067843',
    origId: [ 'DDF25067843' ],
    src: 'DDF',
    merged: 'DDF25067992',
    mergedTop: 'TRBS5827499'
  }
```

```
  {
    _id: 'DDF25067992',
    ddfID: 'DDF25067992',
    origId: [ 'DDF25067843', 'DDF25067992' ],
    origSrc: [ 'DDF' ],
    src: 'DDF',
    merged: 'TRBS5827499'
  }
```

```
  {
    _id: 'TRBS5827499',
    ddfID: 'DDF25067992',
    origId: [ 'DDF25067843', 'DDF25067992', 'TRBS5827499' ],
    origSrc: [ 'DDF', 'TRB' ],
    src: 'TRB',
  }
```

#### NOTE: records with merged Flag is merged with others, do not include in search, but can query by id.

## 7 Add Tags
- add sldDom, school, transit, boundary, census, sldDom, bltYr1, bltYr2
  - sldDom, bltYr1, bltYr2 and school tags always update
  - transit, boundary, census only add at property first time import
- checktags
  - slddom >365, warning
  - check bndCity,bndProv,bndRegion, if not same with prop,save to log

## 8 Save To db
1. unset require fields  
**unset only happen when update property with same _id**
  1. unset tag if not exist
  2. 如果必须字段缺少，做 unset，`CHECK_UNSET_FIELDS`:
      - 'm',
      - 'kch', # mapping from kit_total or kit_plus+num_kit
      - 'num_kit',
      - 'kit_plus',
      - 'bdrms',
      - 'tbdrms',
      - 'br_plus',
      - 'bthrms',
      - 'gr',
      - 'tgr',
      - 'park_spcs',
      - 'sqft',
      - 'sqft1',
      - 'sqft2',
      - 'yr_built',
      - 'age',
      - 'age1',
      - 'age2',
      - 'mfee',
      - 'tax',
      - 'unt',
      - 'apt_num' # was mapping to unt
      - 'flt', # front lot feet.
      - 'phodl',
      - 'psn',
      - 'rmSqft',
      - 'rmSqft1',
      - 'rmSqft2',
      - 'sqftQ',
      - 'rmBltYr',
      - 'sqftSrc',
      - 'untStorey'
2. set merged if old is merged
3. if new is merged, also save new in db.
4. save metainfo to properties_import_log

### metainfo (properties_import_log) fields
| Key | Description | Example Value |
|-----|-------------|---------------|
| _id | property _id + src suffix | "DDF24807807_ddf" |
| id | final property _id, if this property is merged to another property, this is merged _id | "TRBW5743907" |
| idBeforeMerge | property original _id, only exists when this property merged | "DDF24807807" |
| setOldAsMerge | when importing this property, if this new property has high priority, and another property need to merge to this property, this is the _id of that property | "DDF24981779" |
| setNewAsMerge | when importing this property, if this new property has low priority and need to merge to another property, this field will be set value 1 | 1 |
| addr | original addr and formatted addr | { "orig": "1050 STAINTON Drive Unit# 223", "addr": "1050 Stainton Dr" } |
| cmty | original cmty and formatted cmty | { "orig": "0180 - Erindale", "cmty": "Erindale" } |
| prov | original prov and forrmatted prov | { "orig": "Ontario", "prov": "ON" } |
| st | original st and forrmatted st | { "orig": "STAINTON", "st": "Stainton" } |
| unt | original unt and forrmatted unt | { "orig": null, "unt": "223" } |
| unset | unset fields | { "rmPsn": 1, "rmPsnDate": 1 } |
| geocoding | geoCoding metainfo | { "doCoding": true } |

### deprecated Fields

cd,slddt -> sldd
unavail_dt-> offD
lstd，ctrdt,ld,listingContractDate -> onD
kit_total->kch
stp ->saletp

Lease，pclass，pcls，idx，vow，dta，lsdp，llq,style
