<mxfile host="65bd71144e">
    <diagram id="creaResoDownload" name="CREA RESO Download Flow">
        <mxGraphModel dx="617" dy="736" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="Start" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="20" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="Initialize System" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="Initialize Collections, Queue, ResoClient" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="Download Initial Data&lt;div&gt;Member+Office&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Download Openhouse" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="680" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="49" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="10">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="320" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="Check Run Mode" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="340" width="120" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="Download Property Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="260" y="460" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="Start Queue Processor" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="570" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="Start Routine Worker&lt;div&gt;query by mt: Member+Office_Prop&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="710" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="Start OpenHouse Worker&lt;div&gt;query all oh everyday 0am&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="850" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="Start Property Status Sync Worker&lt;div&gt;query all active prop everyday 2am&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1000" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="540" y="210" as="sourcePoint"/>
                        <mxPoint x="340" y="240" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="540" y="220"/>
                            <mxPoint x="340" y="220"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3" target="5" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="540" y="210" as="sourcePoint"/>
                        <mxPoint x="740" y="240" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="540" y="220"/>
                            <mxPoint x="740" y="220"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="4" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="340" y="320"/>
                            <mxPoint x="540" y="320"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="6" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="740" y="320"/>
                            <mxPoint x="540" y="320"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="40">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="630" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="41">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="720" y="400"/>
                            <mxPoint x="770" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="42">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="880" y="400"/>
                            <mxPoint x="910" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="50">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1040" y="400"/>
                            <mxPoint x="1060" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="Init" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="380" y="380" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="Routine" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="600" y="380" width="40" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>