# import branch online steps

## on batch server
1. Stop all import batches and remove cronjobs
   crea/treb/photos/tgr

2. Sync source to latest and npm install
   
3. Import old collections to new rni db
   Run the following commands for twice, because the first run will take more than half hour.
   replace the connection string with product rni
   ```
   ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c treb > logs/migrateTreb.log 2>&1 &
   ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c crea > logs/migrateCrea.log 2>&1 &
   ```
4. Start new import routines
   replace the id and credentials with real ones
   ```
   ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea >> logs/creaPhoto.log 2>&1
   ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb >> logs/trebPhoto.log 2>&1
   ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee rKfpLGwCCyfcWM1KzTdSQ6Y8 VenzibWOzDXnAm70CCJEpeVW > logs/creaDownload.log 2>&1
   ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee evow EV20ywa 'C8p6\$39' > logs/trebEVOW.log 2>&1
   ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx D20ywa 'H9p3\$63' > logs/trebIDX.log 2>&1
   ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee vow V14fxd 'Rd\$8439' skipDiff > logs/trebVOW.log 2>&1
   ```
5. Restart app/web nodejs

## on all other servers

After batch server finished upgrade, sync source to new latest, and restart nodejs