###
refine geocoding 计算结果存到geocache里。不更新原始数据。
1.  aggregate properties by uaddr, for condo and condo townhouse, output into properties_condo, match count >20
2.  process properties_condo
    1.  if one address has only one loc, use this loc update geo_cache, set rlat, rlng
    2.  if one address has more loc, remove outlier, get avg, calc dist of orig with avg loc
        1.  calc dist of avg and orig
            1. if dist <= 100, save avglat as rlat, avglng as rlng （是否自动更新geocache的lat，lng，还是增加）
            2. if dist > 100, do geocoding with google
               1. calc googleDist with origloc and avgloc, get mindist
               2. if google q is 100, and mindist < 100, use the one near google as rlat,rlng,rq = 120, rsrc = stat
               3. else set new_geocodder as tobe fixed.
                  1. 需要记录使用了多少condo来做aggregate
                  2. geoConflict[{tp:'google',lat,lng,q:}},{tp:'stat',lat,lng,cnt:}}]
    3. save avgLoc, rloc, dist, google result to geo_cache 
   


3. Add geo_cache manage page. refer to boundary Admin page
  1. geo_cache (bing,stat,ddf,trebManal),geoConflict = 1, 
     1. 
     2. geoConflict = {bing:{lat,lng,q:,src:},DDF:{lat,lng}, stat:{}, TRB:{}}, lat,lng,src:'manual',q:130
 
  2. Query Geo_cache by fileds: id, geoConflict, aUaddr, q, rq, dist,rSrc. sort by dist, avgCnt
  3. need Pagination, show result in list, in list page
  4. Click record enter to detail Page
     1. Show lat,lng, rlat, rlng as two point in map. show two markers. (maybe 3, goLat,goLng)
     2. orig marker can not drag
     3. rlat,rlng marker can drag. 
     4. cick any marker, show button "use this". will update geocache, set lat,lng, geoq, src='manual',q=130, unset rlat,rlng,rq, geoConflict,
     5. or input correct.
     6. add another button as apply to all properties. update all properties with same uaddr to new lat,lng. (warning, count, confirm.)



  5.  确认refine geocoding的结果(to discuss more)
    1. search lat!=rlat, tofixed not exists. check sevral result
    2. click confirm, will update lat,lng with rlat,rlng. set q = 120, src = stat, choose use which.
    3. click update all properties. will update all properties of this address using new q
   
4.  use rlat,rlng
   1. in geocoding if has rlat,rlng and rq, use rlat,rlng
   2.  for townhouse. if source has lat and lng, use mapped value for properties. do not use cached if not in cache, upsert in cache. 
       1.  有的townhouse是一个地址，不同的unit可以有不同的geocoding，geo_cache里存的是一个stat的地址（或者geocoding的地址），如果source（trebman或者ddf）給了lat，lng，那么就不需要查cache，直接使用，如果没给，用cache里的。
   3. ddf,trebMan 进来的loc 与geocoding的orig loc如果距离太远，插入到geoConflict里。


./start.sh batch/prop/refineGeocoding.coffee
condo里uaddr一样的房源，找到中心点和std，根据std去掉噪声数据，然后重新求平均。更新geo cache，q=120

geo_cache excpected result
src:stat, q = 120
src:manual ,q = 130
src:treb, q = 105

#check geo cache里有多少q=120的点



Aggregate Condo lat,lng, update geo_cache和propertyNew. (7min)
  ```
  nohup ./start.sh lib/batchBase.coffee batch/prop/refineGeocoding.coffee useNew > logs/refineGeocoding.log 2>&1 &
  ```
  sample result:
  address that has more then 20 same uaddr 9647
  {
    totalUaddrToProcess: 9647,
    uaddrToCompute: 9235, # uaddr to do aggregate
    geoCacheMatchedCount: 2308, # find in geocache by uaddr
    geoCacheModifiedCount: 2308, # modified in geochace
    propsmatchedCount: 1670837, #find in properties
    propsmodifiedCount: 1670837, # modified properties
    allSameAndToSaveCacheOnly: 394, # only modify geocoache
    noGroupAsToFewValidprop: 18 # has less then 15 after filter outlier.
  }
  aggregate condo or townhouse: 1:45:19.013 (h:mm:ss.mmm)
