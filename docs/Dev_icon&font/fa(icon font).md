1. add '/css/font-awesome.min.css' to page
2. '/css/font-awesome.min.css' uses '/fonts/fa.ttf'
3. usage: span.fa.fa-rmbed



## How to edit icons?
1. goto https://icomoon.io/
2. goto https://icomoon.io/app/#/select then https://icomoon.io/app/#/projects
3. import project, select RM Icons proj.json
4. add icons(svg format, prefer single color!)
5. generate font
6. modify font-awesome.(min)css to /fonts
7. download proj and update RM Icons proj.json（需要返回至引入文件页面下载）

在项目中更新
替换项目中原有的font 下载文件夹/font/* -> /webroot/public/fa/
替换项目中原有的css文件 下载文件夹/style.css -> /webroot/public/css/font-awesome.css
  粘贴内容，在最上面的url部分添加/ （'fonts -> '/fonts)
压缩更改后的css文件  ./bin/brewcoffee.sh
## multi-color icons
<span class="fa-radar2"><span class="path1"></span><span class="path2"></span></span>


## svg sprite 生成步骤
1. 在 icomoon里生成新的project https://icomoon.io/app/#/select
2. 点击上方import Icons按钮导入要生成的svg图标
3. 选中要生成的图标后点击最下方Generate SVG & More，切换页面
4. Download 旁边有个设置图标，点击进行设置修改：Class Prefix编辑框填写ss-，勾选Use class selector，Minify JS code，Override size，并将Override size设置为100，勾选Tiles(CSS sprite)，设置Sprite columns为1，Margin为5，之后点击Download进行下载。
5. 下载完成加压后，找到sprite.svg文件用vscode打开。
6. 参照项目里的sprite.svg文件，将下载的内容复制到最后一个图标的下发，修改y的值，每次增加105，之后修改文件第三行的height和viewBox，在最后一个svg的y基础上增加100。
7. 在项目中找到svgsprite.scss文件修改 $spriteHeight : 最新的height;在文件顶部$icons末尾加上新添加的图标名称
8. 用命令进入svgsprite.scss文件所在的位置，执行sass svgsprite.scss svgsprite.css；
9. 接下来执行./bin/brew_css.sh。

# svg修改颜色和大小的引用方式
如果需要使用可以修改颜色和大小的svg雪碧图，可以将icomoon生成的svg雪碧图复制到webroot/public/img/sprite/sprite-xxx.svg中，删除每个svg里的x，y坐标，增加 shape-rendering="geometricPrecision" 使svg雪碧图显示更圆滑。
使用时可以参考coffee4client/components/appSettings.vue
要先引用后使用
<svg viewBox="0 0 100 100" width="16" height="16" style="color: #333;">
  <use :xlink:href="langIco"></use>
</svg>

langIco: '/img/sprite/sprite.svg#ss-language'

注意: 如果修改 webroot/public/img/sprite/sprite-xxxx.svg里的内容需要修改版本号