{
    _id : 房源的uaddr
    addr : 房源地址
    city : 城市
    cmty : 社区
    cnty : 国家
    hist : [该uaddr的估价历史
        {
            _id : 用户id+new date().getTime()
            uid : 用户id
            tp : 评估类型
            bdrms : 卧室数量
            br_plus : 地下卧室
            bthrms : 浴室数量
            gr : 车库数量
            reno : 房屋状况
            sqft : 面积
            front_ft : 沿街宽度
            depth : 深度
            lotsz_code : front_ft和depth的测量单位
            irreg : 形状是否规则
            tax : 税
            excl :
            incl : 对比包括的出售房源id列表
            excl_r :
            incl_r : 对比包括的出租房源id列表
            result : 查找结果
              {
                f : 最小值
                t : 最大值
                p : 结果中间值
                aa : 精确度
                a : aa < 0.05 精确度高
                      aa < 0.1  精确度中等
                      aa >= 0.1 精确度低
                ver : 1.0,
                range : 查找距离范围
                last : 查找时间范围
                rent : 出租相关信息
                  {
                    f : 最小值
                    t : 最大值
                    p : 结果中间值
                    aa : 精确度
                    a : aa < 0.05 精确度高
                          aa < 0.1  精确度中等
                          aa >= 0.1 精确度低
                    ver : 1.0,
                    range : 查找距离范围
                    last : 查找时间范围
                  },
                p1 : VIP经济(roundToK(sale.v1))，其他权限('For VIP user only')
                excl :
                incl : 对比包括的出售房源信息列表
                excl_r :
                incl_r : 对比包括的出租房源信息列表
                max_dist_range : 搜索房源范围(根据房源类型范围不同)
                ok : 估价状态
                histId : 用户id+new date().getTime()
              },
            mlsid : 进入估价的房源ID
            img : 进入估价的房源图片
            dists : 房源距离估价地点的距离
              [
                {
                    id : 房源id
                    d : 距离数值
                }
              ],
            weights : 占比
              [
                {
                    id : 房源id
                    weight : 占比详细信息
                      {
                        wTotal : 31.91,其他项和
                        wRange : 5.3,
                        wBdrms : NumberInt(0),
                        wBr_plus : NumberInt(0),
                        wBthrms : NumberInt(0),
                        wSqft : 0.04,
                        wSld : NumberInt(0),
                        wTs : 19.23,
                        wGr : NumberInt(0),
                        wPrice : 7.34
                    }
                }
              ],
            ts : 估价时间
        }
    ],
    lat : 房源纬度
    lng : 房源经度
    mt : 修改时间
    prov : 省份
    st : 街道
    st_num : 街道号
    tp : 房源类型
    unt : 单元
    users : 在这个位置估价过的用户
      {
        5daea88cca2e8b4f9bc44903 : 用户id
          {
            nm : 用户名
            range : 评估范围
            tp : 评估类型
            ts : 评估时间
            avt : 用户头像
          }
      }
}