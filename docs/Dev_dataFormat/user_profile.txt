user_profile 数据格式

favMap :{
  0 : 收藏夹key(listing.propFav.g的对应字段)
    {
      v : 收藏夹名称
      mt : 最近一次修改时间
      ts : 创建时间
      vip经纪功能
        clnt : 被转发的客户_id(客户信息在data.crm表中)
        cNm : 客户名字
    }
  cntr : number 收藏夹个数(创建的新收藏夹的key)
}

savedSearch : [
  {
    k : 搜索相关条件，与检索时的字段对应，主要用于在后端做处理
      {
        status : A,
        src : mls,
        saletp : sale,
        saleDesc : Sale,
        city : Toronto,
        prov : ON,
        ptype : Residential,
        no_mfee : false,
        sort : auto-ts,
        oh : false,
        soldOnly : true
      },
    r : 搜索相关条件，存有每个条件的具体描述，主要用于在前端做展示
      [
        {
            k : src,
            vv : MLS Listings
        },
        {
            k : saletp,
            vv : For Sale
        },
        {
            k : prov,
            vv : Ontario
        },
        {
            k : city,
            vv : Toronto
        },
        {
            k : ptype,
            vv : Residential
        },
        {
            k : sort,
            vv : Auto
        }
      ],
    ts : 创建时间
    sbscb : 是否推送匹配房源信息
    nm : 保存的检索条件名称
    vip经纪功能(打开推送时，如果有clnt，命中条件的房源会发送给客户)
      clnt : 被转发的客户_id(客户信息在data.crm表中)
  }
]