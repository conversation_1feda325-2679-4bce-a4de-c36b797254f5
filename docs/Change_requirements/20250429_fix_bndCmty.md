# 需求 [fix_bndCmty]

## 反馈

1. Fred反馈bndCmty tag添加存在问题，目前treb优先 其余按照查询取第一个bndCmty，应该改为不同board需要使用不同的src的bndCmty，在使用时进行过滤

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-04-29

## 原因与数据确认

1. 目前bndCmty的tag判断逻辑为: 存在src为'treb'的boundary记录时,使用该记录,如果不存在时使用第一条记录
2. 现有`{tp:'cmty',src:{$ne:null}}`的记录`[ 'BC', 'CLG', 'EDM', 'cornerstone', 'oreb', 'rebgv', 'treb' ]`
3. rebgv的数据需要确认,可能可以忽略

## 解决办法

1. 修改boundary中`{tp:cmty}`的数据,将src改为对应的board(prop.src)
   1. `{BC:BRE,oreb:OTW,treb:TRB,cornerstone:CAR}`
2. 修改文件`src/batch/boundary/importBoundaryV3.coffee`中cmty的src
3. 获取所有的bndCmty记录,保存在`bndCmtys`字段中
4. 从`bndCmtys`中获取评分最高的记录
   1. 对bndCmty的src添加权重`{TRB:50,BRE:40,CAR:40,CLG:40,EDM:40,OTW:40,Other:30}`,如果房源src和bndCmty.src对应,权重添加30
   2. 对bndCmty.name与房源的cmty name相似度添加权重,权重计算方法为: 相似度 * 30
   3. 对bounds范围添加权重,权重计算方法为: 面积排序(面积由小到大排序,权重从1开始依次递减0.1) * 20
   4. 根据权重计算评分,获取最高评分的记录作为bndCmty,并且将评分添加到bndCmty字段中
<!-- 房源bndCmty的历史数据暂时不考虑 -->
<!-- geolib需要升级到3.3.4版本,用于计算多边形面积,该版本在2年前发布,属于稳定版本 -->

## 影响范围

1. 房源的bndCmty获取发生改变

## 是否需要补充UT

1. 需要修改函数`getBoundaryTag`的测试用例

## 确认日期:    2025-04-29

## online-step

1. 升级'geolib', `npm install geolib@3.3.4 --save`
2. 执行mongoshell语句
```shell
db.boundary.updateMany({tp:'cmty',src:'BC'},{$set:{src:'BRE'}})
db.boundary.updateMany({tp:'cmty',src:'cornerstone'},{$set:{src:'CAR'}})
db.boundary.updateMany({tp:'cmty',src:'oreb'},{$set:{src:'OTW'}})
db.boundary.updateMany({tp:'cmty',src:'treb'},{$set:{src:'TRB'}})
```
1. 重启watch&import
2. 重启server
