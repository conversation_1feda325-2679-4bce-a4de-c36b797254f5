# 需求 [log_auaddr_error]

## 反馈

1. fred反馈model/020_geoCoderLocal.coffee里面, 为什么要把对同一个collection的update分成2步，update/update2? saveGeoCache2

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-06-24

## 原因

1. 分两步是因为不影响主更新（update），因为aUaddr是unique的，更新因为种种原因导致Error E11000（多线程，地址mapping重复dup...等），所以分两步， 第一步比较重要，优先更新，第二部可以出错

## 解决办法

1. 记录出错时的参数和数据库状态，为未来修改提供数据
  

## 确认日期:    2024-06-

## online-step

1. 重启watchAndImportToProperties
