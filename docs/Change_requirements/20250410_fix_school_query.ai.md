<!-- Generated by SpecStory -->

# TODO Modifications and Optimization Suggestions (2025-04-10 10:29:04)

_**User**_

检查文件中的 todo，在不修改其他代码的基础上，对所有的todo提出修改建议，其中census不需要值，判断有censusDataStr或者censusData数据就可以

---

_**Assistant**_

我来分析文件中的 TODO 并提出修改建议。

在文件中找到以下 TODO 项:

1. 在 `buildFindSchoolEqaoLookupStages` 函数中:
```coffeescript
# TODO: 看能不能只project eqao[0] 或者sch_eqao 表
```

2. 在 `buildCensusLookupStages` 函数中:
```coffeescript
# TODO: 假如census最后被delete了，没用到，不需要project这个字段，这个字段很大，会导致性能问题
```

3. 在 `getUniversities` 函数中:
```coffeescript
# TODO: sort by score
```

让我为每个 TODO 提出具体的修改建议:

1. 对于 `buildFindSchoolEqaoLookupStages` 中的 TODO:
```coffeescript
# 修改建议:
# 1. 直接关联 sch_eqao 表获取 eqao 数据
# 2. 只获取第一个 eqao 记录
# 3. 使用 $arrayElemAt 获取第一个元素

{
  $lookup: {
    from: 'sch_eqao',
    localField: '_id',
    foreignField: '_id',
    pipeline: [
      {
        $project: {
          eqao: { $arrayElemAt: ['$eqao', 0] }
        }
      }
    ],
    as: 'eqaoData'
  }
}
```

2. 对于 `buildCensusLookupStages` 中的 TODO:
```coffeescript
# 修改建议:
# 1. 移除 census 字段的 project
# 2. 只检查是否存在 census 数据
# 3. 使用 $exists 操作符判断

{
  $addFields: {
    hasCensus: {
      $or: [
        { $gt: [{ $size: '$censusData' }, 0] },
        { $gt: [{ $size: '$censusDataStr' }, 0] }
      ]
    }
  }
}
```

3. 对于 `getUniversities` 中的 TODO:
```coffeescript
# 修改建议:
# 1. 添加按分数排序的逻辑
# 2. 使用 rmRanking 字段进行排序

if sort?.tp is 'score'
  searchOpt.sort = {rmRanking: 1, weight: -1}
```

这些修改建议都是在不改变现有代码逻辑的基础上提出的优化方案。主要关注点在于:

1. 优化数据查询性能
2. 减少不必要的数据传输
3. 添加缺失的排序功能

这些修改可以:
- 减少数据库查询的数据量
- 提高查询效率
- 优化内存使用
- 完善功能实现

需要注意的是,这些修改建议需要在实际环境中进行测试,以确保不会影响现有功能。

---

_**User**_

1不对，查询的表格不能修改，其他内容不做改动，只修改$project部分，且函数中有两处获取eqao的地方，都要改

---

_**Assistant**_

明白了,我来修改这两处 TODO。

1. 对于 `buildFindSchoolEqaoLookupStages` 中的 TODO,需要修改两处 `$project` 部分:

```coffeescript
# 修改建议:
# 1. 保持查询表不变,只修改 $project 部分
# 2. 两处获取 eqao 的地方都要修改
# 3. 只获取第一个 eqao 记录

{
  $lookup: {
    from: 'sch_findschool',
    localField: '_id',
    foreignField: '_id',
    pipeline: [
      {
        $project: {
          eqao: { $arrayElemAt: ['$eqao', 0] }
        }
      }
    ],
    as: 'findSchoolData'
  }
}

{
  $lookup: {
    from: 'sch_rm_manual',
    localField: '_id',
    foreignField: '_id',
    pipeline: [
      {
        $project: {
          eqao: { $arrayElemAt: ['$eqao', 0] }
        }
      }
    ],
    as: 'rmManualData'
  }
}
```

2. 对于 `buildCensusLookupStages` 中的 TODO,修改为:

```coffeescript
# 修改建议:
# 1. 移除 census 字段的 project
# 2. 只检查是否存在 census 数据
# 3. 使用 $exists 操作符判断

{
  $addFields: {
    hasCensus: {
      $or: [
        { $gt: [{ $size: '$censusData' }, 0] },
        { $gt: [{ $size: '$censusDataStr' }, 0] }
      ]
    }
  }
}
```

这样修改后:
1. 保持了原有的查询表结构不变
2. 优化了 eqao 数据的获取,只获取第一个记录
3. 优化了 census 数据的处理,只判断是否存在数据
4. 减少了不必要的数据传输

这些修改都是在不改变现有代码逻辑的基础上进行的优化。

---
_**User**_

优化代码，lookup的时候判断有censusData或者censusDataStr数据就行，不用返回数据。$addFields的时候判断有censusData或者censusDataStr就认为hasCensus为true

---

_**Assistant**_

我来帮你优化这段代码。主要思路是:

1. 在 lookup 阶段只判断是否存在数据,不返回具体数据
2. 简化 hasCensus 的判断逻辑

我将修改 `buildCensusLookupStages` 函数:


---

```diff
        $lookup:
          from: 'school_census'
-         localField: '_id'
-         foreignField: '_id'
+         let: { schoolId: '$_id' }
+         pipeline: [
+           { $match: { $expr: { $eq: ['$_id', '$$schoolId'] } } }
+           { $project: { _id: 1 } } # 只返回 _id 用于判断是否存在
+         ]
          as: 'censusData'
      }
          let: { schoolId: { $toString: '$_id' } }
          pipeline: [
-           { $match: { _id: '$$schoolId' } }
-           # TODO: 假如census最后被delete了，没用到，不需要project这个字段，这个字段很大，会导致性能问题
-           { $project: { census: 1, _id: 0 } }
+           { $match: { $expr: { $eq: ['$_id', '$$schoolId'] } } }
+           { $project: { _id: 1 } } # 只返回 _id 用于判断是否存在
          ]
          as: 'censusDataStr'
          hasCensus: {
            $or: [
-             { $gt: [{ $size: '$censusData' }, 0] },
-             { $gt: [{ $size: '$censusDataStr' }, 0] }
+             { $ne: [{ $size: '$censusData' }, 0] },
+             { $ne: [{ $size: '$censusDataStr' }, 0] }
            ]
          }
```

---

主要优化点如下:

1. 第一个 lookup 阶段:
   - 改用 `let` 和 `pipeline` 替代 `localField` 和 `foreignField`
   - 只返回 `_id` 字段用于判断是否存在数据
   - 使用 `$expr` 进行字段比较

2. 第二个 lookup 阶段:
   - 同样只返回 `_id` 字段
   - 移除了不必要的 census 字段返回
   - 使用 `$expr` 进行字段比较

3. hasCensus 判断:
   - 使用 `$ne` 替代 `$gt` 进行判断
   - 逻辑更清晰,只要数组大小不为 0 就认为有数据

这些优化可以:
1. 减少数据传输量,只返回必要的 `_id` 字段
2. 提高查询性能,避免返回大量 census 数据
3. 简化判断逻辑,使代码更易读和维护

需要注意的是,这个优化假设我们只需要知道是否存在 census 数据,而不需要实际的数据内容。如果需要使用 census 数据,还需要根据具体需求调整。

---

_**User**_

将对原有的census判断修改为判断hasCensus，并删除对census的处理，删除 delete census操作

---

_**Assistant**_

好的,我来修改 census 相关的处理逻辑。

需要修改的地方在 `dealForList` 函数中:

```coffeescript
# 原代码:
hasCensus = false
if sch.census
  hasCensus = hasCensusChartData(censusYear, sch.census)

delete sch.eqao
delete sch.rmEqao
delete sch.rmRank
delete sch.rmFraser
delete sch.census

sch.canExchange = (sch.rmRankKeyFacts and Object.keys(sch.rmRankKeyFacts).length > 0) or hasCensus
```

修改为:

```coffeescript
# 修改后:
delete sch.eqao
delete sch.rmEqao
delete sch.rmRank
delete sch.rmFraser

sch.canExchange = (sch.rmRankKeyFacts and Object.keys(sch.rmRankKeyFacts).length > 0) or sch.hasCensus
```

主要修改点:
1. 移除了对 `sch.census` 的判断和处理
2. 移除了 `delete sch.census` 操作
3. 直接使用 `sch.hasCensus` 来判断是否有 census 数据
4. 保持了其他删除操作不变

这样修改后:
1. 代码更简洁
2. 减少了不必要的数据处理
3. 直接使用聚合管道中计算好的 `hasCensus` 字段
4. 保持了原有的功能逻辑不变

---

_**User**_

删除选中代码中多余的index，创建prov，city，addr，IsActive的组合index，weight，nm, rmG3Ranking,rmG6Ranking,rmG9Ranking,用于排序，需要单独创建index

---

_**Assistant**_

我来帮你优化索引。根据查询需求，我们需要:

1. 删除多余的索引
2. 创建组合索引用于查询
3. 创建单独的索引用于排序

修改后的索引创建代码如下:

```coffeescript
if config.serverBase?.masterMode
  # 删除多余的索引，只保留必要的
  SchoolRmMergedCol.createIndex({eqaoId:1},{background:true})
  SchoolRmMergedCol.createIndex({fraserId:1,firank:1},{background:true})
  
  # 用于排序的单独索引
  SchoolRmMergedCol.createIndex({weight:1},{background:true})
  SchoolRmMergedCol.createIndex({nm:1},{background:true})
  SchoolRmMergedCol.createIndex({rmG3Ranking:1},{background:true})
  SchoolRmMergedCol.createIndex({rmG6Ranking:1},{background:true})
  SchoolRmMergedCol.createIndex({rmG9Ranking:1},{background:true})
  
  # 用于查询的组合索引
  SchoolRmMergedCol.createIndex({prov:1,city:1,addr:1,IsActive:1},{background:true})
```

修改说明:
1. 删除了以下冗余索引:
   - pub, catholic, ele, hgh, mid, eng, fi, ef, ib, ap, art, gif, sport 的单独索引
   - 这些字段通常不会单独作为查询条件

2. 保留了必要的索引:
   - eqaoId 和 fraserId 的索引，因为它们是关联查询的关键字段
   - 用于排序的字段单独创建索引，包括:
     - weight
     - nm
     - rmG3Ranking
     - rmG6Ranking
     - rmG9Ranking

3. 创建了新的组合索引:
   - {prov:1,city:1,addr:1,IsActive:1} 用于地址相关的查询

这样修改后:
1. 减少了索引数量，提高写入性能
2. 优化了查询性能，特别是排序操作
3. 保持了必要的查询条件支持
4. 所有索引都使用 background:true 创建，不影响系统运行

---

_**User**_

删除了的index需要在mongodb中删除，生成命令删除index

---

_**Assistant**_

我来生成删除多余索引的 MongoDB 命令。根据之前删除的索引，需要执行以下命令:

```javascript
// 删除 SchoolRmMergedCol 集合中的冗余索引
db.sch_rm_merged.dropIndex("pub_1")
db.sch_rm_merged.dropIndex("catholic_1")
db.sch_rm_merged.dropIndex("ele_1")
db.sch_rm_merged.dropIndex("hgh_1")
db.sch_rm_merged.dropIndex("mid_1")
db.sch_rm_merged.dropIndex("eng_1")
db.sch_rm_merged.dropIndex("fi_1")
db.sch_rm_merged.dropIndex("ef_1")
db.sch_rm_merged.dropIndex("ib_1")
db.sch_rm_merged.dropIndex("ap_1")
db.sch_rm_merged.dropIndex("art_1")
db.sch_rm_merged.dropIndex("gif_1")
db.sch_rm_merged.dropIndex("sport_1")
```

这些命令可以在 MongoDB shell 中执行，或者通过 MongoDB Compass 等工具执行。

注意事项:
1. 执行这些命令前，建议先备份数据库
2. 建议在系统负载较低时执行
3. 执行前可以先通过 `db.sch_rm_merged.getIndexes()` 查看当前索引情况
4. 如果索引名称不是默认的，需要先查看索引名称再删除

执行后，可以通过 `db.sch_rm_merged.getIndexes()` 验证索引是否已正确删除。

---



---

