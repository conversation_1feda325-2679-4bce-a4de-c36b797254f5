# 需求 [fix_trebResoDownload]

## 反馈

1. <PERSON>反馈N12128683应该有2个openhouse，确只显示了一个

## 需求提出人:    Sophie

## 修改人：       Maggie

## 提出日期:      2025-05-08

## 原因与数据确认

1. 在openhouse表中，有2个openhouse： db.reso_treb_evow_openhouse.find({"ListingKey":'N12128683'})
[
  {
    _id: 'N12128683-1',
    ListingKey: 'N12128683',
    ModificationTimestamp: '2025-05-08T23:40:44Z',
    OpenHouseDate: '2025-05-10',
    OpenHouseEndTime: '2025-05-10T20:00:00Z',
    OpenHouseId: '286078',
    OpenHouseKey: 'N12128683-1',
    OpenHouseStartTime: '2025-05-10T18:00:00Z',
    OpenHouseStatus: 'Active',
    OpenHouseType: 'Public',
    OriginalEntryTimestamp: '2025-05-08T23:40:44Z',
    _mt: ISODate('2025-05-08T23:44:50.483Z'),
    ts: ISODate('2025-05-08T23:44:50.483Z')
  },
  {
    _id: 'N12128683-2',
    ListingKey: 'N12128683',
    ModificationTimestamp: '2025-05-08T23:40:45Z',
    OpenHouseDate: '2025-05-11',
    OpenHouseEndTime: '2025-05-11T20:00:00Z',
    OpenHouseId: '286081',
    OpenHouseKey: 'N12128683-2',
    OpenHouseStartTime: '2025-05-11T18:00:00Z',
    OpenHouseStatus: 'Active',
    OpenHouseType: 'Public',
    OriginalEntryTimestamp: '2025-05-08T23:40:45Z',
    _mt: ISODate('2025-05-08T23:44:50.516Z'),
    ts: ISODate('2025-05-08T23:44:50.516Z')
  }
]
但merged中只有一个：
openHouse: [
      {
        tp: 'Public',
        status: 'Active',
        f: '2025-05-11T18:00:00Z',
        t: '2025-05-11T20:00:00Z',
        key: 'N12128683-2'
      }
    ]
而且本地无法复现。
考虑可能是在mergeResourceIntoProperty中，会获取prop，由于 MongoDB 的复制延迟或其他原因，第二次读取可能还没有看到第一次的更新，所以只更新了一个。
之前遇到TRBC12100287 media部分更新，也符合这种情况。


## 解决办法

1. findOne使用linearizable，writeConcern: { w: "majority" }

## 影响范围

1. trebResoDownload

## 是否需要补充UT

1. 不需要

## 确认日期:    2025-05-09

## online-step

1. 重启trebResoDownload
