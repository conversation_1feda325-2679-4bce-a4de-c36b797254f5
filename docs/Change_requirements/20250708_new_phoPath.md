# 需求 [new_phoPath]

## 反馈

1. Rain反馈coffee实现gofile.levelstore的相关l1,l2功能，能pass UT
2. 后端增加判断，如果有新字段tnLH/phoLH，转换为phourls:[https://img.realmaster.com/mls/[TRB|BRE|...]/[L1]/[L2]/[id]_[hash].[ext]]
3. 首页优先显示有图的房源。 和增加开关，如果第三方有图，但是我们自己没下载完成，是否用第三方图的开关，这个不着急。先做1和2

## 需求提出人:    Rain

## 修改人：       Luoxiaowei

## 提出日期:      2025-07-08

## 原因

1. MLS房源图片提出需要下载图片需求,Effective immediately, to better serve all users, Vendors/Members will again be required to download the photos from the API for use on their websites. We thank you in advance for your cooperation.

## 解决办法

1. 在appweb项目中实现gofile中的`getFullFilePathForProp`功能。
2. 房源图片列表生成时,优先判断tnLH/phoLH字段,使用新的生成逻辑生成。
3. esWatch中添加`tnLH`字段
4. 修改mapping,使用rni的ts字段

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-07

## online-step
因为修改为使用phoLH，tnLH，docLH
需要重启：
1. trebResoDownload
2. carDownload
3. raeDownload


1. 安装`crc-32`依赖,`npm install crc-32`
2. 重启watch&import
3. 重新init es
4. 重启server
