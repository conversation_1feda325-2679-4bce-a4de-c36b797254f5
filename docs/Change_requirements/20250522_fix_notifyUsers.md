
# 需求 [fix_notifyUsers]

## 反馈

ERROR:cmate3.coffee,model/sysNotify.coffee,2025-05-22T17:37:46.414
 model/sysNotify.coffee notifyUsers 274 RangeError [ERR_OUT_OF_RANGE]: The value of "offset" is out of range. It must be >= 0 && <= 17825792. Received 17825794
    at validateOffset (node:buffer:122:3)
    at Buffer.write (node:buffer:1105:5)
    at Object.encodeUTF8Into (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:175:58)
    at serializeString (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3065:44)
    at serializeInto (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3438:25)
    at serializeObject (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3232:22)
    at serializeInto (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3666:25)
    at serializeObject (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3232:22)
    at serializeInto (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3666:25)
    at serializeObject (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3232:22)
    at serializeInto (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3666:25)
    at serializeObject (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3232:22)
    at serializeInto (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3465:25)
    at serializeObject (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3232:22)
    at serializeInto (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:3666:25)
    at Object.serialize (/opt/rmappweb/src/node_modules/bson/lib/bson.cjs:4048:32)
    at Msg.serializeBson (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/commands.js:383:21)
    at Msg.makeDocumentSegment (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/commands.js:377:37)
    at Msg.toBin (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/commands.js:366:29)
    at MessageStream.writeCommand (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/message_stream.js:43:34)
    at write (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection.js:479:30)
    at Connection.command (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection.js:312:13)
    at /opt/rmappweb/src/node_modules/mongodb/lib/sdam/server.js:206:18
    at Object.callback (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection_pool.js:321:13)
    at ConnectionPool.processWaitQueue (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection_pool.js:543:33)
    at /opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection_pool.js:180:37
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
 {
  code: 'ERR_OUT_OF_RANGE'
}


## 需求提出人:    Rain

## 修改人：      liurui

## 提出日期:     2025-05-23

## 原因
PnToken.findToArray 数据量过大（184737）

## 解决办法

1. notifyUsers添加对pn_token表查询数据的限制

## 确认日期:    2025-05-23



## Online step

重启server
