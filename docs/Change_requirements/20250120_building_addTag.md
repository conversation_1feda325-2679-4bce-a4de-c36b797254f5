# 需求 [building_addTag]

1. 后台building编辑页面增加对Tag的编辑功能。tag的数据结构支持可筛选查询。
3. 是否可以从房源详情页的 Remark 和 Details卡片的 Detail内容中获取builging的特征：根据地址获取比如近3年同一个bugilding的不同房间信息，当房源样本数足够大时比如100个，那么对于pool这个特征在30-40个房间信息里出现，则可以认为是真实的；当样本数据过小时，对于pool这个特征必须过半的房源信息中出现才认为可信；按此办法获取到每个building的tags；该程序比如1年跑一次进行building tag的更新。

## 需求提出人:   Tao，Allen

## 修改人：      zhangman

## 提出日期:     2024-10-17

## 解决办法

首先查看目前项目中，condos表里有 amen 和 amen_extra，这两个字段的来源是通过跑 batch/condos/condos.coffee batch 从 properties 表里的 amen 字段在经过处理获的；properties 表里的 amen 数据从import房源信息时获得; 
在condos表里对于 amen 处理：根据 uaddr 和 mt 倒序查询 1000 条数据，在将数据里 amen 的每一个特征统计出现次数，使用第一个的cnts作为 heighties 次数，当其他的特征次数大于等于 heighties/2, 认为可信。

完成需求三可以分为三个部分：
1. 先根据第三方给回来的房源数据中包含有building特征的字段，对数据进行整合处理；查找一年/两年内同一uaddr下的building数据获取特征数据，统计每个单词在样本数据中出现的频率，计算可信度。可信度公式：单词出现的样本数 / 样本总数；设置一个可信度阈值。根据单词的可信度与阈值比较，决定是否保留该单词

2. 从 Details 信息卡片中查找相应字段，对数据进行标准化处理后，采用算法进行筛选；(details里的字段也是1里面的数据)

3. 采用大语言模型等从 Reamrk 中获取 building 特征，对数据进行统一化处理后，按照算法对其进行筛选；

不同房源的字段名称：
1. OTW: Amenities
2. CLG: AssociationAmenities
3. CAR: Amenities
4. BCRE: AssociationAmenities
5. TREB: AssociationAmenities
6. EDM: 没有

其他字段是否也算需要确认：
CommunityFeatures，InteriorFeatures, ExteriorFeatures

目前阶段先完成需求1和需求3的第1部分。

请参考 @02_complexLatLng.coffee和@condos.coffee 文件，按照 @02_complexLatLng.coffee和@condos.coffee 文件的格式，为我写一个新的batch。要求如
下：
功能需求：
  - 跑该batch的命令中包含-d参数，用来表示查找几年的数据；
  - 创建一个新表叫condo_tags；
  - 使用mongo语句查询properties表，查询条件: 1.根据参数d查找近多少年的数据。2.查找ptype2:['Apartment']。3.根据uaddr字段进行分组，统计每个组里有多少个数据count。4.获取amen数组字段数据，和统计数据count；
  - 对查询回来的每条数据进行如下处理：遍历amen数据，统计相同单词出现的频率；计算可信度（出现频率 / 该条数据的count字段）；根据可信度进行排序，找到可信度最大的数据，将其除以2作为阈值，输出大于等于该阈值的数据，保存到一个数组字段；
  - 使用mongo更新语句，将处理后得到的数组字段保存到condo_tags,_id是uaddr，如果根据uaddr没有查询到该条数据则进行插入；

## 确认日期:    2025-01-24

