# 需求 [fix_media]

## 反馈

1. BRER2975829没有图，在rni中没有生成phoUrls，是4月4号phoUrls被删除了

## 需求提出人:    Fred/Rain

## 修改人：       Maggie

## 提出日期:      2025-04-15

## 原因

1. bcreResoDownload在运行downloadQueuedResources时候，运行mergeResourceData会导致再次检查Media，因为压缩Media后会删除该字段，导致这里没有Media这个字段，直接unset了


## 解决办法

1. 增加判断，如果没有mergedDoc.Media，说明之前处理过了，就不再运行processMedia()。
2. batch 修复bcre，crea数据
3. creaResoDownload, trebResoDownload增加判断，只有当不是Queue的时候，才做重复判断直接忽略处理

## 影响范围

1. bcreResoDownload, creaResoDownload, trebResoDownload

## 是否需要补充UT

1. 不需要添加UT

## 确认日期:    2025-04-16

## online-step

1. 重启bcreResoDownload，creaResoDownload, trebResoDownload
2. 运行batch
```
 ./start.sh -n fixCreaBcrePhoUrls -cmd "lib/batchBase.coffee batch/import/fixCreaBcrePhoUrls.coffee dryrun"

 测试服：
 All collections completed. Total process time 0.29095 mins BCREProcessed(14044):48.26K/m, BCRENoMedia(10023):34.44K/m, BCREDryrun(4021):13.82K/m, CREAProcessed(4865):16.72K/m, CREADryrun(4045):13.90K/m, CREANoMedia(820):2.818K/m

```