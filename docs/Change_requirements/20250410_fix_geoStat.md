# 需求 [fix_geoStat]

## 反馈

1. Fred反馈geo_coding_stat表是不是应该用time series？需要区分房源src,engine,统计个数和geoq平均。应该可以用src或者engine去query。现在的_id 统计和作sum都比较复杂。

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-04-02

## 原因

## 解决办法

1. 添加geo_stat的time series表,格式为 {ts,metadata:{engine,src},id,geoq,err,noRs},granularity(minutes),expireAfterSeconds(一年过期)
```JSON
{
    "_id" : ObjectId("67fcd1e280b58b3fe7d03339"),
    "ts" : ISODate("2025-04-14T09:14:10.662+0000"),
    "metadata":{
        "engine" : "bing",
        "src" : "DDF"
    },
    "id" : "DDF28043373",
    "geoq" : NumberInt(100),
    "_mt" : ISODate("2025-04-14T09:14:10.662+0000")
}
```
<!-- 使用engine和src作为metadata时,可以提高统计engine的总量,或者根据engine和src进行统计的效率以及优化存储 -->
2. geo_coding_stat的保存转为使用geo_stat保存
3. 修改rmconfig,项目启动时如果没有geo_stat创建time series的geo_stat表
4. 修改batchBase,在加载model之前先set_predef_colls

## 影响范围

1. 弃用geo_coding_stat,geocoding的统计改用新的time series表(geo_stat)
2. 改用时序表geo_stat后,会导致geo的统计数据量比以前大(每次进行geocoding时会插入一条数据)

## 是否需要补充UT

1. 不需要补充UT

## AI使用统计

## 确认日期:    2025-04-14

## online-step

1. 更新rmconfig(https://github.com/real-rm/rmconfig/pull/14)
2. 重启server
3. 重启watch&import
