# 需求 [fix_bcre_his]

## 反馈
1. bcre reso 数据：fixHisBCRE 修改的log顺序不对

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-02-11

## 原因
1. 在fixHisBCRE中，filterDuplicateHis 为了保留新的正确的his，对his做了reverse，后通过ts排序。 没有考虑到有些his的ts相同，顺序会反，比如new和addpic。

## 解决办法
1. 修改fixHisBCRE中filterDuplicateHis，多个重复的只保留最后一个，不修改顺序。
2. 修改最开始筛选，只处理在开始时间之后和修复时间之前，有1条以上log的记录。
3. 有些旧数据prop已经sold，新的reso还会进来pnd-》closed-》pnd，如BRER2750308，这些不修改his。

## 确认日期:    2025-02-11

## 测试
Total process time 68.45578333333333 mins.  processed(11951):174.5/m, noChangedHis1(5046):73.71/m, successProcess(11951):174.5/m, noChangedHis0(1892):27.63/m, oldSoldSkip(8):0.116/m, dryHisUpdate(4730):69.09/m, sameHisSkip(270):3.944/m, propNoHis(2):0.029/m, convertFailed(3):0.043/m
#skip import BRER2956163 [{ fld: 'lp || lpr', msg: 'price:2500000 is not in range for Lease' }]
#skip import BRER2956239 [{ fld: 'lp || lpr', msg: 'price:2700000 is not in range for Lease' }]
#skip import BRER2949899 [{ fld: 'lp || lpr', msg: 'price:6500 is not in range for Sale' }]

## online-step
0. 先停止pushNotify，待修复完成后再启动。
1. 执行batch fixHisBCRE
```
  ./start.sh -n fixHisBCRE -cmd "lib/batchBase.coffee batch/prop/fixHisBCRE.coffee preload-model dryrun"
```
