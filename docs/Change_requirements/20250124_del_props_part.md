# 需求 [del_props_part]

## 反馈

1. Fred反馈props_part可以转用elasticsearch
2. Fred提出需求,去除properties中多余的索引

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON> xiaowei

## 提出日期:     2025-01-24

## properties表索引

| index_key | index_name | remark |
|-----------|------------|---------|
| { _fts: 'text', _ftsx: 1 } weights: { addr: 1 } | addr_text | es时不会使用, mongo时error log, 删除索引 |
| { _id: 1 } | _id_ | |
| { _mt: -1 } | _mt_-1 | 在使用,保留 |
| { _sysexp: 1 } | _sysexp_1 | rm 房源 过期字段,保留 |
| { 'bndCmty.nm': 1, mt: -1 } | bndCmty.nm_1_mt_-1 | src/batch/tag/addTagToStatUname.coffee 执行时添加索引,保留 |
| { 'favU.u': -1 } | favU.u_-1 | 不再使用,可以删除 |
| { 'favU.u': 1 } | favU.u_1 | 不再使用,可以删除 |
| { 'la._id': 1, status: 1, mt: -1 } | la._id_1_status_1_mt_-1 | @Me','@Branch','@Company'查询使用,需要确认现在是否还在使用 |
| { 'la.agnt._id': 1 } | la.agnt._id_1 | /1.5/wesite/list(type:'fea'),/1.5/wesit/listCount,'@Me'查询使用,需要确认现在是否还在使用 |
| { 'la.agnt._id': 1, status: 1, mt: -1 } | la.agnt._id_1_status_1_mt_-1 | /1.5/wesite/list(type:'fea'),/1.5/wesit/listCount,'@Me'查询使用,需要确认现在是否还在使用 |
| { 'la.agnt.bid': 1 } | la.agnt.bid_1 | 目前没有使用,可以删除 |
| { 'la.agnt.id': 1 } | la.agnt.id_1 | /1.5/wesite/list(type:'fea'),/1.5/wesit/listCount,'@Me'查询使用,需要确认现在是否还在使用 |
| { 'la2.agnt._id': 1 } | la2.agnt._id_1 | /1.5/wesite/list(type:'fea'),/1.5/wesit/listCount,'@Me'查询使用,需要确认现在是否还在使用 |
| { bid: 1, status: 1, mt: -1 } | bid_1_status_1_mt_-1 | bid查询有使用 |
| { bm: 1 } | bm_1 | 未使用,可以删除 |
| { city: -1 } | city_-1 | 删除，使用组合索引 |
| { city: 1, prov: 1, ptype: 1, saletp: 1, ts: -1 } | city_1_prov_1_city_1_ptype_1_saletp_1_ts_-1 | 保留，考虑与其它复合索引优化 |
| { cmty: 1, mt: -1 } | cmty_1_mt_-1 | 不再使用,可以删除 |
| { ddfID: 1 } | ddfID_1 | 手动查找时使用，保留 |
| { favU: 1 } | favU_1 | 不再使用,可以删除 |
| { geoq: 1 } | geoq_1 | 删除，考虑复合索引 |
| { id: 1 } | id_1 | 查询有使用,保留 |
| { loc: '2dsphere' } | loc_2dsphere | 查询有使用,保留 |
| { merged: 1 } | merged_1 | 查询有使用,保留 |
| { mergedTop: 1 } | mergedTop_1 | 查询有使用,保留 |
| { mt: -1 } | mt_-1 | 排序有单独使用,保留 |
| { mt: -1, spcts: -1 } | mt_-1_spcts_-1 | 保留 |
| { onD: 1 } | onD_1 | 排序有单独使用,保留 |
| { owner: 1 } | owner_1 | 未使用,可以删除 |
| { prov: 1 } | prov_1 | 单索引删除，考虑复合索引 |
| { ptype: 1, src: 1, prov: 1, city: 1, offD: 1 } | ptype_1_src_1_prov_1_city_1_offD_1 | 32，33 考虑合并 |
| { ptype: 1, src: 1, prov: 1, city: 1, onD: 1 } | ptype_1_src_1_prov_1_city_1_onD_1 | 32，33 考虑合并 |
| { ptype: 1, src: 1, prov: 1, city: 1, ptype2: 1, cmty: 1 } | ptype_1_src_1_prov_1_city_1_ptype2_1_cmty_1 | 保留 |
| { ptype: 1, src: 1, prov: 1, city: 1, sldd: 1 } | ptype_1_src_1_prov_1_city_1_sldd_1 | 保留 |
| { ptype2: 1, mfee: 1, geoq: 1 } | ptype2_1_mfee_1_geoq_1 | 存在联合使用，但是使用的es，可以删除 |
| { rmmt: -1 } | rmmt_-1 | watchMongo在迁移时使用,不使用props_part时可以删除 |
| { rmmt: 1 } | rmmt_1 | 未使用,可以删除 |
| { schs: 1, status: 1 } | schs_1_status_1 | 未使用,可以删除 |
| { seller: 1 } | seller_1 | 未使用,可以删除 |
| { sid: 1 } | sid_1 | 有使用，保留 |
| { slddt: 1 } | slddt_1 | 不再使用,可以删除 |
| { spcts: -1, mt: -1 } | spcts_-1_mt_-1 | 保留mt_-1_spcts_-1,删除当前索引 |
| { src: 1, ddfID: 1, _id: 1 } | src_1_ddfID_1__id_1 | 手动查询时使用,保留 |
| { topTs: -1 } | topTs_-1 | 确认是否仅有排序，考虑删除 |
| { ts: -1 } | ts_-1 | 排序有单独使用,保留 |
| { uaddr: 1 } | uaddr_1 | 存在复合索引，可以删除 |
| { uaddr: 1, onD: -1 } | uaddr_1_onD_-1 | 保留 |
| { uaddr: 1, onD: 1 } | uaddr_1_onD_1 | 删除 |
| { uid: 1 } | uid_1 | 查询有使用，确认是否可以复合索引 |
| { zip: 1, mt: -1 } | zip_1_mt_-1 | 不再使用，可以删除 |

## props_part使用修改说明

1. 估价, file: src/apps/80_sites/AppRM/propRelated/evaluation.coffee      改用ES
2. dailyNotify, file: src/batch/dailyNotify.coffee                       unused,已修改文件名
3. mongoWatch, file: src/batch/prop/watchPropAndUpdateMongo.coffee       不用mongoWatch时可以取消,暂时不用修改
4. deleteUser, file: src/model/010_user.coffee,                          已经去掉
5. findNearByListings, file: src/model/properties.coffee                 改用ES
6. countPartialEvalNearbyProp, file: src/model/properties.coffee         改用ES
7. countUserListings, file: src/model/properties.coffee                  改用ES
8. findFeatureListings, file: src/model/properties.coffee                改用ES
9. revokeListingMarket, file: src/model/properties.coffee                去掉props_part
10. getTopUpListingsByCity, file: src/model/properties.coffee            改用ES
11. findListingsByUserFavs, file: src/model/properties.coffee            改用ES (调用的edmProperty下的getUpdatedPropertyEdm函数未在使用)
12. findListingsForEdm, file: src/model/properties.coffee                没有使用,已删除
13. findListingsForEvaluationEdm, file: src/model/properties.coffee      edmNotify的通知不再使用evaluation参数(估价的收藏保存在evaluation表)
14. 效率对比测试, file: src/stats/propSearchCompare.coffee                 unused，已修改文件名

## 解决办法

1. 添加batch 删除properties索引
2. 将使用props_part的地方,根据情况改为es/properties查询

## 确认日期:    2025-01-24

## online-step

1. 重启server
2. 执行batch删除properties中多余的index
```shell
./start.sh -n removePropIndexes -cmd "lib/batchBase.coffee batch/prop/remove_propIndexes.coffee dryrun"

结果: Index removed, processed 25, removed 25, total process time 0.0001 mins.
```
