<!-- Generated by SpecStory -->

# 文件修改与需求实现 (2025-04-09 16:07:09)

_**User**_

按照需求文档和解决办法，修改对应文件

---

_**Assistant**_

I'll help you implement the changes according to the requirements document. Let's break this down into two parts:

1. First, let's modify `reImportProp.coffee` to add the business parameter and related logic
2. Then, we'll modify `trebResoDownload.coffee` to handle the status update when no property is returned

Let's start with `reImportProp.coffee`:


---

```diff
        -d --date:        query date gte ts, format YYYY-MM-DD
        -a --days:        query days ago, number
+       -b --business:    if set, query business properties
    New config:
-     ./start.sh -t batch -n reImportProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee preload-model dryrun -d 2024-01-01 -a [days]"
+     ./start.sh -t batch -n reImportProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee preload-model dryrun -d 2024-01-01 -a [days] -b"
    createDate:    2024-02-21
    Author:        xiaowei<PERSON>uo
  yargs.option 'date', { alias: 'd', description: 'query date gte rmmt,formate:YYYY-MM-DD', required: false, type: 'string'}
  yargs.option 'days', { alias: 'a', description: 'query days ago', required: false, type: 'number'}
+ yargs.option 'business', { alias: 'b', description: 'if set, query business properties', required: false, type: 'boolean'}
  
  START_DATE = yargs.argv.date
  DAYS = yargs.argv.days
+ IS_BUSINESS = yargs.argv.business
  dryRun = AVGS.indexOf('dryrun') >= 0
  NOW = new Date()
      onD:{$lte:inputToDateNum(queryDays)},
      status:'A',
-     ptype:'r',
+     ptype: if IS_BUSINESS then 'b' else 'r',
      src:{$ne:'RM'},
      mt:{$lte:queryDays}
    }
+   if IS_BUSINESS
+     query.src = 'TRB'
+     queryDays = new Date(NOW - 100 * 24 * 3600 * 1000) # 100 days for business properties
+     query.mt = {$lte:queryDays}
    if START_DATE
      query.rmmt = {$gte:new Date(START_DATE)}
            speedMeter.check {notAddToQueue:1}
            debug.warn "prop:#{prop._id} is same to rni, but prop.src is not 'TRB','BRE','DDF'"
-           return cb()
+           return cb()
+ 
+         # For business properties, check TRBRniCol first
+         if IS_BUSINESS
+           trbRniRecord = await TRBRniCol.findOne {_id: prop._id}
+           if not trbRniRecord
+             debug.info "Business property not found in TRBRniCol, setting status to U", prop._id
+             if not DRYRUN
+               await PropertiesCol.updateOne {_id: prop._id}, {$set: {status: 'U'}}
+             speedMeter.check {businessStatusUpdated:1}
+             return cb()
  
          queueId = null
```

---

Now let's modify `trebResoDownload.coffee` to handle the status update when no property is returned:



---

```diff
      if resoReturn.value.length < resoParams.$top
        debug.info "there is no return for these properties", recordsLeft
+       # Update status to 'U' for remaining records that were not returned
+       if recordsLeft.length > 0
+         debug.info "Updating status to 'U' for #{recordsLeft.length} properties not found in RESO"
+         for record in recordsLeft
+           await PropertiesCol.updateOne(
+             {_id: record._id},
+             {$set: {status: 'U'}}
+           )
        queryLog.end logKey, {isDone: true, updatedTotal, returnedCountTotal}
        await queue.removeBatchFromQueue(records)
```

---

I've made the following changes to implement the requirements:

1. In `reImportProp.coffee`:
   - Added a new `-b/--business` command line option
   - Modified the query to use `ptype:'b'` and `src:'TRB'` when business mode is enabled
   - Set query days to 100 days for business properties
   - Added logic to check TRBRniCol first for business properties and set status to 'U' if not found

2. In `trebResoDownload.coffee`:
   - Modified `getAndSavePropertiesByQueue` to update status to 'U' for any properties that were not returned from the RESO API
   - Added logging to track these status updates

The changes implement both requirements from the document:
1. For business properties, we now check TRBRniCol first and set status to 'U' if not found
2. When no property is returned from RESO API, we update the status to 'U'

The changes maintain the existing functionality while adding the new business property handling logic. All error handling and logging are preserved, and the code follows the established patterns in both files.

Would you like me to explain any part of the changes in more detail?

---



---

