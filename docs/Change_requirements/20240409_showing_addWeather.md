### 需求 [showing_addWeather]
原话：查一下调取天气数据的open api，想将天气信息加入到看房管理里面，即选了第一个房源和时间，就自动把天气信息显示到showing顶部，可以先显示：温度，天气，UV，湿度。我们天气显示部分，如果预约的时间超过48小时就不要显示了。就在48小时以内的，在showing保存的时候自动添加就行。如果有更新用最新的。

需求设计：weather设计为一个单独的接口，在showing点击保存或者打开showing时进行时间，房源是否预约等条件进行判断，满足条件调weather接口，显示weather信息。详细参考：docs/Dev_weather/weather.md
## 需求提出人:    Fred
## 修改人:       zhang man

## 提出日期
1. 2024-03-14提出需求

## 解决办法
1. weather单独设计一个接口进行weather相关部分的处理

## 需求确认日期
1. 2024-04-10 