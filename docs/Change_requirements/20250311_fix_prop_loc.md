# 需求 [fix_prop_loc]

## 反馈

1. fred反馈相似房源查找存在error log
ERROR:cmate3.coffee,apps/80_sites/AppRM/propRelated/similarProp.coffee,2025-02-03T19:25:47.81
 apps/80_sites/AppRM/propRelated/similarProp.coffee processTicksAndRejections 222 Error: no location info,params: {
  page: 0,
  limit: 50,
  id: 'TRBN1864631',
  type: 'House,Detached',
  month: '',
  saleDesc: 'Sold',
  saletp: 'Sale',
  offset: 0,
  mlsid: 'TRBN1864631',
  lp: 659900,
  gr: 2,
  bdrms: 4,
  bthrms: 3,
  br_plus: undefined,
  sqft: 2750
}

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2025-03-11

## 原因

1. 房源TRBN1864631不存在lat/lng/loc信息
2. 在测试服统计，存在以下几种没有lat/lng的房源数据
   1. out of area房源,lat/lng/loc都不存在(属于正常情况)
   2. 非out of area房源,lat/lng/loc都不存在，uaddr在geo_cache中也没有记录(未找到具体原因,可能和batch设置noGeo有关)<!-- Rain：垃圾数据目前先保留 -->
   3. 非out of area房源,lat/lng/loc都不存在，uaddr在geo_cache中存在记录(本地import未复现)
   4. 存在loc,但是没有lat/lng信息(未复现原因,大多为历史数据)

## 方案

1. 添加batch修复存在loc/geo_cache的记录的历史数据
2. 对于没有geo_cache记录,loc信息的记录暂时进行warning log,重新import可能会造成geoCoding次数超出限制

## 确认日期:    2025-03-11

## online-step

1. 执行batch,修复数据
```shell
./start.sh -n fix_prop_loc -cmd "lib/batchBase.coffee batch/prop/fix_prop_loc.coffee dryrun"
测试服结果
Completed, Total process time 9.551833333333333 mins, processed(203072):21.26K/m, hasLoc(25285):2.647K/m, dryRun(51672):5.409K/m, noGeo(150451):15.75K/m, hasGeo(26387):2.762K/m, OutOfArea(949):99.35/m
```
