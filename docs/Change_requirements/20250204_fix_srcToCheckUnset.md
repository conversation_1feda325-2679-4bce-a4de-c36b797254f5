# 需求 [fix_srcToCheckUnset]

## 反馈

1. Sales反馈房源TRBN11938641户型错了，不是3+1

## 需求提出人:   ZhangLin

## 修改人：      Maggie

## 提出日期:     2025-02-04

## 原因

1. saveToMaster.coffee 下面的srcToCheckUnset变量没有支持reso,bcreReso等source，导致不会检查需要unset的字段
   
## 解决办法

1. 将doMapping函数放到impCommon.coffee中，其他mapping都引用impCommon.coffee中的doMapping函数。
2. 修改impMappingRESO.coffee，impMappingBcreReso.coffee，impMappingCar.coffee，impMappingEdm.coffee，impMappingCreb.coffee中的doMapping函数，将fieldMap作为参数传入。
3. 修改saveToMaster.coffee，在srcToCheckUnset中添加creb,edm,car,reso,bcreReso source。
4. 添加batch，修复错误数据：检索properties表中，status是'A'的房源，根据src从rni中查找对应的record，然后使用convertAndSaveRecordAsync reImport数据。


## 确认日期:    2025-02

## 测试
### Performance Metrics
- Total process time: 24.27 mins
- Processed: 156,759 properties (6.458K/min)
- Updated: 154,940 properties (6.383K/min)
- BRE Deleted: 139 properties (5.726/min)
- Skip BCRE: 19 properties (0.782/min)
- Skip TREB: 1,661 properties (68.42/min)




## online-step

1. 重启watch&import
2. 重启server
3. 执行batch/prop/fixSrcToCheckUnset
   ```
   ./start.sh -n fixSrcToCheckUnset -cmd "lib/batchBase.coffee batch/prop/fixSrcToCheckUnset.coffee preload-model dryrun -t 2024-01-01"
   ```