# 需求 [remove_props_part]

## 反馈

1. Fred反馈props_part好像还有地方在指向它。看看有指向它的都删除一下吧。

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-03-20

## 原因

1. 上一次删除props_part只是将引用props_part的地方改用es,但是如果config为Mongo的话还是会使用props_part,需要暂时保留先观察一段时间

## 方案

1. 添加判断,当config.propertiesBase.useSearchEngine不是ES时抛出error
2. 完全移除props_part的使用
3. ut中props_part相关的地方需要修复

## 确认日期:    2025-03-20

## online-step

1. 重启server
