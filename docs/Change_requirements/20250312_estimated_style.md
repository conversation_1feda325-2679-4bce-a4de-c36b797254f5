# 需求 [estimated_style]

房源详情页的房屋估价显示改版

## 需求提出人:   Allen

## 修改人：      ZhangMan

## 提出日期:     2025-02

## 方案

1. 仅for sale显示估价Est. value，sold显示挂盘价，点击Est. value跳转到Estimated Home Value部分。
2. 估价后面有一个圆圈问号标签，点击后打开帮助标准alert，和朝向后的帮助按钮保持一致；内容显示：The estimated value is based on our AI algorithm model, and is only for reference. Speak with a REALTOR for better insight into market value of the property.
3. Estimated Home Value卡片显示逻辑：Active房源，且有Est. value时对所有用户显示；Sold房源且有Est. value时对inRealGroup,noteAdmin权限用户显示SOLD price，其余用户Estimate home value部分不显示；其它下市状态房源不显示Estimate home value部分。
4. Estimated Home Value卡片显示样式规定：胶囊左右边界不超出显示区域；提供估价和估价范围(Min到Max)，min和max的label左/右对齐，如果估价价格label长度足够，可以显示明细价格而非$***K。
当估价和售卖价格同时出现时：售卖价格在估价范围内时，只显示绿色bar，超出范围时，除了绿色bar外，显示蓝色/红色bar。如果售价低于估价min显示蓝色+绿色bar；如果售价高于估价max显示绿色+红色bar；不同颜色bar的长度取决于，例如：售价高于估价max时，估价min到售价这个区间作为100%，估价max在区间中什么位置，绿色bar长度就是多少，剩余长度是红色bar；反之亦然。如果售价出现的估价条上，展示灰色的Est. Val的游标。
5. 分享房源详情页面：Est. value的title和update文字颜色保持一致，后面的具体价格显示View，可点击，点击后跳转引导下载页面；Estimated Home Value部分：Active房源，且有估价时显示估价部分，绿色bar的下方Max，Min，标签显示出来，但是下方的具体价格虚化，具体的估价也虚化，点击后跳转引导下载页面；下市房源包括sold房源不显示Estimate home value部分。
6. 估价卡片显示在房源详情页Investment Analysis title下方；
7. 估价卡片增加按钮Customize Estimate点击后跳转手动估价界面，如果是分享出去的按钮上显示View Estimate，点击后跳转下载引导页。
8. 修复分享到微信里打开后点击朝向等帮助按钮不显示alert
9. Associate Listing部分只有管理员可以看

## 确认日期:  2025-03-13，2025-03-25