# 需求 [upgrade_mongodb_driver]

## 反馈

之前的mongodb_driver版本太旧，更新为最新的v6.16


## 需求提出人:    Fred, Rain

## 修改人：      Maggie

## 提出日期:     2025-06-02

## 原因


## 解决办法
1. useNewUrlParser 和 useUnifiedTopology 都默认启用， 不再需要
2. collection.stats()	在 MongoDB 6.x 中被移除	用 db.command({ collStats: 'collection_name' })
3. 从 MongoDB Node.js Driver v6.0.0 开始，ObjectId.isValid() 不再认为长度为 12 的任意字符串是合法的 ObjectId。
4. PropertiesCol.findOneAndUpdate 使用callback，ret就是origProp，在Mongo4中默认添加includeResultMetadata:true
5. findAndModify 已经弃用
6. {safe: true} 已废弃， 改成{w:1}


## 影响范围
mongodb相关

## 是否需要补充UT
添加findOneAndUpdate，stat，DEPRECATED_METHODS相关ut

## 确认日期:    2025-06-06

## Online step

1. 在src路径下运行
```bash
npm install
```
2. 重启服务
