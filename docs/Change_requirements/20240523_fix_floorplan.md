# 需求 [fix_floorplan]

## 反馈

1. rain/fred 反馈condos下的floorplan图片需要backup一份,folder和图片 名称需要hash
2. 对backup的图片添加水印,放到指定的folder下(不修改backup的图片,水印图片为新生成的)
3. 需要新建一张floorplans的表,将condos下的floorplan记录移到该表下面
4. 修改图片的path 'https://f.realmaster.com//mnt/ca2/rmfu/f_realmaster_cn/floorPlan/d0c694/3ffc97.png'
5. 前端展示的floorplan信息全部通过floorplans表获取
6. 经济可以修改floorplan数据，但是需要提交审核,审核通过后修改floorplan数据

## 需求提出人:   rain

## 修改人：      luoxiaowei

## 提出日期:     2024-05-22

## 问题与解决方案

1. condos下的floorplanId改为nanoid，现有的hash可能会存在问题(如果两条floorplan完全一样，id也会一致)
2. 经纪可以加comments   保存到一个新的collection
3. verified记录，batch需要判断condos数据是否有变更,如果修改了 添加warning log
4. 经纪的match针对整条floorplan

## 解决方案

1. 修改batch->floorPlanWaterMark
   1. 支持指定输出folder
   2. 对condos下的图片先进行backup, 对backup的图片添加水印 (folder的名字nanoid取1位, 图片的名字nanoid取9位)
   3. 向floorplans表写入数据改为每一个floorplan为一个record
   4. floorplan的图片url只记录path, 域名等由代码自动补全
   5. batch执行逻辑流程图见 docs/Dev_floorplans/addWaterMark.drawio
2. 添加floorplan model,为前端提供相应的api
   1. 基础改查
   2. 经纪添加comments

### collection->floorplans数据结构

| field      | data type      | description
| ---------- | -------------- | ---------------------------------
| _id        | string         | ObjectId
| tp         | string         | 区分img为floorplan或building
| uaddr      | string         | uaddr, condos->id
| src        | string         | source, 区分mls上传, 人工上传(manu), 已经有floorplan解析导入进来(import)
| prov       | string         | province
| city       | string         | city
| nm         | string         | name, condos.floorplan 相同name表明是同房源数据(loft类型等, 多张户型图)
| bdrms      | string         | bedrooms
| bthrms     | string         | bathrooms
| sqft       | string         | sqft
| price      | string         | price
| imgs       | array object   | [{img:'a/bcd123456.jpg', origImg:['P/C/BK.jpg'], imgHash:'', w:'', h:''}], 相同户型图可能来自多个condos.floorplan记录, origImg记录为数组
| unts       | array object   | [{ lvl:[1, 2, 3], unt:[1], fce:[N, E], fpId:'fpID1', origImg:'P/C/BK.jpg' }] 每条floorplan对应一个unit信息
| matchCnt   | number         | 经纪点击match的次数, 即使floorplan有多个户型图点了match也是针对整条floorplan记录
| matchUids  | array object   | eg:[ {uid:'', ts:'', propId:'', unt:''} ]点击match的经纪uid
| verified   | object         | eg: {status:'', uid:'', ts:''}
| touchedMt  | timestamp      | batch修改记录日期
| ts         | timestamp      | 创建时间
| _mt        | timestamp      | 修改时间

## 确认日期：        2024-05-31

## 流程图确认日期:    2024-06-07

   如果用当前格式, 前端需要添加限制, 控制输入需要严格一些
   以下case 考虑是否存在更好的数据格式
   case1:   低层高层朝向问题
[{

```javascript
  lvl: [1, 2, 3], unt: [1], fce: [N, E]
```

}, {

```javascript
  lvl: [4, 5], unt: [1], fce: [E]
```

}]
   case2:   低层高层unit no问题
[{

```javascript
  lvl: [1, 2, 3], unt: [1, 2], fce: [N, E]
```

}, {

```javascript
  lvl: [4, 5], unt: [2], fce: [N, E]
```

}]
   case3:   同楼层 朝向问题
[{

```javascript
  lvl: [4, 5], unt: [1, 2], fce: [E]
```

}, {

```javascript
  lvl: [4, 5], unt: [1], fce: [N, E]
```

}]

## online-step

1. 清空vow.floorplans的记录(清空记录是因为在2023-12时生成过一份数据,该数据格式以及图片命名等不符合现有需求,所以需要清除掉旧的数据重新生成)
2. 需要先上线branch->merge_condos,执行condos 合并batch->src/batch/condos/mergeCondos_byUaddr.coffee(该batch已经执行过,可以跳过当前步骤)
3. 执行batch src/batch/condos/fix_floorplanId.coffee，修改现有condos下的floorplan.id,改用nanoid
4. 执行batch src/batch/condos/floorPlanWaterMark.coffee，重新backup/watermark,生成floorplans记录
<!-- 测试时可以注释掉floorPlanWaterMark#115, 118-142的代码, imgServerDlAddr改为'https://f.realmaster.com/',此时只会读取远程img, 不会backup/watermark -->
