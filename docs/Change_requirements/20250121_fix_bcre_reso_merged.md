# 需求 [fix_bcre_reso_merged]

## 反馈

1. bcre reso 数据：bridge_bcre_logs和bridge_bcre_raw存在pending但是bridge_bcre_merged没有更新的情况

## 需求提出人:   <PERSON><PERSON><PERSON><PERSON><PERSON>

## 修改人：      Maggie

## 提出日期:     2025-01-21

## 原因

1. saveToMergedCollection中，mergedDoc = existMergedProp or {...propertyCopyFilterd, mt: mt, ts: ts}， 如果存在existMergedProp，则mergedDoc = existMergedProp， 不会更新为新进来的prop

## 解决办法

1. 修改saveToMergedCollection，mergedDoc = {...propertyCopyFilterd, mt: mt, ts: ts}
2. 查找bridge_bcre_logs中同一个sid有多条记录，[mt 20250101后的，不能用时间戳筛选，因为有log第一条记录是2024年的，后面才是2025年的]，batch 从raw更新merged。[因为bcreResoDownload在2025-01-21T14:58:00上线，batch仍未上线，修改为添加revisedTime，reImportBcreResoMerged 只处理在revisedTime之前有2条以上log，revisedTime之后没有log的记录]
3. 添加fixHisAndReImportBCREProps，修改his。[修改为删除revisedTime后的his，使用重新生成的his]

## 确认日期:    2025-01-21

## online-step

1. 重启bcreResoDownload【已完成】
2. 先执行batch reImportBcreResoMerged
```
  ./start.sh -t batch -n reImportBcreResoMerged -cmd "lib/batchBase.coffee batch/import/reImportBcreResoMerged.coffee dryrun"
```
3. 执行batch fixHisBCRE【需要步骤2运行后，这个fixHisBCRE才能测试】
```
  ./start.sh -t batch -n fixHisBCRE -cmd "lib/batchBase.coffee batch/prop/fixHisBCRE.coffee preload-model dryrun"
```
