# 需求 [fix_unset_rmSqft]

## 反馈

1. Tao反馈房源TRBN12030169的面积存在问题，Rain反馈该房源面积是一开始写大了，现在改小了，但是面积估算在rni数据修改后没有更新

## 需求提出人:    Rain

## 修改人：       Lu<PERSON>ei

## 提出日期:      2025-04-05

## 原因

1. 该房源一开始的面积数据为{"LivingAreaRange" : "3500-5000"}, PublicRemarks中有提到"Nearly 5,000 Sqft Of Interior Living Space On Oversized Lot.", 导致我们估算的面积为5000
2. 后来房源面积数据改为了{"LivingAreaRange" : "2500-3000"},但是我们在检查unset字段时没有包含rmSqft相关信息,导致估算房源数据没有被修改

## 解决办法

1. 在 watch&import的检查unset信息时(CHECK_UNSET_FIELDS),添加rmSqft/rmSqft1/rmSqft2/sqftQ/sqftSrc字段,以支持这些字段的unset检查

## 影响范围

1. 当房源更新之后,房源的面积估算值可能会被unset掉

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-04-07

## online-step

1. 重启watch&import
2. 手动修改房源TRBN12030169的rmSqft信息
```shell
db.properties.updateOne({_id:'TRBN12030169'},{$unset:{rmSqft:1,sqftQ:1,sqftSrc:1}})
```
