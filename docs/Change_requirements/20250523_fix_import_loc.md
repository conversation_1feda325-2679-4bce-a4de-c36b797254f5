# 需求 [fix_import_loc]

## 反馈

1. Rain反馈reImportProp error: MongoServerError: Can't extract geo keys:lat: null, q: 100, lng: null, loc: { type: "Point", coordinates: [ null, null ] },

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-05-23

## 原因

1. isAddressFieldsChanged函数对oldProp没有经纬度的返回值错误,应该返回true

## 解决办法

1. 修改isAddressFieldsChanged函数oldProp没有经纬度信息时的返回值

## 影响范围

## 是否需要补充UT

1. 需要针对isAddressFieldsChanged函数覆盖补充UT

## 确认日期:    2025-05-23

## online-step

1. 重启watch&import
