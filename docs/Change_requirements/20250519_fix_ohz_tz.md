# 需求 [fix_ohz_tz]

## 反馈

1. sales反馈温哥华房源open house时间存在问题

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON><PERSON>ei

## 提出日期:      2025-05-19

## 原因

1. BRE房源的open house时区转换使用多伦多时区,应该使用温哥华时区(BRE房源的open house时区BCRES_Timezone均为'Pacific Time')

## 不使用loc或者prov/city进行判断时区的原因

1. BRE来源在rni中存在省份提供错误的数据
2. open house转换在geoCoding之前,可能存在经纬度不存在或者经纬度错误的情况
3. BRE房源的open house有提供`BCRES_Timezone`字段,可以添加该字段判断,使用提供的timezone进行转换

## 解决办法

1. BCRE房源的open house时间转换为温哥华时区
   1. src/lib/helpers_date.coffee文件下添加formatUTC2Vancouver函数(参考formatUTC2EST),转换为温哥华时区
   2. src/libapp/impMappingBcreReso.coffee文件下formatOpenHouseDateTime函数调用formatUTC2Vancouver函数
2. 添加batch修复历史数据,参考src/batch/prop/fix_reso_oh.coffee文件
3. 补充相关UT

## AI疑问解答

1. 时区处理：
   - 使用 `moment-timezone` 库处理时区转换
   - 时区标识符使用 `America/Vancouver`
   - 库会自动处理夏令时(DST)的转换

2. 历史数据修复：
   - 数据源：`bridge_bcre_merged` 表
   - 修复范围：所有包含 `openHouse` 字段的记录
   - 修复方式：将时间从UTC转换为温哥华时区

3. 数据回滚：
   - 暂时不考虑数据回滚方案

## 影响范围

1. BCRE房源import时open house时间转换根据当地时区处理

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-05

## online-step

1. 重启watch&import
2. 执行batch
```shell
./start.sh -n fix_bcre_ohz -cmd "lib/batchBase.coffee batch/prop/fix_bcre_ohz.coffee dryrun"

测试服执行结果:
Completed, Total process time 0.6622333333333333 mins, processed(31772):47.97K/m, dryRun(30561):46.14K/m, skip(1211):1.828K/m
```
