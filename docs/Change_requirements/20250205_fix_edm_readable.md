# 需求 [fix_doMapping]

## 反馈

1. 在做fixDoMapping时，发现edm的RoadType字段值在readable中都不存在。

## 需求提出人:   

## 修改人：      Maggie

## 提出日期:     2025-02-04

## 原因

1. 2个值对应ROAD ACCESS，第二个[多数为null]会覆盖第一个
   
## 解决办法

1. 在edmConvertToReadable中，检查readableKey是否已经存在，如果存在，且已经有非null值，则不更新。
2. 修改删除不需要的LR_remarks55【对应的PublicRemarks原本字段已经有了】,删除数据中没有的LMD_MP_Latitude,LMD_MP_Longitude
3. 添加batch fixEdmReadable.coffee，对于有多个value对应一个key的值【FIELD_MAPPINGS】，检查是否获取到正常的值，如果有错误，则更新。


## 确认日期:    2025-02

## 测试结果
### Performance Metrics
- Total process time: 0.34 mins
- Processed: 73,400 records (216.8K/min)
- Updated: 70,467 records (208.1K/min)
- No changes: 2,933 records (8.66K/min)
- Success rate: 96% records updated


## online-step
1. 重启raeDownload
2. 运行batch/prop/fixEdmReadable.coffee
   ```
   ./start.sh -t batch -n fixEdmReadable -cmd "lib/batchBase.coffee batch/import/fixEdmReadable.coffee dryrun"
   ```
