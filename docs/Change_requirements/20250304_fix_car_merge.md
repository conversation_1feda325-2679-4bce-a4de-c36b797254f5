# 需求 [fix_car_merge]

## 反馈

1. 房源TRBX9413172为在市状态，但是merge到了下市房源CAR40629406上,导致用户检索不到该上市房源

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2025-03-04

## 原因

1. treb reso下载的房源会包含CAR/OTW的sorce,由于CAR房源仍然在下载,索引treb reso中对于可以通过id找到CAR的房源需要优先merge到CAR房源上

## 解决办法

1. 在通过id查找CAR房源的时候添加status判断，如果不一致的话不进行merge并warning log
2. 在状态一致时
   1. 某个房源的addr不存在时,跳过merge
   2. 两个房源的daddr都存在,且值不一样时,跳过merge(如果两个房源的daddr都为N,可以merge)
   3. 当只有一个房源的daddr存在,且为N时,跳过merge(一个存在为Y,一个不存在时可以merge)
   4. 如果daddr都不存在时,可以进行merge

## 确认日期:    2025-03-04

## online-step

1. 重启watch&import
2. 执行batch,修复历史数据

```shell
./start.sh -n fix_carMerge -cmd "lib/batchBase.coffee batch/prop/fix_carMerge.coffee dryrun"
测试服dryRun运行结果:
Completed, Total process time 0.48028333333333334 mins. processCount(96850):201.6K/m, noNeedRemoveMerge(94638):197.0K/m, statusNotMatch(2046):4.259K/m, dryRun(2187):4.553K/m, addressMissing(141):293.5/m, origSidNotMatch(25):52.05/m
```
