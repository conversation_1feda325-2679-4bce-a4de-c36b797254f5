# 需求 [fix_notifyAdmin]

## 反馈

1. sendMailObj.notifyAdmin()不支持await


## 需求提出人:    <PERSON><PERSON>
## 修改人：       Maggie

## 提出日期:      2025-06-26

## 原因

1. notifyAdmin 方法使用的是回调模式：
notifyAdmin: (msg)->
  # ...
  @sendMail engine,mail,(err,response)->
    if err
      console.error err
不需要也不支持 await。


## 解决办法
1. 去除await
  i18nStat.coffee
  reImportProp.coffee
  trebManualCheck.coffee


## 影响范围
1. i18nStat
2. reImportProp
3. trebManualCheck

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-26

## online-step

1. i18nStat，reImportProp都是cronjob，不需要重启
2. trebManualCheck 影响旧的trebDownload，不需要重启
