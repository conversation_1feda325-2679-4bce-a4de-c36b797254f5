# 需求 [school_explain]

## 反馈

1. 相应表格前添加说明
2. 排名表显示2位小数
3. 检查发现osslt数据导入的有问题，给了最新的数据需要更新
4. 添加显示osslt通过率

## 需求提出人:   tao

## 修改人：      liurui

## 提出日期:     2025-02-25

## 原因


## 解决办法

1. 添加说明
2. 修改处理部分
3. 替换处理后的eqao数据
4. 添加batch更新merged表数据

## 确认日期:    2025-02-27

online step:
1. 替换rmlfs中 sch_eqao2.bson.gz 的数据到 sch_eqao 表中
   1. 增加了rmRank字段中osslt的通过率(OSSLTrmP)字段，更新了g9的g9rmScore和g9rmMathScore数据
2. 跑batch 将eqao表中最新排名更新到merged表中
···
  sh start.sh -t batch -n fixRmRanking -cmd "lib/batchBase.coffee batch/correctData/20250305_fixMregedRmRank.coffee"
···
