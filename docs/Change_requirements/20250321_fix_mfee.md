# 需求 [fix_mfee]

## 反馈

1. Fred反馈房源E4426498仅显示了condo fee信息(房源详细)，但是mainInfo没有显示mfee内容

## 需求提出人:   Fred

## 修改人：      Lu<PERSON> xiaowei

## 提出日期:     2025-03-20

## 原因

1. branch[show_edm_condoFee]仅添加了前端对condo fee的显示，但是没有将condo fee转为mfee
2. edm,creb,trebReso的mapping函数中对应mfee的处理存在dry问题

## 方案

1. 当EDM房源没有mfee时，判断是否存在CondoFee，如果存在 将其转为mfee
2. 添加batch,将没有mfee但是存在condoFee的房源添加mfee
3. 对creb,trebReso的formatMfee进行dry

## 影响范围

1. 会影响EDM房源的mfee处理

## 是否需要补充UT

1. 需要补充ut,在import的ut中补充EDM房源对AssociationFee与CondoFee处理的cases

### UT补充发现的问题

1. EDM的mapping没有引入statusLstMapping而直接使用
2. OTW的mapping将formatStatusAndLst exports出去,但是原文件没有定义函数而直接使用
3. import的UT测试由于boundary受本地数据影响导致比较存在问题
4. import的UT测试因为school表改名,并且也受本地数据影响导致比较存在问题

### UT补充发现问题的解决方案

1. 问题1与问题2在对应的mapping文件已修复
2. 暂时取消bnds,schools,schs的比较,添加了TODO 以后改为设置mock数据测试

## 确认日期:    2025-03-21

## online-step

1. 重启watch&import
2. 执行batch
```shell
./start.sh -n fix_edm_mfee -cmd "lib/batchBase.coffee batch/prop/fix_edm_mfee.coffee dryrun"
执行结果:
Completed, Total process time 3.476 mins, processed(19119):5.500K/m, dryRun(19119):5.500K/m
```
