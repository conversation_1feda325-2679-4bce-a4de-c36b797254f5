# 需求 [reso_logs]

## 反馈

1. reso_crea_dup_logs空间占用涨幅过快，需要处理

## 需求提出人:    Rain

## 修改人：      Maggie

## 提出日期:     2025-04-07

## 原因
crea相同hash的log太多，导致reso_crea_dup_logs空间占用涨幅过快

## 解决办法
1. media，Rooms数据压缩。 
2. 重复数据删除。 
3. 添加batch处理reso_crea_dup_logs表中已有数据


## 影响范围
creaResoDownload 

## 是否需要补充UT
1. 不需要补充UT

## 确认日期:    2025-04-08

## online-step

1. 重启creaResoDownload
2. 运行batch
```
./start.sh -n compressCreaDupLog -cmd "lib/batchBase.coffee batch/import/compressCreaDupLog.coffee dryrun"
```
测试服：
 Completed, Total process time 66.66258333333333 mins, processed(7771599):116.5K/m, dryRunUpdate(417656):6.265K/m, dryRunDelete(7353943):110.3K/m
