# 需求 [fix_trebReso]

## 反馈

1. sales反馈房源TRBX12165216图不对

## 需求提出人:    Sophie

## 修改人：       Maggie

## 提出日期:      2025-05-22

## 原因

1. 这个房源的图更改了3次，后2次是通过时间戳更新进来的，最后merged中只有第三次更新的图，media表数据正常，怀疑还是单个mergeResourceIntoProperty时候不同步


## 解决办法

1. 在获取到resource后，保存到resource表，并添加到resourceUpdatedByMt col中
{id:'TRBX12165216',resourceType: resourceName,ts}【 id 和 resourceType 复合索引】
待完成一整次getResourceAsync resource获取后，处理resourceUpdatedByMt col数据，从本地resource表中获取，整合到merged中

## 影响范围

1. trebReso resource mt download

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-22

## online-step

1. 重启trebResoDownload