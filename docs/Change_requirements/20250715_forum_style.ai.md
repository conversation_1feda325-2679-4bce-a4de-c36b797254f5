<!-- Generated by SpecStory -->

# 处理广告显示逻辑 (2025-07-15 10:20Z)

_**User**_

1. @forumSummaryCard.js 修改， 如果是ad，不显示内容，只保留上部分图片
2. @homeResponsive.html 修改， 如果是ad，不显示整个卡片

---

_**Assistant**_

Read file: appweb/src/appDev3/vue-components/forum/forumSummaryCard.js

---

Read file: appweb/src/themes/baseTheme/forum/homeResponsive.html

---

Read file: appweb/src/appDev3/vue-components/forum/forumSummaryCard.js

---

```diff
    template: `
    <div class="forum-summary-card" v-bind:class="{summaryWeb: isWeb , greybg: post.gid, 'layout-up-down': curStyle == 'up-down','box-shadow': curStyle == 'up-down'}">
-     <!-- 广告模式 -->
-     <div class="post-top-div" style="position:relative; display:block!important;" v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle != 'up-down'">
-         <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_('Edit')}}</span></div>
-         <img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" />
-         <div class="post-top-text">{{$_('AD')}}</div>
-     </div>
-     <!-- 正常内容模式 -->
-     <div :style="computedContainerStyle" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov}">
-         <!-- 上下布局：图片在上 -->
-         <div v-if="curStyle == 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
-             <div v-if="renderImageComponent().hasThumb" :style="renderImageComponent().imageStyle + 'background-image: url(' + renderImageComponent().thumbUrl + ');'" :class="renderImageComponent().imageClass">
-                 <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" style="position: absolute; top: 10px; right: 10px;"><i class="fa fa-video-camera"></i></span>
-                 <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" style="position: absolute; bottom: 10px; right: 10px;">{{$_('LIVE')}}</span>
-                 <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" style="position: absolute; bottom: 10px; right: 10px;">{{renderImageComponent().scheduledTime}}</span>
-             </div>
-             <div v-else :class="renderImageComponent().imageClass" :style="renderImageComponent().imageStyle">
-                 <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 50px; color: #d2d5d8;">{{renderImageComponent().firstLetter}}</div>
-             </div>
-         </div>
+     <!--
+       @description
+       如果是广告（ad），只显示图片部分（上半部分），不显示内容区域。
+       判断条件：post.adInlist && post.adTopPhoto && post.adTop
+       只保留图片展示部分，隐藏下方内容。
+     -->
+     <template v-if="!(post.adInlist && post.adTopPhoto && post.adTop)">
+       <!-- 正常内容模式 -->
+       <div :style="computedContainerStyle" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov}">
+           <!-- 上下布局：图片在上 -->
+           <div v-if="curStyle == 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
+               <div v-if="renderImageComponent().hasThumb" :style="renderImageComponent().imageStyle + 'background-image: url(' + renderImageComponent().thumbUrl + ');'" :class="renderImageComponent().imageClass">
+                   <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" style="position: absolute; top: 10px; right: 10px;"><i class="fa fa-video-camera"></i></span>
+                   <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" style="position: absolute; bottom: 10px; right: 10px;">{{$_('LIVE')}}</span>
+                   <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" style="position: absolute; bottom: 10px; right: 10px;">{{renderImageComponent().scheduledTime}}</span>
+               </div>
+               <div v-else :class="renderImageComponent().imageClass" :style="renderImageComponent().imageStyle">
+                   <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 50px; color: #d2d5d8;">{{renderImageComponent().firstLetter}}</div>
+               </div>
+           </div>
  
-         <!-- 内容区域 -->
-         <div :class="curStyle == 'up-down' ? 'content-section-ud' : 'post-summary'">
-             <!-- 标题区域 -->
-             <div :class="curStyle == 'up-down' ? 'post-title-ud' : 'post-title'" v-bind:class="{deleted: post.del}" @click="openView()">
-                 <span class="red-dot-forum fa fa-circle" v-if="post.hasUpdate"></span>
-                 <span  class="red-button"  v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle == 'up-down'">{{$_('AD')}}</span>
-                 <span class="red-button" v-if="post.sticky && !noTag.top">{{$_('TOP')}}</span>
-                 <span class="red-button" @click="openGroup()" v-if="post.gid && post.gnm && !noTag.gid">{{post.gnm}}</span>
-                 <span class="red-button blue" @click="openTp()" v-else-if="(post.tpbl && post.tpbl.length) || (post.tp && !noTag.topic)">{{computedTp}}</span>
-                 <span class="red-button blue" @click="openTag()" v-else-if="(post.tags && post.tags[0]) && !noTag.tag">
-                     <span v-if="post.tags[0]=='HOT'">{{$_('HOT')}}</span>
-                     <span v-else>{{post.tags[0]}}</span>
-                 </span>
-                 <span class="red-button blue" @click="openBySrcView('property')" v-else-if="post.src=='property' && post.cc>0 && !noTag.property">{{$_('Home Review')}}</span>
-                 <span class="red-button blue" @click="openBySrcView('sch')" v-else-if="(post.src=='sch'||post.src=='psch') && !noTag.sch && !noTag.psch">{{$_('School Review')}}</span>
-                 <span class="red-button blue" @click="addCityFilter()" v-show="(post.city||post.prov||post.cnty) && !noTag.city && post.cnty!=='No City'">{{$_(post.city||post.prov||post.cnty,'city')}}</span>
-                 <!-- 标题文本 -->
-                 <span :class="curStyle == 'up-down' ? 'txt-ud' : 'txt'" v-if="['property','psch','sch'].indexOf(post.src)>=0 && post.cmntl">{{post.cmntl + ' ' + post.tl}}</span>
-                 <span :class="curStyle == 'up-down' ? 'txt-ud' : 'txt'" v-else>{{post.tl}}</span>
-             </div>
+           <!-- 内容区域 -->
+           <div :class="curStyle == 'up-down' ? 'content-section-ud' : 'post-summary'">
+               <!-- 标题区域 -->
+               <div :class="curStyle == 'up-down' ? 'post-title-ud' : 'post-title'" v-bind:class="{deleted: post.del}" @click="openView()">
+                   <span class="red-dot-forum fa fa-circle" v-if="post.hasUpdate"></span>
+                   <span  class="red-button"  v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle == 'up-down'">{{$_('AD')}}</span>
+                   <span class="red-button" v-if="post.sticky && !noTag.top">{{$_('TOP')}}</span>
+                   <span class="red-button" @click="openGroup()" v-if="post.gid && post.gnm && !noTag.gid">{{post.gnm}}</span>
+                   <span class="red-button blue" @click="openTp()" v-else-if="(post.tpbl && post.tpbl.length) || (post.tp && !noTag.topic)">{{computedTp}}</span>
+                   <span class="red-button blue" @click="openTag()" v-else-if="(post.tags && post.tags[0]) && !noTag.tag">
+                       <span v-if="post.tags[0]=='HOT'">{{$_('HOT')}}</span>
+                       <span v-else>{{post.tags[0]}}</span>
+                   </span>
+                   <span class="red-button blue" @click="openBySrcView('property')" v-else-if="post.src=='property' && post.cc>0 && !noTag.property">{{$_('Home Review')}}</span>
+                   <span class="red-button blue" @click="openBySrcView('sch')" v-else-if="(post.src=='sch'||post.src=='psch') && !noTag.sch && !noTag.psch">{{$_('School Review')}}</span>
+                   <span class="red-button blue" @click="addCityFilter()" v-show="(post.city||post.prov||post.cnty) && !noTag.city && post.cnty!=='No City'">{{$_(post.city||post.prov||post.cnty,'city')}}</span>
+                   <!-- 标题文本 -->
+                   <span :class="curStyle == 'up-down' ? 'txt-ud' : 'txt'" v-if="['property','psch','sch'].indexOf(post.src)>=0 && post.cmntl">{{post.cmntl + ' ' + post.tl}}</span>
+                   <span :class="curStyle == 'up-down' ? 'txt-ud' : 'txt'" v-else>{{post.tl}}</span>
+               </div>
  
-             <!-- 评论信息区域 -->
-             <div class="post-comments" :style="curStyle == 'up-down' ? 'margin-top: 8px;' : ''">
-                 <span class="post-name pull-left">{{trimStr(post.fornm,12)}}</span>
-                 <span class="post-bottom">
-                     <span v-if="post.src!='sch' && post.src!=='psch'">
-                         <span v-if="dispVar.isAdmin">{{post.vc}} | {{post.vcapp}} | {{post.vcc}}</span>
-                         <span v-else>{{computedVc}}</span>
-                         <span class="fa fa-eye" style="padding-left:'5px'"></span>
-                     </span>
-                     <span v-if="(post.cc > 0) && (!post.discmnt || dispVar.forumAdmin)">
-                         <span v-if="post.src!='sch' && post.src!=='psch'">|</span>
-                         <span>{{post.cc}}</span>
-                         <span class="fa fa-comments"></span>
-                     </span>
-                     <span v-if="dispVar.forumAdmin && post.similars">| S: {{post.similars.length}} |</span>
-                 </span>
-                 <span class="post-ts" v-if="dispVar.isAdmin">{{formatTs2(post.mt)}}</span>
-                 <span class="post-ts" style="padding-right:5px;">{{formatTs2(post.ts)}}</span>
-                 <span class="realtor-only" v-if="post.realtorOnly">{{$_('Realtor Only')}}</span>
-                 <span class="icon icon-close reportForumIcon" v-if="parentPage == 'forum'" data-sub="report forum" :data-id="post._id" @click="toggleFlagModal(post)"></span>
-             </div>
-         </div>
+               <!-- 评论信息区域 -->
+               <div class="post-comments" :style="curStyle == 'up-down' ? 'margin-top: 8px;' : ''">
+                   <span class="post-name pull-left">{{trimStr(post.fornm,12)}}</span>
+                   <span class="post-bottom">
+                       <span v-if="post.src!='sch' && post.src!=='psch'">
+                           <span v-if="dispVar.isAdmin">{{post.vc}} | {{post.vcapp}} | {{post.vcc}}</span>
+                           <span v-else>{{computedVc}}</span>
+                           <span class="fa fa-eye" style="padding-left:'5px'"></span>
+                       </span>
+                       <span v-if="(post.cc > 0) && (!post.discmnt || dispVar.forumAdmin)">
+                           <span v-if="post.src!='sch' && post.src!=='psch'">|</span>
+                           <span>{{post.cc}}</span>
+                           <span class="fa fa-comments"></span>
+                       </span>
+                       <span v-if="dispVar.forumAdmin && post.similars">| S: {{post.similars.length}} |</span>
+                   </span>
+                   <span class="post-ts" v-if="dispVar.isAdmin">{{formatTs2(post.mt)}}</span>
+                   <span class="post-ts" style="padding-right:5px;">{{formatTs2(post.ts)}}</span>
+                   <span class="realtor-only" v-if="post.realtorOnly">{{$_('Realtor Only')}}</span>
+                   <span class="icon icon-close reportForumIcon" v-if="parentPage == 'forum'" data-sub="report forum" :data-id="post._id" @click="toggleFlagModal(post)"></span>
+               </div>
+           </div>
  
-         <!-- 左右布局：图片在右 -->
-         <div v-if="curStyle !== 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
-             <lazy-image :class="renderImageComponent().imageClass" v-if="renderImageComponent().hasThumb" :alt="renderImageComponent().altText" :error="imageLoadError" :src="renderImageComponent().thumbUrl" :imgstyle="renderImageComponent().imageStyle"></lazy-image>
-             <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" :class="renderImageComponent().vidRecordClass"><i class="fa fa-video-camera"></i></span>
-             <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" :class="renderImageComponent().vidLiveClass">{{$_('LIVE')}}</span>
-             <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" :class="renderImageComponent().vidLive2Class">{{renderImageComponent().scheduledTime}}</span>
-             <div :class="renderImageComponent().imageClass" v-if="!renderImageComponent().hasThumb">
-                 <div class="no-img">{{renderImageComponent().firstLetter}}</div>
-             </div>
-         </div>
-     </div>
+           <!-- 左右布局：图片在右 -->
+           <div v-if="curStyle !== 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
+               <lazy-image :class="renderImageComponent().imageClass" v-if="renderImageComponent().hasThumb" :alt="renderImageComponent().altText" :error="imageLoadError" :src="renderImageComponent().thumbUrl" :imgstyle="renderImageComponent().imageStyle"></lazy-image>
+               <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" :class="renderImageComponent().vidRecordClass"><i class="fa fa-video-camera"></i></span>
+               <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" :class="renderImageComponent().vidLiveClass">{{$_('LIVE')}}</span>
+               <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" :class="renderImageComponent().vidLive2Class">{{renderImageComponent().scheduledTime}}</span>
+               <div :class="renderImageComponent().imageClass" v-if="!renderImageComponent().hasThumb">
+                   <div class="no-img">{{renderImageComponent().firstLetter}}</div>
+               </div>
+           </div>
+       </div>
+     </template>
+     <!-- 广告模式，仅显示图片部分（上半部分） -->
+     <template v-else>
+       <div class="post-top-div" style="position:relative; display:block!important;">
+           <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_('Edit')}}</span></div>
+           <img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" />
+           <div class="post-top-text">{{$_('AD')}}</div>
+       </div>
+     </template>
    </div>
    `
```

---

```diff
    <div id="forum-main" class="container">
      <div id="forum-containter" v-on:scroll="listScrolled" class="row">
-         <div v-for="post in allPosts" track-by="$index" :key="post._id" class="col-xs-12 col-sm-6 col-md-4">
+         <!-- 只渲染非广告类型的帖子卡片 -->
+         <!-- JsDoc: 过滤广告类型（ad）帖子，只显示非广告帖子卡片 -->
+         <div v-for="post in allPosts" v-if="!(post.adInlist && post.adTopPhoto && post.adTop)" track-by="$index" :key="post._id" class="col-xs-12 col-sm-6 col-md-4">
            <forum-summary-card parent-page="forumList" :post="post" :disp-var="dispVar" :display-page="displayPage" :no-tag="noTag" cur-style="up-down"></forum-summary-card>
          </div>
```

---

已完成如下修改，满足你的需求：

1. **forumSummaryCard.js**  
   - 修改了组件模板部分：  
     - 如果是广告（`post.adInlist && post.adTopPhoto && post.adTop`），只显示上半部分图片，不显示内容区域。
     - 详细注释说明了实现逻辑，便于后续维护。

2. **homeResponsive.html**  
   - 在 `v-for` 处增加过滤条件：  
     - 只有非广告类型的帖子才渲染卡片（`v-if=\"!(post.adInlist && post.adTopPhoto && post.adTop)\"`）。
     - 添加了注释说明过滤逻辑。

这样广告类型的帖子只会显示图片部分（在卡片内），并且在首页列表不会渲染整个卡片。  
如需进一步自定义广告展示样式或有其他需求，欢迎继续告知！

---
