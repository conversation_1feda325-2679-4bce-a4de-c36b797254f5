# 需求 [i18n_stat]

## 反馈

1. Rain反馈 i18n里有很多crossst和private remark（可能是）被翻译了，这个不应该翻译，应该还有别的字段，不确定是前端还是后端，这个优先改下吧。另外测下i18n_clean能不能去掉这些垃圾数据

## 需求提出人:   Rain

## 修改人：      Luo xiaowei

## 提出日期:     2025-03-25

## 原因

1. 房源详细中的字段值翻译导致垃圾数据进入 (src/libapp/propertiesTranslate.coffee，不同board翻译的字段不同)

## 方案

1. Dev模式下,i18n添加数据时增加log
2. 添加翻译时的正则过滤条件,增强数据库添加数据的验证条件(现有的数组正则匹配不能合并成一个正则去match处理,会缺少message信息)
3. 房源详细中展示字段,添加flag标明是否需要翻译
4. I18n_clean的batch需要定时执行，去清理未添加翻译的数据

## 影响范围

1. i18n表数据添加
2. 房源详细展示内容

## 是否需要补充UT

1. 需要补充ut测试正则匹配
2. 需要针对translate_rmprop_detail函数补充ut

## 确认日期:    2025-03-26

## online-step

1. 重启server
2. 设置batch->i18n_clean的定期执行
