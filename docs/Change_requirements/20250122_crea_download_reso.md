# 需求 [crea_download_reso]

## 反馈

1. DDF 找到不到25001112房源，web api https://ddfapi-docs.realtor.ca/#section/ Quickstart-Overview 可以获取到

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-01-22

## 原因

1. ddf 旧的api 获取不到房源，新的api 可以获取到

## 解决办法

1. 根据文档，获取数据例子到 mlsImport/creaReso/dataSample。
2. config获取creaReso的user和pass，添加creaReso class 根据user和pass获取token，并在token过期后，自动获取新的token。
3. creaReso的普通接口，会返回nextLink，根据nextLink获取数据。The limit of '100' for Top query。 replication api 只返回ListingKey和ModificationTimestamp，不能用。
4. 在merged中合并ListOffice, CoListOffice, ListAgent, CoListAgent，OpenHouses。
5. 沿用旧数据的_id，sid使用ListingId，_id 使用DDF+ListingKey。
6. 新建creaResoDownload.coffee，获取ddf数据，保存到数据库。
7. crea中openhouse没有时间戳，而且oh变化后，不会体现在prop的ModificationTimestamp上，另外，oh修改会直接有个新的OpenHouseKey，旧的OpenHouseKey直接在crea接口中就查不到了
8. openHouse 先每天凌晨获取全量，数据保存到变量gOpenHouseDict中，然后更新到merged表中（如果prop不存在，则将其加入prop queue），然后获取prop中ohMt【加index】小于今天，id放到queue。在处理prop时候，mergeResourceData从gOpenHouseDict中获取oh，更新到merged中，如果没有则删除OpenHouses时候同时删除ohMt。
9. 将merged中的"Media" 只保留url，m（如果有），整体压缩后，保存到数据库phoUrls，并保存图片数目pho，保存第一张图为thumbUrl。
10. 在raw和merged中添加status字段，初始为A，每天2am通过crea接口获取active prop，如果prop不存在，则将status设置为U，删除StandardStatus。


## 确认日期:    2025-01-23

## online-step
0. rmconfig中approve PR add creaReso config, 在local.ini添加creaReso的user和pass
1. init es
   ```
   ./start.sh -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init"
   ```
2. 重启server
3. 重启watchAndImportToProperties， 参数一样，替换 -t crea -> -t creaReso
4. 运行crea reso download
```
./start.sh -t batch -n creaResoDownload -cmd 'lib/batchBase.coffee mlsImport/creaReso/creaResoDownload.coffee init routine'
```

