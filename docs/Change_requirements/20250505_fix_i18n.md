# 需求 [fix_i18n]

## 反馈

1. liurui 反馈正常的翻译被删除了：Listing updates on your saved searches, homes, communities and locations. The more precise search criteria and location scope you set, the more precise results you get.

## 需求提出人:    liurui

## 修改人：      Maggie

## 提出日期:      2025-05-05

## 原因

1. 正常的翻译长度超过100，被src/lib/i18n.coffee中增加的正则删除：
# 新增规则：字符串长度超过100
  {
    regex: /.{100,}/,
    message: 'String length exceeds 100 characters'
  }

## 解决办法

1. I18n_clean 对于正则匹配删除数据时,添加没有翻译的条件 `{zh: {$exists: false}}`
2. I18n_clean 将删除的数据先保存到备份数据库I18n_delete
3. 修改i18nRegexArray中过滤HTML/XML标签的正则表达式

## 影响范围

1. 符合i18nRegexArray 正则且没有翻译的，才会被删除

## 是否需要补充UT

1. 不需要补充UT,需要修复failed UT

## 确认日期:    2025-05-05

## online-step

1. 重启server
