# 需求 [fix_trebMedia]

## 反馈

1. TRBC11965003 一开始没有图，后来有图了

## 需求提出人:    Rain

## 修改人：       Maggie

## 提出日期:      2025-04-17

## 原因

1. trebResoDownload在保存property的时候，使用mergedCol.replaceOne没有保留phoUrls，导致需要等queue获取到后才能添加上。


## 解决办法

1. 在replaceOne 之前保留phoUrls, thumbUrl, pho，不再保留media。在后面filterCompressTrebResoMedia处理时候，如果新更改的没有media，会相应的unset掉phoUrls, thumbUrl, pho

## 影响范围

1. trebResoDownload

## 是否需要补充UT

1. 不需要添加UT

## 确认日期:    2025-04-17

## online-step

1. 重启trebResoDownload