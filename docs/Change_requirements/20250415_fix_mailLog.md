# 需求 [fix_mailLog]

## 反馈

1. Rain提出需求:mail_log分为mail_log_priority/mail_log_edm， 并且checkAndResendEmailWhenSoftBounce应该检查mail_log_priority, EDM发送失败不需要重试

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-04-15

## 原因

## 解决办法

1. 修改rmconfig中关于mail_log的表名与数据库
2. 修改mailLog的表结构
3. 调用sendMail的地方,邮件补充eventType字段,发送邮件tags添加该字段信息
4. checkAndResendEmailWhenSoftBounce时检查tags.eventType,如果不是指定的eventType时不进行查找详情并重试
5. 历史数据不用处理,会自动过期

## 添加的eventTyp

1. Registration               文件位置: src/apps/80_sites/AppRM/user/userPost.coffee    需要查找详情并重试
2. ResetPasswordEmail         文件位置: src/apps/80_sites/AppRM/user/userPost.coffee    需要查找详情并重试
3. ForgotPwdAutoResetEmail    文件位置: src/apps/80_sites/AppRM/user/userPost.coffee    需要查找详情并重试
4. RegistrationConfirmation   文件位置: src/libapp/oauth.coffee                         需要查找详情并重试
5. BatchSendMail              文件位置: src/batch/sendMail.coffee
6. Transaction                文件位置: src/apps/80_sites/AppRM/0_common/transaction.coffee
7. EmailTest                  文件位置: src/apps/80_sites/AppRM/admin/sysAdmin.coffee
8. TopListingEmail            文件位置: src/apps/80_sites/AppRM/adRelated/propTopUp.coffee
9. TrustedAssignmentEmail     文件位置: src/apps/80_sites/AppRM/form/formManage.coffee
10. SendSpecificEmailForFub   文件位置: src/apps/80_sites/AppRM/form/formManage.coffee
11. CrimeImportStarted        文件位置: src/apps/80_sites/AppRM/ioNhooks/crime.coffee
12. CrimeImportFinished       文件位置: src/apps/80_sites/AppRM/ioNhooks/crime.coffee
13. DataImportStarted         文件位置: src/apps/80_sites/AppRM/ioNhooks/dataImport.coffee
14. DataImportFinished        文件位置: src/apps/80_sites/AppRM/ioNhooks/dataImport.coffee
15. ChatEmail                 文件位置: src/apps/80_sites/AppRM/user/chat.coffee
16. ContactEmail              文件位置: src/apps/80_sites/AppRM/user/userPost.coffee
17. ErrorReportEmail          文件位置: src/apps/80_sites/AppRM/user/userPost.coffee
18. EdmTestEmail              文件位置: src/apps/80_sites/WebRM/edm.coffee
19. EdmTopListingNotify       文件位置: src/batch/topListingNotify.coffee
20. EdmNotify                 文件位置: src/batch/edm/edmBatchHelper.coffee
21. BatchInRealGroupStats     文件位置: src/batch/export/inRealGroupStats.coffee
22. BatchFormDump             文件位置: src/batch/form/formdump.coffee
23. BatchI18n                 文件位置: src/batch/i18n/i18n.coffee
24. BatchMailConfigTest       文件位置: src/batch/test/mailConfig.coffee
25. ServiceAlertEmail         文件位置: src/lib/appBase.coffee,src/lib/coffeemate3.coffee
26. NotifyAdmin               文件位置: src/lib/sendMail.coffee
27. SysNotify                 文件位置: src/model/sysNotify.coffee

## 影响范围

1. mail_log保存数据表名与表结构修改
2. softBounce只会查找重试以下eventType:['Registration','ResetPasswordEmail','ForgotPwdAutoResetEmail','RegistrationConfirmation']

## 是否需要补充UT

1. 需要修复/添加UT

## 确认日期:    2025-04-15

## online-step

1. 更新rmconfig(https://github.com/real-rm/rmconfig/pull/15)
2. 重启server
