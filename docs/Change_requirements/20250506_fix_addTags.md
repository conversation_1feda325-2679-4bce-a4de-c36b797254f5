# 需求 [fix_addTags]

## 反馈

1. Rain反馈以下error log
`
INFO:batch/prop/watchAndImportToProperties.coffee,libapp/saveToMaster.coffee,2025-05-05T22:40:06.843
 saveToMongo new ID DDF28263247
TypeError: Cannot read properties of undefined (reading '0')
    at /opt/rmappweb/src/libapp/propertyTagHelper.coffee:148:32
    at /opt/rmappweb/src/libapp/schoolHelper.coffee:409:14
    at Database_method_setup.Database.findToArray (/opt/rmappweb/src/lib/mongo4.coffee:1115:18)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
ERROR:batch/prop/watchAndImportToProperties.coffee,libapp/saveToMaster.coffee,2025-05-05T22:40:06.849
 libapp/saveToMaster.coffee createNewProp 1246 TypeError: Cannot read properties of undefined (reading '0')
`

## 需求提出人:    Rain

## 修改人：       Lu<PERSON> xia<PERSON>ei

## 提出日期:      2025-05-06

## 原因

1. sch_rm_merged表中存在有bnds但是没有loc的记录。`{ _id: 'ON022860' }`

## 解决办法

1. 修改src/libapp/propertyTagHelper.coffee#91 行,添加loc判断,如果没有loc时warning log

## TODO

1. 学校现在数据有点问题,有些排名靠前的没lat,lng。需要刘蕊确认修复

## 影响范围

1. 会输出sch_rm_merged表中存在有bnds但是没有loc的记录

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-06

## online-step

1. 重启server
2. 重启watch&import
