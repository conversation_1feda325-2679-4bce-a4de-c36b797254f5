# 需求 [fix_CMAField]

## 反馈

CMA的kitchen和Locker也没有值

## 需求提出人:    Rain

## 修改人：       zhangman

## 提出日期:      2025-04-15

## 原因

1. kitchen主要的字段应该是kch，之前查找的是num_kit;
2. Locker没有值是因为mapping时没有将第三方获取的Locker字段转成我们之前的lkr缩写
3. Estimated Price TRBN12081719房源详情页有值，CMA没值
4. Lot Size 现在有一个统一计算后的字段lotsz

## 解决办法

1. kitchen查找kch字段并显示；
2. Locker我先加上Locker字段，后期看罗晓伟是否需要修改mapping部分和batch修改现有数据；
3. 没有获取sqft1:1,sqft2:1,rmSqft: 1
4. Lot Size 增加lotsz字段，优先显示lotsz，但是之前的数据是没有加这个字段的，所以之前的展示方式需要保留
5. 和Tao沟通CMA的Included主要是水电暖，发现Treb出租的房源有一个字段叫RentIncludes，"RentIncludes": [
    "Building Insurance",
    "Building Maintenance",
    "Central Air Conditioning",
    "Common Elements",
    "Exterior Maintenance",
    "Heat",
    "Parking",
    "Recreation Facility"
    ],将数据截图给Tao确认数据是否是需要的，Tao说要Heat，Parking，其他可以不显示。但是我考虑到万一存在内容拼写错误，以及这个例子只是一部分数据，可能还有其他的值也可能是需要的，所以这个值的内容还是原封不动显示。

## 确认日期:    2025-04-15