# 需求 [fix_lotsz]

## 反馈

1. sales反馈房源N11975852的lotsz存在问题,在treb中为13487,但是app中为9311.52

## 需求提出人:   Maggie

## 修改人：      <PERSON><PERSON><PERSON>

## 提出日期:     2025-04-09

## 原因

1. trebReso的数据来源新增了LotSizeArea,LotSizeAreaUnits字段,之前没有该字段时通过front_ft与depth计算得到lotsz值

## 解决办法

1. trebReso添加LotSizeArea,LotSizeAreaUnits字段的mapping
   1. 优先使用LotSizeArea,LotSizeAreaUnits字段计算lotsz
   2. 当LotSizeAreaUnits不存在,LotSizeArea存在时,使用LotSizeUnits计算lotsz
   3. 当LotSizeArea,LotSizeAreaUnits都不存在时,使用front_ft与depth计算lotsz
2. 添加batch修复trebReso的lotsz数据(其它board确认过不存在该问题)

## 影响范围

1. trebReso房源的lotsz计算与显示

## 是否需要补充UT

1. 不需要补充UT

## 代码修改与AI使用占比

1. 代码修改基本全部由AI生成,大约占用时间为1h(包含与AI对话完善需求以及修改,测试)
2. 有2行代码为手动修改(src/libapp/impMappingRESO.coffee#450,451),由于是简单修改所以直接手动修改
3. 数据问题确认以及等待AI大约占用时间为2h(AI经常在一个对话中卡住,需要更换chat后可以继续完成需求)

## 确认日期:    2025-04-09

## online-step

1. 重启watch&import
2. 执行batch修复数据
```shell
./start.sh -n fix_lotsz -cmd "lib/batchBase.coffee batch/prop/fix_lotsz.coffee dryrun"
测试服dryrun结果:
Completed, Total process time 4.4237166666666665 mins, processed(8035):1.816K/m, dryRun(8018):1.812K/m, noLotSizeArea(17):3.842/m
对于noLotSizeArea的结果,已经确认LotSizeArea的值均为0,可以不做处理
```
