# 需求 [fix_pushNotify]

## 反馈

1. sales反馈匹配到savedSearch的新上房源没有推送通知

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-06-12

## 原因

1. src/libapp/properties.coffee#2418 func->propPassSearchCondition 针对ptype2的过滤判断存在问题,eg:查询条件["Semi-Detached","Townhouse"]与房源["Townhouse","Attached"]应该是passed，但是代码判断为failed

## 解决办法

1. 修改func->propPassSearchCondition针对ptype2的判断逻辑
2. func->propPassSearchCondition添加unit test

## 确认日期:    2025-06-13

## online-step

1. 重启watchPropAndPushNotify
