# 需求 [fix_trebReso_deleted_medias]

## 反馈

1. 有一个listing[*********]在MLS上前天已经Terminated 并删除所有照片了，但是现在在房大师上还是能看到照片

## 需求提出人:   Sophie

## 修改人：      Maggie

## 提出日期:     2025-03-26

## 原因
*********先删除了图片，然后修改了listing状态，导致ModificationTimestamp改变，之前isResourceNewer返回false
prop：
 "ModificationTimestamp": "2025-03-28T00:27:36Z", "TerminatedEntryTimestamp": "2025-03-28T00:27:35Z",
media 最后更新"ModificationTimestamp": "2025-03-28T00:27:18.419042Z",

我们系统：
prop： 
 _mt: ISODate('2025-03-28T00:32:40.632Z')
media：
 _mt: ISODate('2025-03-28T00:31:47.008Z')


## 方案
1. 确认trebResoDownload中只有通过时间戳获取到的最新resource才会走到mergeResourceIntoProperty-》generateSetForPropertyResource-》shouldUpdateResource，所以不再需要判断isResourceNewer，去掉这一步的判断。
2. 这个数据加入Queue，修改这个prop


   
## 影响范围
1. trebResoDownload

## 是否需要补充UT
1. 不需要

## 确认日期:    2025-03

## online-step

1. 重新启动trebResoDownload
2. 修复prop
```
 db.reso_treb_evow_property_queue.insertOne({
    "_id": "*********",
    "dlShallEndTs": ISODate("1970-01-01T00:00:00.000+0000"),
    "priority": NumberInt(20000)
  })
```
   