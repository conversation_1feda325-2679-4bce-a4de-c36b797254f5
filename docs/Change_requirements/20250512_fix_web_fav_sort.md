# 需求 [fix_web_fav_sort]

## 反馈

1. Tao 反馈客户想要网页版的收藏添加最近客人

## 需求提出人:    Tao

## 修改人：       liurui

## 提出日期:      2025-05-09

## 原因

1. 之前未提供

## 解决办法

1. 修改src/react_components/favorite/FavoriteGroupModal.js#67 行,收藏成功后添加 window.sortByMtWithoutDefault 处理
2. 增加显示，可以选择根据name/mt进行排序
3. 修改显示样式，放在右边高度全屏



## 影响范围

1. web端list页和detail页

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-12

## online-step

1. 重启server
