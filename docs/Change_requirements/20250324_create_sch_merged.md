# 需求 [create_sch_merged]

## 反馈

添加merged程序 
Merged更新方式:从各个表里面拿值拼起来
source mt> merged mt 更新数据
新加数字id ，后期对应学校 
	每一个省份对应数字（100000）
	按照学校名字排序后，省份+学校（10000）+0
	
不保存非active的学校

## 需求提出人:    Fred

## 修改人：      liurui

## 提出日期:     2025-01

## 原因


## 解决办法

1. 添加batch生成sch_rm_merged数据

## 确认日期:    2025-03-24


## OnlineStep
1. 由于之前的sch_rm_merged表数据是经过核对的，可以作为生成merge表的参考，所以将原来的表名修改为sch_rm_merged_manual，保留
```
  db.getCollection('sch_rm_merged').renameCollection('sch_rm_manual_merged')

```
2. 更新有rmrank的数据isactive为1，并添加数据到manual表中

```shell
 sh start.sh -t batch -n changeSchoolToActive -cmd "lib/batchBase.coffee batch/correctData/20250226_changeSchoolToActive.coffee"
```

3. init数据,init前需要保证sch_rm_manual和sch_findschool有数据，测试可以添加参数dryrun
```
  sh start.sh -t batch -n generateSchRmMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee init"
```

4. 日常更新,在 findschool batch结束之后

```
  sh start.sh -t batch -n generateSchRmMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee"
```