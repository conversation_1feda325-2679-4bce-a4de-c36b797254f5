# 需求 [fix_trans]

## 反馈

1. Grok: Too many requests: RejectLimits(LimitsInfo { id: Buf("/team:f63fb6db-3854-4fbe-8594-303c944f8bbc/u:3d9f2491-679a-47e9-9046-906bd88efdb8"), req_type: Buf("/rt:grok-2-1212"), actual: Values { rps: 1, rph: 0 }, expected: Values { rps: 1, rph: 1200 } }) Some resource has been exhausted

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-02-21

## 原因
rps: 1, rph: 1200 目前的translate超过了

## 解决办法

1. 在GrokTranslator class 中将message 存入translationQueue，然后批量处理
2. 添加lastCallTs和isTranslating，根据上一次query的时间，wait相应的时间，并添加防止重入。
watchPropAndTranlate需要满足：
  1.不是所有translate的接口都有限制
  2.其他app内也会调用翻译
  3.随时会切换translate的提供方，应该只改动gDefaultBatchTranslateServiceName值
   

## 确认日期:    2025-02-26

## online-step

1. 重新启动watchPropAndTranslate
