# 需求 [add_school_rank]

## 反馈

1.增加升学排名
2.增加滑铁卢大学数据

## 需求提出人:    Tao

## 修改人：      liurui

## 提出日期:     2025-03-07

## 原因
以前未提供

## 解决办法

1. 增加batch更新数据
2. 修改前端显示

## 确认日期:    2025-03-14


## 父记录
  create_sch_merged 生成merged表
  新添加的排名数据，保存在sch_rm_rank表时，使用了对应学校的_id，所以需要在create_sch_merged之后再跑


## Online step

1.将sch_eqao表中保存的rmrank和rmG3Ranking，rmG6Ranking，rmG9Ranking数据更新至sch_rm_rank表中
```
sh start.sh -t batch -n school_move_rmrank -cmd "lib/batchBase.coffee batch/correctData/20250407_school_move_rmrank.coffee"
```

2.将private_schools中保存的ivyRank数据更新至sch_rm_rank表中
```
 sh start.sh -t batch -n prischool_move_ivyrank -cmd "lib/batchBase.coffee batch/correctData/20250407_prischool_move_ivyrank.coffee"
```

3.更新AdjFactors.xlsx文件，添加滑大数据
最新数据文件已更新至/rmlfs/xlsx文件夹下
```
sh start.sh -t batch -n update_adj_factors -cmd "lib/batchBase.coffee batch/school_rank/update_adj_factors.coffee -f /rmlfs/xlsx/2024AdjFactors.xlsx"
```


4.更新School-University-data-summary.xlsx文件，添加top100数据
最新数据文件已更新至/rmlfs/xlsx文件夹下
```
sh start.sh -t batch -n update_top100_school_rank -cmd "lib/batchBase.coffee batch/school_rank/update_top100_school_rank.coffee -f /rmlfs/xlsx/2024School-University-data-summary.xlsx  -y 2024"
```

5.检查排名没有问题后删除sch_eqao中的rmrank和private_schools中的ivyRank
···
db.getCollection("sch_eqao").updateMany {rmRank:{$exists:true}}, {$unset:{rmRank:1}}
db.getCollection("private_schools").updateMany {ivyRank:{$exists:true}}, {$unset:{ivyRank:1}}
···
