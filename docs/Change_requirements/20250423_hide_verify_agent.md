# 需求 [hide_verify_agent]

## 反馈

1. 登录隐藏选项：默认都是访客
2. 普通用户只能认证为商家
3. 普通用户变成经纪怎么操作？使用注册是的待认证机制？


## 需求提出人:    Allen


## 修改人：      liurui

## 提出日期:
  2025-04-01  1，2，3


## 原因
之前未提供

## 是否需要补充UT
  否

## 解决办法

1. 注册隐藏权限选择部分（注册页面。账号绑定页面）
2. setting页面认证固定跳转的url
3. 优化待认证机制，可以在用户管理页面，添加待认证标识（waitingVerifyAgent），不直接跳转页面，先验证邮箱，之后在登陆成功后判断有标识跳转认证页面


## 确认日期:    2025-04-11


## online-step/上线步骤
1. 重启server








