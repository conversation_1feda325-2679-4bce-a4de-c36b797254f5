# 需求 [fix_cmty_stat]

## 反馈

1. Tao反馈Central Erin Mills社区在市房源库存统计数据错误

## 需求提出人:   Tao/Fred

## 修改人：      <PERSON><PERSON><PERSON><PERSON>

## 提出日期:     2025-02-06

## 原因

1. elasticsearch的统计数据时，存在cmty大小写不一致问题，导致cmty统计数据错误

## 解决办法

1. 在检索cmty时忽略大小写

## 确认日期:    2025-02

## online-step

1. 重启server
2. 执行统计batch，重新统计近两年半月/周数据
```shell
./start.sh -t batch -n statsLastW -cmd "lib/batchBase.coffee stats/prop_mw.coffee -n 110 LastW"
./start.sh -t batch -n statsLastM -cmd "lib/batchBase.coffee stats/prop_mw.coffee -n 30 LastM"

NOTE: 
  1. 因为测试服房源数量不同 统计时间存在差异,并且没有speedMeter,所以没有添加执行结果
  2. 由于跑近两年半时间的统计数据,时间过长,所以使用-t batch执行
```
