# 此文件仅用来展示如何创建和沟通需求

# requirements detail template如下
```
### 需求 [branchName]

## 反馈
1. XXX 发送安卓推送收不到

(# Sample Data)

## 需求提出人:   XXX
## 修改人:       YYY

## 提出日期
1. 2024-04-22 提出反馈1

## 原因
1. 经过测试xxx文件，结果为xxx版本问题

## 影响范围
1. 列出此修改会影响的所有模块和功能
2. 标注可能受影响的上下游系统
3. 评估对性能、数据一致性等方面的影响
4. 分析不同用户角色下的使用场景影响

## 是否需要补充UT
- [ ] 是
- [ ] 否
如选"是"，说明需要补充的测试用例范围：
1. 
2. 

## 测试验证方案/测试方法 （需要测试与修改相关的功能，防止改一个bug，改出另外的bug）
1. 描述如何验证此修复是否成功
2. 列出需要执行的测试场景
3. 说明如何进行回归测试


## 解决办法/方案
1. 更新版本1.0.4 -> 1.1.3


## 需求确认日期
1. 2024-04-22

## 测试结果/
测试服batch执行结果:
 Total process time 7.320033333333333 mins, processed(715):97.67/m, noTrbProp(292):39.89/m, needMerge(423):57.78/m

## online-step/上线步骤
1. 运行batch
./start.sh  -n fix_oreb_merge -cmd "lib/batchBase.coffee batch/prop/fix_oreb_merge.coffee dryrun"


```
# 如何完成需求修改
0. 从[release]上checkout -b [branchName]
1. 写需求文档，可以复制上面的模板
2. 文档完成后，找需求提出人确认需求和解决办法符合提出人的想法
   1. 如何确认：需要复述提出人的问题和解决办法, 因为不同人对于问题和解决办法和理解不同
3. 开始开发工作
4. 开发完成后，找需求提出人确认实现部分功能没问题
5. 提出Pull Request， 进行Peer Review
6. Review 完成后群里@负责人 推上线







--------------------------

<!-- ### 需求1 [manual_import]
反馈: tao/sales反馈有些房源在市状态没有更新
原因: debug结果为房源在treb的node-rets 返回未找到，可能因为房源从evow系统撤出
解决办法:
1. 增加手动页面输入id，管理员可以从该页面copy id list，然后去treb系统手动导入
2. +batch从数据库自动查找10天没跟新的active房源，加入到该db，自动程序可以重新按照id查找这些房源
3. 管理页面可以更新房源最新状态


### 需求2 [fix_importDelProp]
反馈: tao/sales反馈有些房源在市状态没有更新2
原因: debug结果为房源已经删除，但是watchImportToMongo 重新import了
解决办法:
1. fix import logic, 不导入del房源
2. +batch, masterdb标记该房源为del，从properties/user_listing删除该房源
 -->
