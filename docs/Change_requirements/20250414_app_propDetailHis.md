# 需求 [app_propDetailHis]

改版房源详情页基础信息，房源估价以及房源历史等模块

## 需求提出人:    Allen

## 修改人：       zhangman

## 提出日期:      2025-04-08

## 解决办法

1. 根据20250408房源详情房源动态@3px.png设计稿进行修改。关键信息部分默认折叠可以左右滑动，点击向下箭头可以展开，展开的信息中右侧第一个增加Type字段；
2. building名字等信息放在Key Facts上面；
3. 相似房源按钮放在watch下面；
4. 把房源估价放到Open House上面，Customize按钮样式调整，增加更新时间显示；
5. 房源历史模块顺序和样式调整；
6. 房源图片如果上下或者左右有留白，但是对于放大图片没有太大变形的话，将图片填满
7. /1.5/props/lstdhist 获取的历史记录增加saleTpTag状态显示, 如果是sale显示绿色背景，其余都是灰色背景。sold字体颜色比delisted颜色偏黑。
8. 房源详情页顶部的图片展示，如果左右或上下留白不多时，将图片放大到整个容器。
9. 将RM房源 verified-status 等信息放在On off market位置，之前的On off market不显示，样式参考人口统计部分。
10. 修复房源详情页图片对齐问题。

## 是否需要补充UT

1. 需要修改UT 

## 确认日期:    2025-04-22