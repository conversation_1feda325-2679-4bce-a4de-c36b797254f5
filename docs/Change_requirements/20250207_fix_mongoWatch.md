# 需求 [fix_mongoWatch]

## 反馈

1. fred反馈batch/prop/watchPropAndUpdateMongo.coffee 跑不过去，可能是index时间太长。
```
Stream End: 5176115/5176115
MongoError: props_part.rename MongoServerError: cannot perform operation: an index build is currently running for collection with UUID: f1ed3459-2d85-469e-8470-0aab08413523. Found index build: cc9ef984-f7f5-436c-9807-bd8feceee7c9
    at Connection.onMessage (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection.js:202:26)
    at MessageStream.<anonymous> (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/connection.js:61:60)
    at MessageStream.emit (node:events:514:28)
    at processIncomingData (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/message_stream.js:124:16)
    at MessageStream._write (/opt/rmappweb/src/node_modules/mongodb/lib/cmap/message_stream.js:33:9)
    at writeOrBuffer (node:internal/streams/writable:399:12)
    at _write (node:internal/streams/writable:340:10)
    at MessageStream.Writable.write (node:internal/streams/writable:344:10)
    at TLSSocket.ondata (node:internal/streams/readable:785:22)
    at TLSSocket.emit (node:events:514:28)
    at addChunk (node:internal/streams/readable:343:12)
    at readableAddChunk (node:internal/streams/readable:316:9)
    at TLSSocket.Readable.push (node:internal/streams/readable:253:10)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:190:23)
 {
  ok: 0,
  code: 12587,
  codeName: 'BackgroundOperationInProgressForNamespace',
  '$clusterTime': {
    clusterTime: new Timestamp({ t: 1738826594, i: 1204 }),
    signature: {
      hash: Binary.createFromBase64("J5h2wPbhxGfjgKnl6gUuK8X9mHk=", 0),
      keyId: new Long("7420741505008009226")
    }
  },
  operationTime: new Timestamp({ t: 1738826594, i: 1204 }),
  [Symbol(errorLabels)]: Set(0) {}
} [ 'props_part_backup', { dropTarget: true } ]
ERROR:batch/prop/watchPropAndUpdateMongo.coffee,libapp/mongoTableHelper.coffee,2025-02-06T02:23:14.957
 libapp/mongoTableHelper.coffee Database_method_setup.Database.<computed> 17 Error: error when rename props_part -> props_part_backup
```

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2025-02-06

## 原因

1. 创建index时间太长，导致在rename时出现问题

## 解决办法

1. 在rename的时候添加index是否创建完成判断
2. 由于没有admin权限，不能直接判断索引是否创建完成，改用stats检查每个索引的大小变化
   1. 每3s检查一次索引大小
   2. 每个索引大小差距在1%以内，则认为索引大小稳定
   3. 连续3次检查结果相同，则认为索引创建完成
   4. 超过100次检查(5mins)，则认为索引创建未完成，error log并throw

## 确认日期:    2025-02-07

## online-step

1. 执行以下命令进行mongo watch init操作
```
./start.sh -t batch -n watchPropAndUpdateMongo_init -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init force"
```
