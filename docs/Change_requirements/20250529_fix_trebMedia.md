# 需求 [fix_trebMedia]

## 反馈

1. 经济反馈很多trebreso房源的图片没有更新


## 需求提出人:    Sophie

## 修改人：       Maggie

## 提出日期:      2025-05-29

## 原因

1. get media evow queue的时候returnedCount: 10175大于10000，最后一个X12180437 （45+175条记录）在前10000个时候已经出现，finishedIds 没有判断是否isDone就直接添加上了X12180437，导致最后finishedIds包含所有records，新一轮的filter为null
```
 {
  name: 'get media evow queue',
  ts: 2025-05-29T03:14:41.109Z,
  resourceName: 'media',
  trebType: 'evow',
  mode: 'queue',
  filter: "ResourceRecordKey in ('X12180437','W12161740','W12112558','E12164219','W12167181','W12164519','E12170262','C12147156','W121
60394','E12167032','E12167080','W12170797','C12146231','C12161387','W12161143','C12159483','C12161885','E12134842','C12159958','W12163
429','W12159837','E12158031','W12161402','C12164269','N12160335','C12164271','E12165221','E12165562','W12159901','N12120828','E1215918
5','E12148694','C12159305','E12158859','E12157954','E12131700','W12158809','C12158253','C12159634','W12160157','E12149500','W12160898'
,'N12159137','E12128010','W12160769','E12158099','E12161767','E12158396','E12145124','W12161959') and MediaStatus eq 'Active'",
  top: 10000,
  skip: 0,
  orderby: 'ResourceRecordKey asc',
  count: true,
  updatedCount: 10000,
  updatedTotal: 10000,
  returnedCount: 10175,
  lastMT: '2025-05-29T03:09:24.299963Z',
  ms: 146624,
  isDone: false
}

 {
  name: 'get media evow queue',
  ts: 2025-05-29T03:17:11.734Z,
  resourceName: 'media',
  trebType: 'evow',
  mode: 'queue',
  filter: undefined,
  top: 10000,
  skip: 0,
  orderby: undefined,
  count: true,
  updatedCount: 10000,
  updatedTotal: 20000,
  returnedCount: 506543823,
  lastMT: '2025-05-28T21:20:51.510186Z',
  ms: 304936,
  isDone: false
}
```

## 解决办法

1. 最后需要确认isDone后 再处理添加finishedIds
2. 在query前添加防御步骤，如果filter是空，error并跳出

## 影响范围

1. getAndSaveResourcesAsync queue 一次返回数量超过10000

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-29

## online-step

1. 重启trebResoDownload
