# 需求 [fix_rateLimiter]

## 反馈

1. Rain反馈以下error log
```
ERROR:cmate3.coffee,libapp/propElasticSearch.coffee,2025-03-14T14:35:28.487
 libapp/propElasticSearch.coffee exports.search 2817 searchIndexByBody {
  label: 1,
  src: 'mls',
  ptype: 'Residential',
  oh: false,
  soldOnly: true,
  page: '333',
  city: 'Toronto',
  prov: 'ON',
  psize: 30,
  saletp: 'Sale',
  dom: '-99999',
  no_mfee: undefined,
  max_mfee: undefined,
  locale: 'en',
  notApp: true,
  apisrc: undefined,
  hasloc: undefined,
  searchAfter: undefined,
  isCip: false,
  isPad: false,
  isShare: undefined,
  isPropAdmin: false,
  isVipRealtor: false,
  isVip: false,
  isAssignAdmin: false,
  isAdmin: false,
  isVipUser: false,
  isUserAdmin: false,
  isRealtor: false,
  isRealGroup: false,
  sort: 'auto-ts',
  status: 'U'
} {
  from: 9990,
  sort: [ { ts: { order: 'desc' } } ],
  query: {
    bool: {
      must_not: [
        { exists: { field: 'merged' } },
        { exists: { field: 'del' } },
        { term: { private: true } },
        { term: { src: 'RM' } }
      ],
      must: [
        { term: { city: 'Toronto' } },
        { term: { ptype: 'r' } },
        { term: { prov: 'ON' } },
        { term: { saletp: 'Sale' } },
        { terms: { lst: [ 'Sld', 'Pnd', 'Cld' ] } },
        { range: { spcts: { gte: 1751-05-31T04:00:00.000Z } } },
        { term: { status: 'U' } }
      ]
    }
  },
  size: 30
} ResponseError: search_phase_execution_exception
Caused by:
illegal_argument_exception: Result window is too large, from + size must be less than or equal to: [10000] but was [10020]. See the scroll api for a more efficient way to request large data sets. This limit can be set by changing the [index.max_result_window] index level setting.
```

## 需求提出人:   Rain

## 修改人：      Luo xiaowei

## 提出日期:     2025-03-16

## 原因

1. es的查询限制了from + size需要在10000以内
2. 用户可能是在抓取数据,page跳到了333页

## 方案

1. es查询提前添加限制: from+size <= 10000
2. rateLimiterV2添加爬虫检测
3. 爬虫检测默认关闭,仅在'/1.5/props/search' 接口添加爬虫检测

## 爬虫检测实现说明

### 检测原理

爬虫检测主要基于以下三种行为模式识别：

1. **高页码访问检测**：当用户访问超过设定阈值的页码（默认100页）时，直接判定为爬虫行为并拉黑
2. **连续页码访问检测**：记录用户页码访问序列，当检测到有规律的连续递增或递减页码访问达到阈值（默认8次）时判定为爬虫
3. **高频高页码访问检测**：在一定时间窗口内，高页码（>最大页码限制的50%）的访问次数超过阈值（默认5次）时判定为爬虫

### 配置参数

在`rateLimiterV2.coffee`中新增了以下爬虫检测相关配置：

```coffeescript
# 爬虫检测相关配置
MaxPageNumber: 100              # 最大页码限制
PageSequenceThreshold: 8        # 连续页码序列长度阈值
HighPageAccessThreshold: 5      # 高页码访问次数阈值 
PageAccessWindowTime: 120000    # 页码访问记录窗口时间（毫秒），默认2分钟
ScraperDetectionEnabled: true   # 是否启用爬虫检测
HighPagePercentage: 0.5         # 高页码判断,超过最大页码限制百分比，默认50%
```

### 数据存储

为了实现爬虫检测，新增了以下数据结构：

```coffeescript
# 爬虫检测相关数据结构
@pageAccessRecords = {}       # 存储会话页码访问记录 
@ipPageAccessRecords = {}     # 存储IP页码访问记录
```

### 核心实现流程

1. **页码提取**：通过`extractPageNumber`方法从请求中提取页码信息，支持从URL查询参数、URL路径和POST请求体中提取
2. **访问记录**：通过`recordPageAccess`方法记录用户访问的页码和时间戳，并自动清理过期记录
3. **模式检测**：
   - `detectSequentialPageAccess`：检测连续页码访问模式
   - `detectHighPageFrequency`：检测高频高页码访问
4. **自动拉黑**：通过`blockIPAndSession`方法自动将检测到的爬虫IP和会话添加到黑名单

### 数据清理策略

为了避免内存占用过大，系统会定期清理过期的页码访问记录：
1. 每次记录新页码访问时，清理该IP/会话的过期记录
2. 每47秒（与已有的`checkAccessRate`周期相同）执行全局清理
3. 每24小时执行一次完全重置

### 误判防护措施

为避免误判，系统采取了以下防护措施：
1. 搜索引擎爬虫（Google、Bing、Baidu等）自动豁免检测
2. 白名单IP豁免检测
3. 多重判断标准，单一异常行为不会触发拉黑(主要指:访问了高页码,或者进行了几次连续页码访问不会直接拉黑)
4. 配置参数可根据实际业务情况调整优化

## 确认日期:    2025-03-19

## online-step

1. 重启server
<!-- 可以通过设置config中的limitAccessRate字段,来控制爬虫检测参数,如果没有使用默认值 -->
