# 需求 [move_rni_coll]

## 反馈

1. 记录改到 tmp.import_watched_records, 如果更新字段少(_mt,ts等)不插入
2. mail_log 记录挪到 tmp.mail_log

## 需求提出人:    Rain

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-04-01

## 原因

1. rni的部分collection占用了过多的空间

## 解决办法

1. import_watched_records移动到tmp数据库
2. import_watched_records添加数据更新判断
   1. 修改表结构,{_id:'propId',[{updateDescription,operationType,ns,clusterTime,token,ts}],ts,_mt}
   2. 部分字段的更新不记录详细内容,仅记录字段更新即可(eg: Rooms:[] 改为 RommsUpdate:1),参考src/libapp/watchHelper.coffee下的IGNORE_FIELDS
3. mail_log移动到tmp数据库,修改model/user_email文件, 修改配置文件

## 影响范围

1. import_watched_records记录内容会变少,数据结构发生改变
2. import_watched_records,mail_log会移动到tmp数据库

## 是否需要补充UT

1. 不需要补充/修复UT

## 确认日期:    2025-04-08

## online-step

1. 修改配置文件(_batch_base.ini)中preDefBatchColls中对mail_log的数据库定义, rni -> tmp
2. 重启watch&import
3. 重启server
