# 需求 [fix_bthrms_mapping]

## 反馈

1. Rain反馈BCRE,CREA等board的`bthrms`字段mapping错误

## 需求提出人:    Rain

## 修改人：       Lu<PERSON> xiaowe

## 提出日期:      2025-06-23

## 原因

1. 错误的将`BathroomsTotalInteger`字段mapping到了`bthrms`字段,应该mapping到`tbthrms`字段

## 解决办法

1. 按以下条件修改不同board的mapping
   1. BCRE: BathroomsTotalInteger -> tbthrms, BathroomsFull -> bthrms, BathroomsHalf -> bthhlf
   2. TREB: BathroomsTotalInteger -> tbthrms/bthrms
   3. CREA: BathroomsTotalInteger -> tbthrms/bthrms
   4. CAR:  BathroomsTotalDecimal -> tbthrms, BathroomsFull -> bthrms, BathroomsHalf -> bthhlf
   5. EDM:  BathroomsTotalDecimal -> tbthrms, BathroomsFull -> bthrms, BathroomsHalf -> bthhlf
   6. RHB:  BathroomsTotalDecimal -> tbthrms, BathroomsFull -> bthrms, BathroomsHalf -> bthhlf
   7. OTW:  TotalBathrooms -> tbthrms, NumberofEnsuiteBathrooms -> bthrms, NumberofPartialBathrooms -> bthhlf
2. 修改es query,将`bthrms`的筛选改为`tbthrms`
3. 添加batch修复历史数据
   1. 支持`mt`参数传入,修复`mt`之后的历史数据,默认修复全部数据
   2. 修复逻辑:
      1. `properties`表查询`{src:{$ne:'RM'}}`数据,`projection`限制在`src,bthrms,tbthrms,BathroomsFull,BathroomsHalf,NumberofEnsuiteBathrooms,NumberofPartialBathrooms,FullBaths,HalfBaths`中
      2. 如果`tbthrms`,`bthrms`都存在或都不存在时,跳过
      3. 如果`src`为`['BRE','CAR','EDM','RHB']`时,将原有的`bthrms`改为`tbthrms`,`FullBaths/BathroomsFull`改为`bthrms`,`HalfBaths/BathroomsHalf`改为`bthhlf`,没有`FullBaths/BathroomsFull`时`bthrms`不修改
      4. 如果`src`为`['OTW']`时,将`NumberofEnsuiteBathrooms`改为`bthrms`,`NumberofPartialBathrooms`改为`bthhlf`。如果没有`bthrms`时,将`tbthrms`赋值到`bthrms`上
      5. 如果`src`为`['TRB','DDF']`时,将原有的`bthrms`改为`tbthrms`,`bthrms`不变

## 是否需要补充UT

1. 需要修复UT

## 确认日期:    2025-06

## online-step

1. 重启watch&import
2. 执行batch
```shell
./start.sh -n fix_prop_bthrms -cmd "lib/batchBase.coffee batch/prop/fix_prop_bthrms.coffee dryrun -mt [YYYY-MM-DD]"

测试服执行结果:
Completed, Total process time 8.0586 mins, processed(15062524):1.869M/m, dryRun(13785108):1.710M/m, skipped(1277416):158.5K/m
```
3. 重新init es, 由于batch修改数据过多 可以在执行batch时停止es的watch,在执行完成后重新init
