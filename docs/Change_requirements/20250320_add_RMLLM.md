# 需求 [add_RMLLM]

## 反馈

1.  51.79.29.111:11434开了gemma3:27b.
 curl -N -X POST http://51.79.29.111:11434/api/generate \
     -o /dev/null -s -w 'Establish Connection: %{time_connect}s\nTTFB: %{time_starttransfer}s\nTotal: %{time_total}s\n' \
     -d '{
          "model": "gemma3:12b",
          "prompt": "Translate the following into Chinese. Just the translation, nothing else. \n\n`Perfect 4 Bedroom & 3 Bathroom Detached Home In Heart Of East York * Enjoy Over 2, 300 Sqft Of Total Living W/ Finished Basement W/ A Separate Entrance * Beautiful Brick & Stone Exterior, Custom Glass Cover Front Porch & Gated Front Yard * Professionally Landscaped & Interlocked Front & Back* 123 Ft Deep Lot * Upgraded White Kitchen W/ Custom White Cabinets, New Porcelain Tiles, Quartz Counters to Backsplash, Under Mount Sink W/ Extendable Faucet Overlooking Window* Stainless Steel Appliances * Pot Lights * Upgraded Living Room W/ Accent Wall & Crown Moulding * Brand New Interior Solid Wood Doors * Upgraded Baseboards * Renovated 3 Pc Bathroom On Main W/ Mosaic Tiles In Shower * Primary Bedroom Features Upgraded 3pc Bath, Cathedral Ceilings & Walk Out to Deck* 2nd Floor Laundry * Finished Basement Perfect For Rental W/Kitchen & 2 Bedrooms *Basement Laundry * Fenced Backyard W/ Brand New Composite Deck, Custom`",
          "stream": true
         }' \
     -H "Content-Type: application/json"
 Establish Connection: 0.000259s
TTFB: 0.072752s
Total: 4.253762s

## 需求提出人:   Fred，Rain

## 修改人：      Maggie

## 提出日期:     2025-03-20

## 原因



## 解决办法

1. 添加lib/translator/RMTranslator.coffee
2. 修改batch/watchPropAndTranslate 优先使用RMTranslator

## 确认日期:    2025-03-20

## online-step
0. 合并rmconfig add_rmLLM branch
1. 重启watchPropAndTranslate
