# 需求 [fix_mapping]

## 反馈

1. 调查BRE在rni的数据发现,存在city与prov不一致的情况,fred反馈在mapping那添加下处理,rni来的数据地址，省等字段的可能都会有问题， 需要结合地址，经纬度，省等信息,目前api返回的已经都是BC省了，你在上线步骤里 添加下命令 改下 现在数据。

## 需求提出人:    Fred

## 修改人：       Luo xiaowei

## 提出日期:      2025-05-23

## 原因

1. BcreReso提供的数据存在city在'BC'省,但是prov提供的是其它省份的数据,eg:BRER2971882->`{City: 'Port Coquitlam',StateOrProvince: 'NS'}`,实际应该是'BC'省

## 解决办法

1. 将`bridge_bcre_merged`表中`StateOrProvince`字段不为`BC`的数据改为`BC`
2. 在mapping之后,添加prov,city的检查,如果发现city和prov无法对应的数据,进行warning log
   1. 如果存在经纬度,根据经纬度进行判断(`boundary`表中数据可以判断)
   2. 当经纬度不存在时,通过`prov_city_cmty`表进行查找判断,也可以通过`boundary`表查找判断
   3. 当city与prov不一致时,warning log,然后import(后面观察log决定是否在check之后更新字段)
<!--
BRE目前存在部分city为`No City Value`,maggie在housesigma发现显示的也为`No City Value`,暂时不处理,warning log后正常import
历史数据在数据更新之后,会重新做geoCoding并修改uaddr
-->

## 影响范围

1. 房源import时,会检查city和prov不对应的数据

## 是否需要补充UT

1. 需要针对数据错误的case补充UT

## 确认日期:    2025-05

## online-step

1. 重启watch&import
2. 执行mongo语句修复记录(Maggie已经在api中确认,bcreReso目前省份只存在'BC'的房源)
```mongo shell
db.bridge_bcre_merged.updateMany({StateOrProvince:{$ne:'BC'}},{$set:{StateOrProvince:'BC'}})
```
