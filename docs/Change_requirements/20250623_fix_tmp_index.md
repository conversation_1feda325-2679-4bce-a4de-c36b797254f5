# 需求 [fix_tmp_index]

## 反馈

1. Fred反馈`tmp`数据库`import_watched_records`,`rmDupLogTmp`,`properties_import_log`,collections没有expiry index

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-06-23

## 原因

1. `import_watched_records`由于数据结构发生改变,没有添加expiry index,需要设置一年过期,`_mt`添加expiry index
2. `rmDupLogTmp`,batch未添加expiry index以及相应字段,需要设置一个月过期
3. `properties_import_log`,需要设置一年过期,参考 src/batch/correctData/provClean.coffee#242

## 解决办法

1. 手动添加`import_watched_records`的expiry index。
2. 修改batch->`src/batch/prop/rmDupLog.coffee`,添加`_exp30d`字段,设置expiry index。
3. 手动添加`properties_import_log`的expiry index。

## 影响范围

1. `import_watched_records`,`rmDupLogTmp`,`properties_import_log`的expiry index。

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-23

## online-step

1. tmp数据库执行以下mongo shell,设置`_mt`一年过期
```shell
db.import_watched_records.createIndex({ "_mt": 1 }, { expireAfterSeconds: 31536000 })
db.properties_import_log.createIndex({ "_mt": 1 }, { expireAfterSeconds: 31536000 })
```
2. 确认`rmDupLogTmp` collection是否为一个月前执行batch产生的数据
   1. 如果为一个月前产生的数据,执行以下mongo shell,清空数据库,设置`_exp30d`字段,设置expiry index。
   ```shell
   db.rmDupLogTmp.deleteMany({})
   db.rmDupLogTmp.createIndex({ "_exp30d": 1 }, { expireAfterSeconds: 0 })
   ```
   2. 如果不是(无法确定)一个月前产生的数据,删除`_mt`在一个月前的数据,其它数据设置`_exp30d`为一个月后,并设置expiry index。
   ```shell
   db.rmDupLogTmp.deleteMany({_mt:{$lt:new Date(Date.now() - 2592000000)}})
   db.rmDupLogTmp.updateMany({},{$set:{_exp30d:new Date(Date.now() + 2592000000)}})
   db.rmDupLogTmp.createIndex({ "_exp30d": 1 }, { expireAfterSeconds: 0 })
   ```
