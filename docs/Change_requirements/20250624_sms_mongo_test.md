# 需求 [sms_mongo_test]

## 反馈
添加mongo test，sms支持多个

## 需求提出人:    Rain
## 修改人：       Maggie

## 提出日期:      2025-06-23

## 原因
mongo ca7 没了后，无法读写



## 解决办法
1. 新增batch，四个db，分别做插入，查询，删除操作，cronjob运行
2. sms 支持多个接收人, config: alertSMS = '123444534,5551234567'
  1. 修改 `src/lib/notify.coffee` - 已经不再使用，mv到src/batch/_unused/notify.coffee
  2. 修改 `src/lib/sendSMS.coffee` - 底层SMS服务支持多个接收人
  3. 修改 `src/lib/batchBase.coffee` - batch错误通知支持多个接收人
  4. 修改 `src/lib/appBase.coffee` - 应用错误通知支持多个接收人
  5. 修改 `src/lib/coffeemate3.coffee` - coffeemate错误通知支持多个接收人
3. local.ini 中 如果有比template多的，报错并退出


## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-23

## online-step

1. 重启服务
2. run batch
```
./start.sh -t batch -n mongoTest -cmd "lib/batchBase.coffee batch/test/mongoTest.coffee dryrun"

# cronjob every 5 min

./start.sh -t batch -n mongoTest -cmd "lib/batchBase.coffee batch/test/mongoTest.coffee" -cron '*:0/5'
```

