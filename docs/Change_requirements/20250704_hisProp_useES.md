# 需求 [hisProp_useES]

## 反馈

1. <PERSON>反馈存在大量slow query
```
{"t":{"$date":"2025-07-03T23:19:07.340-04:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn745","msg":"Slow query","attr":{"type":"command","isFromUserConnection":true,"ns":"listing.properties","collectionType":"normal","command":{"find":"properties","filter":{"merged":{"$exists":false},"unt":"2605","$or":[{"uaddr":"CA:ON:TORONTO:16 YONGE ST"},{"geoq":{"$gte":100},"loc":{"$geoWithin":{"$centerSphere":[[-79.37595564388464,43.643363328282646],3.1357300763550272e-06]}}}]},"sort":{"onD":-1},"projection":{"sid":1,"id":1,"lp":1,"lpr":1,"sp":1,"lst":1,"onD":1,"offD":1,"zip":1,"mt":1,"unt":1,"exp":1,"sldd":1,"addr":1,"del":1,"st_num":1,"MlsStatus":1,"src":1,"status":1,"saletp":1},"lsid":{"id":{"$uuid":"f260eedf-2f46-4957-a3e4-e3a7034f32db"}},"$clusterTime":{"clusterTime":{"$timestamp":{"t":1751599146,"i":31}},"signature":{"hash":{"$binary":{"base64":"e6021UKjyd4XIkLCMhxuo5qJ57o=","subType":"0"}},"keyId":7487843956621836295}},"$readPreference":{"mode":"nearest"},"$db":"listing"},"planSummary":"IXSCAN { loc: \"2dsphere\" }, IXSCAN { uaddr: 1, onD: 1 }","planningTimeMicros":215321,"keysExamined":6080,"docsExamined":6065,"hasSortStage":true,"fromPlanCache":true,"nBatches":1,"cursorExhausted":true,"numYields":12,"nreturned":16,"planCacheShapeHash":"4FC62628","queryHash":"4FC62628","planCacheKey":"B5BC0C06","queryFramework":"classic","reslen":4399,"locks":{"Global":{"acquireCount":{"r":13}}},"readConcern":{"level":"local","provenance":"implicitDefault"},"storage":{"data":{"bytesRead":189573357,"timeReadingMicros":125149}},"cpuNanos":116191316,"remote":"192.168.0.101:52024","protocol":"op_msg","queues":{"ingress":{"admissions":1},"execution":{"admissions":14}},"workingMillis":215,"durationMillis":215}}
```

## 需求提出人:    Fred

## 修改人：       Luo xiaowei

## 提出日期:      2025-07-04

## 原因

1. 查找历史房源使用的properties表,耗时超过了100ms。

## 解决办法

1. 历史房源查找函数(findAddrHistory)改用es查找

## 是否需要补充UT

1. 需要补充并修复UT

## 确认日期:    2025-07

## online-step

1. 重新init es
2. 重启server
