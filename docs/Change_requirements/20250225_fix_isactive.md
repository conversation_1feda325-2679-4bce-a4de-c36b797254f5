# 需求 [fix_isactive]

## 反馈

1. 学校列表中g9ranking 排名的29名，25，27，35，50，52，53的学校没显示

## 需求提出人:   tao

## 修改人：      liurui

## 提出日期:     2025-02-19

## 原因


## 解决办法

1. 查找有rmrank但是isactive为0的学校(512)
2. 添加batch，增加这部分学校到merged里面，并增加eqaoId 和 rmrank数据

## 确认日期:    2025-02-19

## online-step

1.  更新有rmrank的数据isactive为1，并添加数据到merged表中

```shell
 sh start.sh -t batch -n changeSchoolToActive -cmd "lib/batchBase.coffee batch/correctData/20250226_changeSchoolToActive.coffee"
```

2. 添加默认排序weight字段

``` shell
sh start.sh -t batch -n school_weight -cmd "lib/batchBase.coffee batch/school_weight/school_weight.coffee"
```
