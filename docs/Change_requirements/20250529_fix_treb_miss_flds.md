
# 需求 [fix_treb_miss_flds]

## 反馈

1. sales反馈房源C12174496建筑年份错误
2. rain反馈很多condo没有age信息

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-05-27

## 原因

1. Maggie调查发现trebReso存在`ApproximateAge`字段没有做mapping,该字段需要转为age字段
2. trebReso以下字段被删除,`BuyerAgentFullName`,`BuyerAgentKey`,`BuyerOfficeKey`
3. trebReso新增以下字段需要做mapping处理:
```JSON
[
  "ContactAfterExpiryYN",
  "DDFYN",
  "InternetAddressDisplayYN",
  "KitchensAboveGrade",
  "LeaseTerm",
  "LegalApartmentNumber",
  "LegalStories",
  "MainOfficeKey",
  "MediaChangeTimestamp",
  "OccupantType",
  "OriginatingSystemID",
  "PermissionToContactListingBrokerToAdvertise",
  "PriorMlsStatus",
  "ShowingRequirements",
  "SourceSystemID",
  "SourceSystemName",
  "SpecialDesignation",
  "SquareFootSource",
  "StandardStatus",
  "TerminatedDate",
  "TerminatedEntryTimestamp",
  "TransactionBrokerCompensation",
  "VendorPropertyInfoStatement",
  "ApproximateAge",
  "BuyOptionYN",
  "CentralVacuumYN",
  "CreditCheckYN",
  "EnsuiteLaundryYN",
  "LeaseAgreementYN",
  "LeasedEntryTimestamp",
  "PercentListPrice",
  "RentalApplicationYN",
  "View",
  "AttachedGarageYN",
  "CoolingYN",
  "Exclusions",
  "FoundationDetails",
  "GarageYN",
  "GasYNA",
  "HeatingYN",
  "LinkYN",
  "LotSizeSource",
  "MLSAreaDistrictOldZone",
  "MLSAreaMunicipalityDistrict",
  "PictureYN",
  "RoomsBelowGrade",
  "SewerYNA",
  "TelephoneYNA",
  "WaterYNA",
  "AssociationYN",
  "HSTApplication",
  "ListAOR",
  "MLSAreaDistrictToronto",
  "StatusCertificateYN",
  "LeasedTerms",
  "HandicappedEquippedYN",
  "PriceChangeTimestamp",
  "RollNumber",
  "SystemModificationTimestamp",
  "GreenPropertyInformationStatement",
  "MortgageComment",
  "ShowingAppointments",
  "Status_aur",
  "TaxLegalDescription",
  "ParcelOfTiedLand",
  "SuspendedEntryTimestamp",
  "SoldConditionalEntryTimestamp",
  "SoldEntryTimestamp",
  "BusinessType",
  "ConditionalExpiryDate",
  "ConditionOfSale",
  "CraneYN",
  "FreestandingYN",
  "ListPriceUnit",
  "LotType",
  "OutsideStorageYN",
  "PropertyUse",
  "RetailArea",
  "RetailAreaCode",
  "TaxType",
  "VirtualTourURLUnbranded",
  "UFFI",
  "BasementYN",
  "Directions",
  "ElectricExpense",
  "Expenses",
  "ExtensionEntryTimestamp",
  "FrontageLength",
  "GrossRevenue",
  "HeatingExpenses",
  "MaintenanceExpense",
  "MediaListingKey",
  "NetOperatingIncome",
  "OperatingExpense",
  "OtherExpense",
  "ProfessionalManagementExpense",
  "SignOnPropertyYN",
  "WaterExpense",
  "YearExpenses",
  "LeasedConditionalEntryTimestamp",
  "ClearHeightFeet",
  "ClearHeightInches",
  "ElevatorType",
  "OfficeApartmentArea",
  "OfficeApartmentAreaUnit",
  "Rail",
  "SoilTest",
  "AssignmentYN",
  "Disclosures",
  "KitchensBelowGrade",
  "LeaseToOwnEquipment",
  "RuralUtilities",
  "TaxBookNumber",
  "Waterfront",
  "SuspendedDate",
  "Town",
  "Topography",
  "NumberSharesPercent",
  "LiquorLicenseYN",
  "SurveyAvailableYN",
  "ParkingLevelUnit1",
  "ParkingSpot1",
  "BusinessName",
  "ChattelsYN",
  "FinancialStatementAvailableYN",
  "FranchiseYN",
  "HoursDaysOfOperation",
  "HoursDaysOfOperationDescription",
  "NumberOfFullTimeEmployees",
  "SeatingCapacity",
  "DealFellThroughEntryTimestamp",
  "WaterView",
  "EscapeClauseHours",
  "EscapeClauseYN",
  "FarmType",
  "SalesBrochureUrl",
  "CoBuyerOfficeName",
  "AlternativePower",
  "ParkingLevelUnit2",
  "ParkingSpot2",
  "Sewage",
  "WaterDeliveryFeature",
  "DevelopmentChargesPaid",
  "MaximumRentalMonthsTerm",
  "MinimumRentalTermMonths",
  "ParkingMonthlyCost",
  "VacancyAllowance",
  "CommonAreaUpcharge",
  "AdditionalMonthlyFee",
  "StatisCauseInternal",
  "WaterBodyType",
  "IndustrialArea",
  "IndustrialAreaCode",
  "TruckLevelShippingDoors",
  "Shoreline",
  "ShorelineAllowance",
  "WaterfrontAccessory",
  "WaterFrontageFt",
  "WaterfrontFeatures",
  "VirtualTourURLBranded",
  "VirtualTourURLBranded2",
  "CommunityFeatures",
  "CommercialCondoFee",
  "DoubleManShippingDoors",
  "DriveInLevelShippingDoors",
  "GradeLevelShippingDoors",
  "TruckLevelShippingDoorsHeightFeet",
  "TruckLevelShippingDoorsHeightInches",
  "TruckLevelShippingDoorsWidthFeet",
  "TruckLevelShippingDoorsWidthInches",
  "WaterBodyName",
  "Volts",
  "GreenCertificationLevel",
  "ShorelineExposure",
  "PortionLeaseComments",
  "PercentBuilding",
  "SoundBiteUrl",
  "GarageParkingSpaces",
  "SeasonalDwelling",
  "AddChangeTimestamp",
  "ImportTimestamp",
  "ListingId",
  "MapColumn",
  "MapPage",
  "MapRow",
  "TimestampSQL",
  "SurveyType",
  "DoubleManShippingDoorsHeightFeet",
  "DriveInLevelShippingDoorsHeightFeet",
  "GradeLevelShippingDoorsWidthFeet",
  "VirtualTourURLUnbranded2",
  "OutOfAreaMunicipality",
  "IslandYN",
  "Winterized",
  "LocalImprovements",
  "OriginalListPriceUnit",
  "DriveInLevelShippingDoorsHeightInches",
  "DriveInLevelShippingDoorsWidthFeet",
  "DriveInLevelShippingDoorsWidthInches",
  "DoNotDiscloseUntilClosingYN",
  "CoListOfficeName3",
  "WaterMeterYN",
  "TrailerParkingSpots",
  "WellDepth",
  "TotalExpenses",
  "DoubleManShippingDoorsHeightInches",
  "DoubleManShippingDoorsWidthFeet",
  "DoubleManShippingDoorsWidthInches",
  "OldPhotoInstructions",
  "RoomType",
  "PercentRent",
  "BaySizeLengthFeet",
  "BaySizeWidthFeet",
  "GradeLevelShippingDoorsHeightInches",
  "LeasedLandFee",
  "VirtualTourFlagYN",
  "LeaseAmount",
  "CarportSpaces",
  "BaySizeLengthInches",
  "BaySizeWidthInches",
  "ClosePriceHold",
  "ElectricOnPropertyYN",
  "ParcelNumber2",
  "SoilType",
  "LocalImprovementsComments",
  "TMI",
  "SpaYN",
  "EstimatedInventoryValueAtCost",
  "FarmFeatures",
  "GradeLevelShippingDoorsWidthInches",
  "StreetDirPrefix",
  "WellCapacity",
  "CommercialCondoFeeFrequency",
  "CoListOfficeName4",
  "RoadAccessFee",
  "SoldAreaCode",
  "Year1LeasePrice",
  "Year1LeasePriceHold",
  "Year2LeasePriceHold",
  "Year3LeasePriceHold",
  "SoldAreaUnits",
  "Year4LeasePriceHold",
  "Year2LeasePrice",
  "Year3LeasePrice",
  "Year4LeasePrice",
  "Year5LeasePrice",
  "PriorPriceCode",
  "StructureType",
  "CloseDateHold",
  "LinkProperty",
  "BrokerFaxNumber",
  "AdditionalMonthlyFeeFrequency",
  "DiscloseAfterClosingDate",
  "ZoningDesignation",
  "CoBuyerOfficeName4"
]
```
<!-- 部分字段暂时不做mapping与添加到房源详细中展示 -->

## 解决办法

1. trebReso在mapping时补充字段转换
   1. `ApproximateAge` : `age`
   2. `Directions` : `dir_desc`
   3. `StreetDirPrefix` : `st_dirpfx`
2. trebReso需要修改`vturl`字段的处理逻辑,以下字段都需要转为vturl(参考`src/libapp/impMappingCar.coffee`文件下`formatVturl`函数,考虑dry)
   1. VirtualTourURLUnbranded
   2. VirtualTourURLUnbranded2
   3. VirtualTourURLBranded
   4. VirtualTourURLBranded2
3. 部分字段放在房源详细里面(`src/libapp/propertiesTranslate.coffee`文件中的`RESO_TREB_EXT_FIELDS`变量补充)
   1. `FarmFeatures` : `{parent: 'Land'}`
   2. `IslandYN` : `{parent: 'Land'}`
   3. `LeasedLandFee` : `{parent: 'Land'}`
   4. `LocalImprovements` : `{parent: 'Other'}`
   5. `LocalImprovementsComments` : `{parent: 'Other'}`
   6. `RoadAccessFee` : `{parent: 'Other'}`
   7. `SeasonalDwelling` : `{parent: 'Building'}`
   8. `SignOnPropertyYN` : `{parent: 'Other'}`
   9. `SurveyType` : `{parent: 'Other'}`
   10. `WaterMeterYN` : `{parent: 'Other'}`
   11. `WellDepth` : `{parent: 'Other'}`
   12. `Winterized` : `{parent: 'Other'}`
   13. `AssignmentYN` : `{parent: 'Other'}`
   14. `GarageYN` : `{parent: 'Building'}`
   15. `HeatingYN` : `{parent: 'Building'}`
   16. `SewerYNA` : `{parent: 'Building'}`
   17. `WaterYNA` : `{parent: 'Building'}`
   18. `TelephoneYNA` : `{parent: 'Building'}`
   19. `AssociationYN` : `{parent: 'Building'}`
   20. `LeasedTerms` : `{parent: 'Other'}`
   21. `HandicappedEquippedYN` : `{parent: 'Other'}`
   22. `OutsideStorageYN` : `{parent: 'Other'}`
   23. `BasementYN` : `{parent: 'Building'}`
   24. `CraneYN` : `{parent: 'Other'}`
   25. `HeatingExpenses` : `{parent: 'Other'}`
   26. `MaintenanceExpense` : `{parent: 'Other'}`
   27. `ElectricExpense` : `{parent: 'Other'}`
   28. `OperatingExpense` : `{parent: 'Other'}`
   29. `OtherExpense` : `{parent: 'Other'}`
   30. `ProfessionalManagementExpense` : `{parent: 'Other'}`
   31. `WaterExpense` : `{parent: 'Other'}`
   32. `YearExpenses` : `{parent: 'Other'}`
   33. `TotalExpenses` : `{parent: 'Other'}`
   34. `SoilType` : `{parent: 'Other'}`
   35. `SoldAreaUnits` : `{parent: 'Other'}`
   36. `AdditionalMonthlyFeeFrequency` : `{parent: 'Other'}`
   37. `ZoningDesignation` : `{parent: 'Other'}`
   38. `BuyOptionYN` : `{parent: 'Other'}`
   39. `CentralVacuumYN` : `{parent: 'Other'}`
   40. `CreditCheckYN` : `{parent: 'Other'}`
4. 按以下逻辑修改文件`src/lib/propAddress.coffee`下的`buildAddrFromSt`函数
   1. 如果st_dirpfx != st_dir,将`st_dirpfx`加在st的最前面
   2. 如果st_dirpfx == st_dir,不做处理
5. 添加batch,修复数据(参考文件`src/batch/prop/fix_trebNewFields.coffee`)
   1. 处理没有mapping的`ApproximateAge`,`Directions`,`StreetDirPrefix`字段,添加mapping(`ApproximateAge`为`New`时,需要根据`onD`计算,参考`src/libapp/impFormat.coffee`第366,367行)
      1. `ApproximateAge` : `age`
      2. `Directions` : `dir_desc`
      3. `StreetDirPrefix` : `st_dirpfx`
   2. 修改`properties`数据

## 确认日期:    2025-05

## 影响范围

1. trebReso房源import的字段mapping以及房源详细中信息展示

## 是否需要补充UT

1. 需要针对相关字段补充/修复 ut

## Online step

1. 重启watch&import
2. 重启server
3. 执行batch
```shell
./start.sh -n fix_reso_missFields -cmd "lib/batchBase.coffee batch/prop/fix_reso_missFields.coffee -d [date] dryrun"

测试服结果:
Completed, Total process time 4.105833333333333 mins.  processed(7787854):1.896M/m, skipped(7070287):1.721M/m, needUpdate(717567):174.7K/m
```
