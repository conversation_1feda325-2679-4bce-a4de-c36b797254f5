# 需求 [app_notesOpening]

  笔记扩大使用人群，之前是只有房大师经纪可以使用，现在扩展到所有用户都可以使用(非房大师经纪的普通用户和经纪)。
  1. 与房大师经纪使用的笔记分开表存储。
  2. 非房大师经纪的笔记按照 uaddr + unt(针对building) + uid 作为unique
  3. 非房大师经纪用户进入房源详情点击笔记按钮(笔记按钮进行样式调整)进入笔记编辑页面，房大师经纪保持现有逻辑不变。
  4. 笔记编辑界面：保持现有功能。优化：没有进行任何编辑操作关闭时直接关闭。
  5. saved模块显示：a. Homes 页面房源收藏，非房大师经纪点击笔记图标（没有笔记时icon是灰色，没有文字显示）直接进入编辑页面，房大师经纪保持现有逻辑不变（input框中如果是房大师经纪只显示房大师笔记）。b. Notes 页面，房大师经纪保持现有逻辑。非房大师经纪显示个人笔记列表。
  6. 如果当前用户从房大师经纪转变成非房大师经纪：房大师的笔记在任何页面都不能查看。
  7. 笔记管理员只能查看房大师经纪写的笔记，不能查看任何个人笔记。
  8. 删除账户: a.同步删除个人笔记，房大师笔记不删除。 b.图片关联到房大师笔记的不删除。
  9. 非房大师经纪，房源详情页的笔记icon下方显示Note。没有笔记显示灰色文字和图标，有笔记时显示红色。
  
## 需求提出人:   Allen
## 修改人：      zhangman
## 提出日期:     2024-06-12
## 解决办法

prop_notes_personal Collection

| field   | data type | description      | notes                                                       |
| ------- | --------- | ---------------- | ----------------------------------------------------------- |
| _id     | Auto      |                  | 自动生成                                                    |
| uid     | ObjectId  | 用户的_id         | 和uaddr,unt一起加唯一索引                                       |
| uaddr   | string    | 房源uaddr         | 和uid,unt一起加唯一索引                                         |
| unt     | string    | 针对building有unt | 和uid,uaddr一起加唯一索引                                         |
| memo    | array     |                  |                                                             |
| .tag    | string    | 用户选中的标签   | 针对于用户不选择tag直接填写内容的部分默认创建时tag为'noTag' |
| .m      | string    | 该标签对应的内容 |                                                             |
| .ts     | string    | 创建或更新的时间 |                                                             |
| ts      | string    | 创建时间戳       |                                                             |
| _mt     | Date      | 更新时间戳       | 数据库自动更新                                              |
| propIds | array     | 房源的propId     |                                                             |
| mt      | string    | 更新时间戳       | 保存时手动更新，用于列表显示时间                            |
| nm_en   | string    | 用户英文名       | 从user中获取                            |
| nm_zh   | string    | 用户中文名       | 从user中获取                            |
| avt     | string    | 用户头像链接      |从user中获取                        |
| nm      | string    | 用户nm       |  从user中获取                        |
| pic     | array     | 笔记中上传的图片 |                                                             |

## 确认日期:
2024-07-04（确认需求）