# 需求 [fix_school_rmrank]

## 反馈

新增加了2021-2022的排名表，2022-2023删除了重复的学校，2023-2024新增了born in Canada及删除了重复的学校

## 需求提出人:    Tao

## 修改人：      liurui

## 提出日期:     2025-03-26

## 原因


## 解决办法

1. 更新最新数据

## 确认日期:    2025-03-27

## Online step
跑命令更新数据,最新数据文件已更新至/rmlfs/xlsx文件夹下
```

sh start.sh -t batch -n school_rmrank -cmd "lib/batchBase.coffee batch/school_eqao/school_rank_from_xlsx.coffee -f  /rmlfs/xlsx/2021-2022CleanSchoolEqaoRankingAndVcStats.xlsx -y 2022"

sh start.sh -t batch -n school_rmrank -cmd "lib/batchBase.coffee batch/school_eqao/school_rank_from_xlsx.coffee -f /rmlfs/xlsx/2022-2023CleanSchoolEqaoRankingAndVcStats.xlsx -y 2023"

sh start.sh -t batch -n school_rmrank -cmd "lib/batchBase.coffee batch/school_eqao/school_rank_from_xlsx.coffee -f  /rmlfs/xlsx/2023-2024CleanSchoolEqaoRankingAndVcStats.xlsx -y 2024"
 
```