# 需求 [fix_bcre_fce]

## 反馈

1. Fred反馈我们高级检索的朝向filter，在bc的房源是不是没有作合适mapping？ 有些board用的是全称或者其它方式的拼写吧？

## 需求提出人:    Fred

## 修改人：      Lu<PERSON> xiaowei

## 提出日期:     2025-03-25

## 原因

1. bcreReso朝向mapping只使用了DirectionFaces,需要在该字段不存在时使用BCRES_DirectionFacesRearYard
2. 字段值存在 'East,Northwest’，'Northeast,Southwest' 多方向同时存在的情况，目前逻辑是不进行缩写处理

## board fce字段整理

| Board | 主要朝向字段 | 备选朝向字段1 | 备选朝向字段2 |
|-------|------------|--------------|--------------|
| CAR   | DirectionFaces | FrontageType (array) | - |
| EDM   | DirectionFaces | UNITEXPOSURE (array) | - |
| CLG   | DirectionFaces | UnitExposure (array) | YardFaces |
| TRB   | DirectionFaces | - | - |
| BRE   | DirectionFaces | BCRES_DirectionFacesRearYard | - |
<!-- DDF没有对应字段,只有st_dir信息 -->

## 解决办法

1. bcreReso的朝向mapping,需要结合DirectionFaces与BCRES_DirectionFacesRearYard同时使用,优先使用DirectionFaces
2. 修改朝向(fce)字段在数据库中的保存格式,由string改为array
3. 房源详情返回fce字段时,后端需要添加翻译并将array转为string
4. unit test修复
5. 添加batch将fce字段的值由string改为array,并且修复bcreReso中没有DirectionFaces但是存在BCRES_DirectionFacesRearYard的数据

## 影响范围

1. 会影响房源import时对fce字段的mapping
2. 会修改fce字段的格式,需要测试朝向的检索以及前端显示

## 是否需要补充UT

1. 需要补充UT,已经补充在propAddress中
2. 需要修复房源import时fce字段的格式

## 确认日期:    2025-03-26

## online-step

1. 重启watch&import
2. 执行batch(由于fce字段在es中没有特别设定格式,可以不用重新init es,通过watch就可以更新)
<!-- NOTE: 对array大量操作,可能会导致mongo数据库opLog,过载失败 -->
```shell
./start.sh -n fix_fce -cmd "lib/batchBase.coffee batch/prop/fix_fce.coffee dryrun"

测试服结果:
Completed, Total process time 23.159883333333333 mins, processed(14460990):624.3K/m, noFce(7169885):309.5K/m, updated(7291054):314.8K/m, notExpectedFce(51):2.202/m
其中 notExpectedFce是因为历史rni数据为数字,和Rain沟通可以不用处理
```
