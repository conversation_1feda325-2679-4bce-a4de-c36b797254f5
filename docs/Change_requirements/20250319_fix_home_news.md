# 需求 [fix_home_news]

## 反馈

Uncaught InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': 'bay的衰落")'' is not a valid attribute name.
    at vue3.min.js:7:77591
    at patchProp (vue3.min.js:7:77615)
    at w (vue3.min.js:7:41082)
    at x (vue3.min.js:7:40724)
    at m (vue3.min.js:7:40296)
    at N (vue3.min.js:7:41660)
    at w (vue3.min.js:7:40965)
    at x (vue3.min.js:7:40724)
    at m (vue3.min.js:7:40296)

## 需求提出人:    Rain

## 修改人：      liurui

## 提出日期:     2025-03-19

## 解决办法

1. 首页展示的新闻标题使用decodeURIComponent预处理,符号转成charCodeAt格式

## 确认日期:    2025-03-19
