# 需求 [crea_mapping]

## 反馈

1. 需要添加DDF房源新的mapping
2. 修改es只使用thumbUrl，不再传入整个phoUrls，具体的listing使用mongo查询。
3. rni中crea的phoUrls是压缩后的二进制。同时考虑先上crea的过渡方案，后面所有房源都添加thumbUrl。

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-01-27

## 解决办法

1. 添加impMappingCreaReso文件来做新ddf房源的import
2. 在properties.coffee中添加getThumbUrlFromPhoUrls函数，如果是线上图片，thumbUrl使用链接，如果是本地图片，thumbUrl格式如下：{ml_base}/cda/828.1726189044/179/27381179_1.jpg。ES中调用这个function，读取结果到es prop.thumbUrl。
3. 在列表中，检索使用的小图中使用es的thumbUrl，并将thumbUrl中的{ml_base}替换为配置中设置的。详情页中使用mongo的phoUrls。

   

## 确认日期:    2025-02

## online-step

1. init es
   ```
   ./start.sh -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init"
   ```
2. 重启server
3. 重启watch&import， 参数一样，替换 -t crea -> -t creaReso