# 需求 [rni_compress_phoUrls]

## 反馈

1. 新的reso数据phoUrls太大，需要压缩

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-02-14

## 原因

1. 新的reso数据phoUrls有所有图片，不同尺寸的的url，太大。

## 解决办法

1. 处理所有新reso数据：trebReso, car, edm, bcreReso。在rni merged中直接将media压缩为phoUrls，压缩前格式[{url:url,m:description},{url:url,m:description}...]，压缩使用zlib，保存用mongo的Binary格式。
2. 修改对应的download，保存thumbUrl，phoUrls：
   trebReso【保存未压缩media + 压缩phoUrls】
   目前media产生有2个地方：
   1. queue，直接获取一整个后可以做压缩
   2. 单个media更新，同时记录listing id记录到db [如果保存为array，突然中断会丢失，后面也不会再更新]整批压缩
压缩phoUrls只保存前端需要使用的trebReso【trebReso将status 'Active',permission Public,sizeDesc 'Medium'的图片urls压缩为phoUrls】

3. 修改对应的mapping
4. batch处理现在已有的数据

## 确认日期:    2025-02-18

## 测试
 All collections completed. Total process time 39.37615 mins.  bcreResoProcessed(208960):5.306K/m, bcreResoDryrun(208960):5.306K/m, carProcessed(345460):8.773K/m, carDryrun(345460):8.773K/m, raeProcessed(75878):1.927K/m, raeDryrun(75878):1.927K/m, trebResoProcessed(1165169):29.59K/m, trebResoDryrun(1165158):29.59K/m, trebResoNoMedia(11):0.279/m



## online-step

1. 重启watchAndImport
2. 重启trebResoDownload.coffee
3. 重启carDownload.coffee
4. 重启edmDownload.coffee
5. 重启bcreResoDownload.coffee
6. 运行batch/import/resoRniCompressUrl.coffee
```
./start.sh -t batch -n resoRniCompressUrl -cmd "lib/batchBase.coffee batch/import/resoRniCompressUrl.coffee dryrun"
```
7. 重新init ES
