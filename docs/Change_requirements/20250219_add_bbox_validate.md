# 需求 [add_bbox_validate]

## 反馈

1. ERROR:cmate3.coffee,model/010_user.coffee,2025-02-17T07:17:15.433
    model/010_user.coffee gFindError 565 Error: User Not Found
       at gFindError (/opt/rmappweb/src/model/010_user.coffee:562:12)
       at /opt/rmappweb/src/model/010_user.coffee:3794:18
       at Database_method_setup.Database.<computed> [as findOne] (/opt/rmappweb/src/lib/mongo4.coffee:1096:22)
       at processTicksAndRejections (node:internal/process/task_queues:95:5)
    revokeWechat no user user _id: undefined user eml: undefined other info:  undefined
2. ERROR:cmate3.coffee,libapp/propElasticSearch.coffee,2025-02-17T10:42:40.289
    libapp/propElasticSearch.coffee exports.search 2763 searchIndexByBody {
     label: 1,
     page: 0,
     bbox: [
       172.90088660971094,
       -33.71873360481923,
       -35.927247758207486,
       117.00908356924015
     ],
     src: 'mls',
     saletp: 'lease',
     saleDesc: 'Rent',
     ptype: 'Residential',
     no_mfee: false,
     sort: 'auto-ts',
     oh: false,
     soldOnly: true,
     ts: 1739806959762,
     hasWechat: true,
     rmProp: true,
     locale: 'zh-cn',
     notApp: false,
     apisrc: undefined,
     hasloc: undefined,
     searchAfter: undefined,
     isCip: true,
     isPad: false,
     isShare: undefined,
     isPropAdmin: false,
     isVipRealtor: false,
     isVip: false,
     isAssignAdmin: false,
     isAdmin: false,
     isVipUser: false,
     isUserAdmin: false,
     isRealtor: false,
     isRealGroup: false,
     status: 'A'
   }

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2025-02-18

## 原因

1. 反馈1是因为用户取消微信授权，在user表没有找到对应的用户，可能的原因为 先在app中删除用户，然后在微信中取消授权
2. 反馈2是因为传入的bbox数据存在错误，es查询时没有验证bbox中的经纬度导致

## 解决办法

1. error log改为warning log
2. es添加验证bbox中的经纬度是否正确

## 确认日期:    2025-02-19

## online-step

1. 重启server
