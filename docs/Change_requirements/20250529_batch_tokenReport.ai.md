<!-- Generated by SpecStory -->

# 函数需求变更：日期范围计算

_**User**_

需求变更：getDateRange函数修改为计算给出日期或者当前日期的上一周周一00:00:00到周天23:59:59

---

_**Assistant**_



---

我需要修改 `getDateRange` 函数来计算给定日期的上一周周一到周日的时间范围。让我来实现这个变更：

---

```diff
    # 检查argv.date是否可以转换为时间格式
    if dateStr
-     end = new Date(dateStr)
-     if isNaN(end.getTime())
+     referenceDate = new Date(dateStr)
+     if isNaN(referenceDate.getTime())
        return [null, null]
-     # 设置为当天的最后一刻
-     end.setHours(23, 59, 59, 999)
-   else
-     end = new Date()
-     end.setHours(23, 59, 59, 999)
-   # 计算开始时间（向前推6天，这样加上当天正好是7天）
-   start = new Date(end)
-   start.setDate(start.getDate() - 6)
+   else
+     referenceDate = new Date()
+   
+   # 计算上一周的周一和周日
+   dayOfWeek = referenceDate.getDay() # 0是周日，1是周一...6是周六
+   
+   # 计算到上一周周一的天数
+   # 如果是周日(0)，上一周周一是7天前
+   # 如果是周一(1)，上一周周一是7天前  
+   # 如果是周二(2)，上一周周一是8天前
+   # ...以此类推
+   daysToLastMonday = if dayOfWeek is 0 then 7 else dayOfWeek + 6
+   
+   # 计算上一周周一 00:00:00
+   start = new Date(referenceDate)
+   start.setDate(referenceDate.getDate() - daysToLastMonday)
    start.setHours(0, 0, 0, 0)
+   
+   # 计算上一周周日 23:59:59
+   end = new Date(start)
+   end.setDate(start.getDate() + 6)
+   end.setHours(23, 59, 59, 999)
+   
    return [start, end]
  
```

---

完成！我已经成功修改了 `getDateRange` 函数。主要变更如下：

## 主要变更：

1. **时间计算逻辑完全重写**：从原来的"向前推6天"改为"计算上一周周一到周日"

2. **新的计算方式**：
   - 使用 `referenceDate` 作为参考日期（如果提供了dateStr就用它，否则用当前时间）
   - 通过 `getDay()` 获取星期几（0=周日，1=周一...6=周六）
   - 计算到上一周周一的天数：
     - 如果是周日：上一周周一是7天前
     - 其他情况：上一周周一是 `dayOfWeek + 6` 天前
   - 上一周周日就是上一周周一加6天

3. **时间设置**：
   - 开始时间（上一周周一）：00:00:00.000
   - 结束时间（上一周周日）：23:59:59.999

这样修改后，无论输入什么日期，函数都会返回该日期所在周的上一周的完整时间范围（周一00:00:00到周日23:59:59）。

---

_**User**_

1. 修改collectTokenStats函数功能，find查找出来后，笔记直接统计计算，claim先不纳入总token数以及分类token数的计算，需要先将每个房源id统计整合，将claim信息按照用户uid分类保存。
2. 另写一个函数去处理claim情况：首先需要将开始和结束日期使用date2num函数(已存在直接使用)转换成数字格式,例如20240305。查询数据库properties表，_id在之前整合的房源id中，并且sldd在转换后的开始日期和结束日期之间，数据库sldd格式是NumberInt(20240305)。获取_id之后，之前保存的claim信息只有token.m在数据库筛选回来之后的_id内，才是有效数据，进行calim token总分统计以及calim 分类 token积分；

---

_**Assistant**_



---

我理解你的需求，需要将claim处理逻辑分离出来，并且只统计在指定时间范围内卖出的房源。让我进行相应的修改：

---

```diff
      return false
  
- # 输出文件名
- gOutputPath = null
- gOutputFileName = null
- stats = {}
- uidsSet = {}
- propSet = {}
- showingOrign = {}
  # 统计token表（认领积分、Note积分），将数据按照uid和key存储到stats中
  collectTokenStats = (cb) ->
```

```diff
      praiseNote: 'PraiseNote'
    }
+   
+   # 用于存储claim信息，等待后续处理
+   claimData = {}
+   allClaimedProperties = {}
+   
    try
      tokenCur = await TokenCol.find({ ts: { $gte: startDate, $lte: endDate } })
```

```diff
        processTs = (new Date().getTime() - processStartTs.getTime())/(1000*60)
        debug.info "Token total process time #{processTs} mins #{speedMeter.toString()}"
+       # 将claimData和allClaimedProperties存储到全局变量供后续使用
+       global.claimData = claimData
+       global.allClaimedProperties = allClaimedProperties
        return cb()
      process: (token, callBack) ->
```

```diff
        if (not uid) or (not keyMap[k])
          return callBack()
+       
+       # 处理笔记相关的token，直接统计
+       if k in ['createNote', 'praiseNote']
          stats[uid] ?= {}
          stats[uid][keyMap[k]] ?= 0
          stats[uid][keyMap[k]] += (token.t or 0) / 100
-       # 统计认领房源个数
-       if k.startsWith('claim')
-         stats[uid].claimedProperties ?= {}
-         stats[uid].claimedProperties[token.m] = 1
+         uidsSet[uid] = 1
+       
+       # 处理claim相关的token，先保存信息，不直接统计
+       else if k.startsWith('claim')
+         claimData[uid] ?= []
+         claimData[uid].push({
+           key: k,
+           mappedKey: keyMap[k],
+           token: (token.t or 0) / 100,
+           propertyId: token.m
+         })
+         allClaimedProperties[token.m] = 1
          uidsSet[uid] = 1
+       
        speedMeter.check {updated:1}
        return callBack()
```

---

现在我需要添加全局变量声明，并创建处理claim的新函数：

---

```diff
  startDate = null
  endDate = null
+ 
+ # 输出文件名
+ gOutputPath = null
+ gOutputFileName = null
+ stats = {}
+ uidsSet = {}
+ propSet = {}
+ showingOrign = {}
  
  # 计算时间范围
```

---

现在我需要添加处理claim的新函数：

---

```diff
    helpers.streaming obj
  
+ # 处理claim token统计，根据房源实际卖出时间筛选
+ processClaimTokens = (cb) ->
+   if not global.claimData or not global.allClaimedProperties
+     return cb()
+   
+   # 将开始和结束日期转换为数字格式
+   startDateNum = date2num(startDate)
+   endDateNum = date2num(endDate)
+   
+   # 获取所有claim的房源ID
+   allClaimedPropIds = Object.keys(global.allClaimedProperties)
+   
+   try
+     # 查询properties表，筛选在时间范围内卖出的房源
+     soldProperties = await PropertiesCol.findToArray(
+       {
+         _id: { $in: allClaimedPropIds },
+         sldd: { $gte: startDateNum, $lte: endDateNum }
+       },
+       { projection: { _id: 1 } }
+     )
+   catch err
+     debug.error err
+     return cb(err)
+   
+   # 创建有效房源ID的集合
+   validPropertyIds = {}
+   for prop in soldProperties
+     validPropertyIds[prop._id] = true
+   
+   # 处理每个用户的claim数据
+   for uid, claimList of global.claimData
+     stats[uid] ?= {}
+     stats[uid].claimedProperties ?= {}
+     
+     for claimInfo in claimList
+       # 只有在有效房源ID内的claim才进行统计
+       if validPropertyIds[claimInfo.propertyId]
+         stats[uid][claimInfo.mappedKey] ?= 0
+         stats[uid][claimInfo.mappedKey] += claimInfo.token
+         stats[uid].claimedProperties[claimInfo.propertyId] = 1
+   
+   debug.info "Processed claim tokens for #{Object.keys(global.claimData).length} users"
+   return cb()
+ 
  # 统计showing表
  collectShowingStats = (cb) ->
```

```diff
          return EXIT 0,err
        try
+         await processClaimTokens()
          writeXLSX()
        catch err
```

---

现在我需要修改主流程，在正确的位置调用新的processClaimTokens函数：

---

Read file: appweb/src/batch/form/weeklyTokenReport.coffee

---

我发现主流程中已经有了`await processClaimTokens()`的调用，但是我需要修改它，因为我的函数是回调风格的，不是async/await风格的。让我修正这个问题：

---

```diff
    collectTokenStats (err) ->
      return EXIT 0,err if err
+     processClaimTokens (err) ->
+       return EXIT 0,err if err
        collectShowingStats (err) ->
          return EXIT 0,err if err
```

```diff
            return EXIT 0,err
          try
-         await processClaimTokens()
            writeXLSX()
          catch err
```

---

完成！我已经成功修改了代码，主要变更包括：

## 主要变更：

### 1. **修改了 `collectTokenStats` 函数**：
- **笔记相关token**（`createNote`, `praiseNote`）：直接统计到 `stats` 中
- **Claim相关token**：不直接统计，而是保存到 `claimData` 和 `allClaimedProperties` 全局变量中，等待后续处理

### 2. **新增了 `processClaimTokens` 函数**：
- 使用 `date2num` 函数将开始和结束日期转换为数字格式（如20240305）
- 查询 `properties` 表，条件是：
  - `_id` 在之前收集的房源ID中
  - `sldd`（卖出日期）在转换后的开始和结束日期之间
- 只有 `token.m`（房源ID）在查询结果中的claim信息才会被统计到token总分和分类token积分中

### 3. **修改了主流程**：
- 调整了函数调用顺序：`collectTokenStats` → `processClaimTokens` → `collectShowingStats` → 其他处理
- 确保claim token的统计基于房源的实际卖出时间，而不是claim的时间

### 4. **关键逻辑**：
- 现在系统会根据房源的 `sldd` 字段（实际卖出日期）来判断该房源的claim是否应该计入统计
- 只有在指定时间范围内实际卖出的房源，其对应的claim token才会被计算进总积分

这样修改后，报告将更准确地反映基于房源实际交易时间的token统计，而不是基于经纪人claim的时间。

---

