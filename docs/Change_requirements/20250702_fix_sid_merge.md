# 需求 [fix_sid_merge] - 房源合并逻辑修改

## 反馈

1. Fred反馈房源BRER3020987与房源DDF28533774没有merge

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON><PERSON>owei

## 提出日期:      2025-06-30

## 原因

1. DDF房源UnparsedAddress字段mapping问题,导致uaddr不一致而没有merge。

## 解决办法

1. DDF房源`UnparsedAddress`字段mapping为`origAddr`。
2. 对相同`sid`的字段,merge时由`uaddr`字段的筛选改为`prov`字段
3. 执行batch修复历史数据

## 房源合并逻辑分析

### 核心函数分析

#### 1. `findDuplicatePropAndDeterminePriority` 函数
**功能**: 查找重复房源并确定合并优先级

**查找策略**:
1. **特殊处理DDF房源**: 当DDF房源省份为Ontario时，先根据sid查找对应的TREB房源
2. **分层查询策略**: 使用`findOldPropForMerge`函数进行分层查询
3. **RHB房源特殊处理**: RHB房源优先merge到TREB房源，未找到时查询其他房源
4. **扩大查询范围**: 最后尝试扩大时间范围查询

**优先级判断**:
- 使用`isImportPropHigherPriority`函数比较房源优先级
- 基于房源来源、省份、城市、图片等因素计算分数
- 分数相同时不进行合并，分数高的房源保留，分数低的被合并

#### 2. `findOldPropForMerge` 函数 (核心查询函数)
**功能**: 查找用于合并的旧房源记录，采用三层分次查询策略

**查询策略优化**:
- **优化前**: 使用复杂的$or查询，包含多个分支条件，查询性能较低
- **优化后**: 改为分次查询策略，按优先级顺序执行三个独立查询，提升性能和可维护性

**三层查询策略**:
1. **策略1 - sid + uaddr匹配** (最高优先级)
   - 查找相同sid和地址的房源
   - 适用于同一房源的更新场景
   - 只包含核心字段：sid、uaddr、onD
   - 时间范围：±15天 (onDMergeDateSpanForSameSid)

2. **策略2 - sid + prov匹配** (中等优先级)
   - 查找相同sid和省份的房源
   - 处理地址变更但sid相同的情况
   - 包含完整业务条件：lp/lpr、unt、src、tot_area等
   - 时间范围：±15天 (onDMergeDateSpanForSameSid)

3. **策略3 - status + uaddr匹配** (最低优先级)
   - 查找相同状态和地址的房源
   - 处理sid不同但实际是同一房源的情况
   - 包含完整业务条件：lp/lpr、unt、src、tot_area等
   - 时间范围：根据是否有图片动态调整 (1天或7天)

#### 3. `buildQuery` 函数 (查询条件构建)
**功能**: 为`findOldPropForMerge`函数构建三种查询策略的查询条件

**查询条件结构**:

**基础查询条件** (所有策略共用):
```coffeescript
baseConditions = {
  _id: { $ne: obj._id }      # 排除当前记录
  merged: null               # 未合并记录
  del: null                  # 未删除记录
  ptype: obj.ptype          # 相同属性类型
}
```

**三种查询策略的具体条件**:

1. **策略1查询条件** (sidUaddrQuery):
   ```coffeescript
   {
     ...baseConditions,
     sid: obj.sid,              # 支持origSid的$in查询
     uaddr: obj.uaddr,
     onD: { $gte: halfMonthFrom, $lte: halfMonthTo }  # ±15天
   }
   ```

2. **策略2查询条件** (sidProvQuery):
   ```coffeescript
   {
     ...baseConditions,
     sid: obj.sid,              # 支持origSid的$in查询
     prov: obj.prov,
     onD: { $gte: halfMonthFrom, $lte: halfMonthTo }, # ±15天
     lp: obj.lp || null,        # 价格条件
     lpr: obj.lpr || null,      # 租金条件
     unt: obj.unt || null,      # 单元号条件
     src: { $ne: obj.src },     # 排除相同数据源 (TRB/BRE)
     tot_area: obj.tot_area     # 总面积条件
   }
   ```

3. **策略3查询条件** (uaddrStatusQuery):
   ```coffeescript
   {
     ...baseConditions,
     uaddr: obj.uaddr,
     status: obj.status,        # RHB+ON时会被删除
     onD: { $gte: timeFrom, $lte: timeTo }, # 根据是否有图片调整时间范围
     lp: obj.lp || null,        # 价格条件
     lpr: obj.lpr || null,      # 租金条件
     unt: obj.unt || null,      # 单元号条件
     src: { $ne: obj.src },     # 排除相同数据源 (TRB/BRE)
     tot_area: obj.tot_area     # 总面积条件
   }
   ```

**特殊业务规则处理**:
- **RHB+Ontario**: 删除status条件以提高匹配率
- **RHB未找到**: 强制添加status条件进行精确匹配
- **TRB无图片**: 删除src限制条件
- **origSid支持**: sid字段支持$in查询包含origSid

**关键优化点**:
1. **策略1简化**: 只包含核心匹配字段，提高匹配成功率
2. **策略2新增**: 解决uaddr不一致但sid相同的合并问题
3. **分次查询**: 避免复杂$or查询，提升查询性能
4. **Early Return**: 找到匹配即返回，减少不必要的查询

#### 4. `mergeByOrigSid` 函数
**功能**: 基于origSid进行RESO房源合并

**适用场景**:
- RESO房源(resoSrc为'itso'或'oreb')
- 具有origSid字段且不是draft类型

**合并逻辑**:
1. **CAR房源** (resoSrc='itso'): 查找对应的CAR房源进行合并，优先级较低
2. **OTW房源** (resoSrc='oreb'): 查找对应的OTW房源进行合并，优先级较高

### 性能优化分析

#### 优化前后对比

**优化前**:
- 使用复杂的$or查询，包含多个分支条件
- MongoDB需要评估所有分支，查询计划复杂
- 索引利用效率较低，查询性能受影响

**优化后**:
- 改为三个独立的简单查询，按优先级顺序执行
- 每个查询条件明确，索引利用率高
- Early return机制减少不必要的查询
- 查询性能显著提升

#### 建议的索引优化

为支持新的查询策略，建议确保以下复合索引存在：
1. `{sid: 1, uaddr: 1, onD: 1}` - 支持策略1查询
2. `{sid: 1, prov: 1, onD: 1}` - 支持策略2查询
3. `{uaddr: 1, status: 1, onD: 1}` - 支持策略3查询

## 是否需要补充UT

1. 需要补充UT，重点测试：
   - `findOldPropForMerge`函数的三层查询策略
   - `buildQuery`函数的查询条件构建逻辑
   - sid+prov匹配逻辑的正确性
   - 时间范围计算的准确性
   - 特殊房源类型(RHB、TRB、DDF)的处理逻辑

## 影响范围

1. **查找merge房源的逻辑变更**:
   - 从复杂$or查询改为分次查询策略，提升性能
   - sid+uaddr策略简化，只考虑核心字段，提高匹配成功率
   - 新增sid+prov策略，解决uaddr不一致但sid相同的合并问题
   - 保持原有的status+uaddr策略不变，确保兼容性

2. **房源合并准确性提升**:
   - 解决相同sid房源因uaddr差异导致的合并失败
   - 提高查询性能，减少数据库负载
   - 增强代码可维护性和可调试性
   - 保持其他合并逻辑的稳定性

3. **代码质量改进**:
   - 添加完整的参数验证和错误处理
   - 增加详细的调试日志，便于问题排查
   - 函数注释完善，提高代码可读性

## 确认日期:    2025-07-01

## online-step

1. 重启watch&import
2. 执行batch->src/batch/prop/fix_propMerged.coffee
```shell
./start.sh -t batch -n fix_propMerged -cmd "lib/batchBase.coffee batch/prop/fix_propMerged.coffee dryrun -d 2025-01-01"

测试服执行结果:
Total process time 31.292516666666668 mins, processed(571316):18.25K/m, noNeedMerge(544023):17.38K/m, needMerge(27293):872.1/m
```
