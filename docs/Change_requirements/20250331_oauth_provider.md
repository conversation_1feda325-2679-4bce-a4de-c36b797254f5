# 需求 [oauth_provider]

## 反馈

1.  client网站能够使用realmaster oauth验证登录

## 需求提出人:   Fred

## 修改人：      Mingming

## 提出日期:     2025-02-21

## 原因

## 方案

见 docs/Dev_oauth_provider/readme.md; docs/Dev_oauth_provider/process.drawio

## 确认日期:    2025-02-21

## online-step

1. rmconfig merge oauth2_config PR https://github.com/real-rm/rmconfig/pull/10
2. rmconfig local.ini 添加 oauthProvider

```
[oauthProvider]
secret = "provider_secret_key"

  [[oauthProvider.clients]]
  clientId = "report_rentals"
  clientSecret = "client_secret_key"
  redirectUri = "http://CLIENT_APP.com/v1/auth/oauth/callback"
```

其中: oauthProvider.clients 的配置需要与 client 网站的配置一致, 例如: 

```
# 这是在 client 网站的配置, 不属于 rmconfig
[oauthClient] 
clientId = "report_rentals"
clientSecret = "client_secret_key"
redirectUrl = "http://CLIENT_APP.com/v1/auth/oauth/callback"
```

3. restart appweb