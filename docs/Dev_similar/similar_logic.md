# 相似房源，同楼房源查询与过滤说明文档

## 相似房源

### 查询逻辑
<!--
房源ptype2仅支持以下类型['Detached','Semi-Detached','Townhouse','Apartment','Bungalow','Link']
其中如果只有Link类型,将以Detached作为筛选,见:docs/Change_requirements/20240424_fix_similar_link_prop.md
-->
1. 获取传入房源的propId,经纬度lat,lng,需要过滤的房源类型type,限制房源在几个月内month,是否查询成交房源isSold,是否查询出租房源isRental
2. 根据type获取查找距离范围限制rang,生成query,查找房源列表(目前按sldd,mt升序仅查找前100条房源)
<!--
query={
  _id:{$ne:propId}
  ptype:'r',
  ptype2:type,
  ts:{$gt: now.setMonth(now.getMonth() - month)}
  saletp: if isRental then 'Lease' else 'Sale'
  status: if isSold then 'U' else 'A'
  lst: if isRental then 'Lsd' else 'Sld' # 当isSold === true时限制lst
  loc:{$near:{
    $geometry:{type: 'Point',coordinates:[lng,lat]}
    $maxDistance:range
  }},
  geoq:{$gte:100}
}
-->
### 添加权重与房源过滤

1. 根据type计算出允许的最大差异权重maxWeight
2. 循环查出的房源列表props分别计算各项差异权重与总差异权重,需要计算的差异权重包括:gr(车位),sld(是否成交),bdrms,ts(根据mt计算),range(距离),sqft(房屋面积),size(占地面积),bthrms,price,ptype2(仅针对Link)
3. 按照总差异值升序排序,取前三个计算平均差异值averageWeight
4. 过滤房源列表中总差异值大于2倍averageWeight的房源
5. 计算剩余房源的平均差异值weightAvg
   1. 如果 3*weightAvg > maxWeight,认为当前房源列表均不是相似房源。
   2. 如果 3*weightAvg <= maxWeight,将当前房源列表作为相似房源返回

## 同楼房源

1. 获取房源地址uaddr,经纬度lat,lng,获取其他过滤条件ptype2,saletp,status,lst,bdrms,根据房源类型获取距离范围range
2. 根据以上信息生成query,返回查询出的房源列表
<!--
query = {
  _id:{$ne:propId}
  ptype:'r',
  ptype2,
  saletp,
  status,
  lst,
  bdrms,
  lst,
  bdrms,
  $or:[
    {uaddr},
    {
      geoq: {$gte:100},
      loc: {
        $geoWithin:{$centerSphere:[[params.lng,params.lat],range]}
      }
    }
  ]
}
-->
