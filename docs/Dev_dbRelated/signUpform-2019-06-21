数据结构：
sample
{
    "_id" : ObjectId("5d3f26b646d176de612b41aa"),
    "formid" : "system", 表格id
    "ip" : "127.0.0.1", 填表人ip
    "ts" : ISODate("2019-07-29T17:02:46.126Z"),
    "srcid" : "594944b3c341401a564b6251", 填表时所在页面proptery的id
    "tp" : "Project", 填表时页面类型
    "formnm" : "system", 表格名称
    "fromUid" : ObjectId("571a7a88287d3da034728d05"), 填表人uid
    "fromEml" : "<EMAIL>", 填表人app中的eml
    "fromNm" : "julie wang", 填表人姓名
    "fromPn" : "ios:51b0efc8dc252ca9f624f8c70c8f064",填表人pn
    "nm" : "julie wang", 填表人写在表格里的名称
    "eml" : "<EMAIL>",填表人写在表格里的eml
    "m" : "12313",填表人写在表格里的内容
    "url" : "http://app.test:8080/html/projDetailPage.html?id=594944b3c341401a564b6251&lang=zh-cn&wDl=1",
    "uid" : ObjectId("571a7a88287d3da034728d05"), 填表人uid
    "city" : "Toronto", project的城市信息
    "prov" : "ON", project的城市
    "addr" : "23sfdsf Spadina Avenue",
    "projnm" : "name Chinese:",
    "projnm_en" : "name English:",
    "img" : "http://f.i.realmaster.com/G/ACQ/MA.jpg",
    "mbl" : "6477046911",
    "pjid" : "594944b3c341401a564b6251",
    "id" : "594944b3c341401a564b6251",
    gids： 发送给的组的id
    "uids" : [ 发送给的用户的uid
        ObjectId("571a7a88287d3da034728d05")
    ],
    "tousers" : [ 发送给的用户的eml。
        {
            "uid" : ObjectId("571a7a88287d3da034728d05"),
            "eml" : "<EMAIL>"
        }
    ]
}


1. add system group :rmsalse, :rmprop, :rmproject in rm_group
2. add system form system in form
3. select sponsor and inquery user and group in project edit page.
3. all sign up form submit to 1.5/form/forminput

4. test:
'project': app里，分享后，web列表，web detail
'wecard': 分享后，发给微图文的作者
'forum': inapp，分享后，发给论坛文章的作者。
'prop': 分享后，发给分享的经纪，在app呢，如果该城市没有推荐经纪，发给系统rmprop，
'evaluationReport': rmsales
'renovation': rmsales
'predictReport': rmsales
'toplisting': rmsales

project
detail页面的表格顺序：
sponsor,sponsor Group -> inquery,inquery group -> rmproject,
楼盘unit的咨询的直接发给楼盘经纪
web列表页面的发给rmproject


input form 测试

新盘：
新盘编辑，选择sponsor agent 和sendto agent。 可以选择group
sponsor agent - sendto agent - default project group （<EMAIL>）

App内新盘detail， 发送给正确的用户
app floor plan， 发给floor plan owner
share， 发给share agent
web list 发给project group
web detail 发给project group

确保链接都能打开。

房源：
share，发给share agent
没有广告经纪的城市，发给salesgroup
web detail，发给salesgroup

微图文：
发给owner

论坛：
发给owner

renovation，
估价报错，
Ai估价报错，
