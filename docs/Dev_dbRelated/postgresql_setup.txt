安装postgresql12:

sudo yum -y install https://download.postgresql.org/pub/repos/yum/reporpms/EL-7-x86_64/pgdg-redhat-repo-latest.noarch.rpm

sudo yum -y install epel-release yum-utils
sudo yum-config-manager --enable pgdg12
sudo yum install postgresql12-server postgresql12
sudo /usr/pgsql-12/bin/postgresql-12-setup initdb

sudo systemctl enable --now postgresql-12

修改端口，设置监听ip：
sudo vi /var/lib/pgsql/12/data/postgresql.conf


listen_addresses = '*' # or server ip
port = 5412

======no need =====
shared_buffers = 20000M #default 128M
synchronous_commit= off #default on comment out
commit_delay = 100000 #default 0  comment out
max_wal_size = 1GB
min_wal_size = 320MB #default 80MB
checkpoint_completion_target = 0.9  #default 0.5 comment out    # checkpoint target duration, 0.0 - 1.0
checkpoint_flush_after = 2MB   #default 256kB  comment out          # measured in pages, 0 disables
======no need =====

设置接收远程连接
sudo vi /var/lib/pgsql/12/data/pg_hba.conf
# Accept from anywhere
host all all 0.0.0.0/0 md5

# Accept from trusted subnet
host all all ************/24 md5

# change autherization method
# IPv4 local connections:
#host    all             all             127.0.0.1/32            ident
host    all             all             127.0.0.1/32            md5

sudo systemctl restart postgresql-12

安装postgis
sudo yum install postgis30_12

创建数据库，安装extension：
sudo -u postgres psql -p 5412
CREATE DATABASE listing;
\c listing
CREATE EXTENSION postgis;
CREATE EXTENSION btree_gist;

创建用户：
CREATE USER listing WITH PASSWORD 'listing'; # ezqx5j4xzx5aw77p
GRANT ALL PRIVILEGES ON DATABASE listing TO listing;

设置环境变量：
base.yml 添加
pgsql:
  host: localhost
  user: 'listing'
  max: 20
  idleTimeoutMillis: 30000
  connectionTimeoutMillis: 20000
  database: 'listing'
  password: 'listing'
  port: 5412
  pool: 20

config.coffee
usePostgresql:true
sqldbs:
  listingPostsql:
    verbose: 1
    tp:'pgsql'
    dbNm: # optional db name, or config name is used
    host: '<%this.pgsql.host%>'
    user: '<%this.pgsql.user%>'
    max: '<%this.pgsql.max%>'
    idleTimeoutMillis: '<%this.pgsql.idleTimeoutMillis%>'
    connectionTimeoutMillis: '<%this.pgsql.connectionTimeoutMillis%>'
    database: '<%this.pgsql.database%>'
    password: '<%this.pgsql.password%>'
    port: '<%this.pgsql.port%>'
    pool: '<%this.pgsql.pool%>'

add index
db.getCollection('properties').createIndex({'rmmt':1})

npm install pg

move data:
./start.sh batch/movePropRecordsPostSql.coffee init

./start.sh batch/movePropRecordsPostSql.coffee routine

))*
http://app.test:8080/sys/generalConfig
use Postgresql = 1

#to connect db
psql -p [port] -h 127.0.0.1 [database] [user]