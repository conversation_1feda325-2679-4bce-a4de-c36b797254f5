[X]* Replace update() -> updateOne()/ updateMany()/ replaceOne()
[X]* Replace remove() -> deleteOne() / deleteMany() /findOneAndDelete()
[X]* Replace count() -> countDocuments(), 
  unless you want to count how many documents are in the whole collection (no filter). 
  In the latter case, use estimatedDocumentCount().
[X]* Replace insert() -> insertOne() / insertMany() /bulckWrite()
[not done]* Replace save() -> updateOne/insertOne()
