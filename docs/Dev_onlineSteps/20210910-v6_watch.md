BranchName: [v6_watch]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Description: change watch to use batchBase


# Steps:
1. kill watch
2. restart watch use batchBase
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init > ./logs/watchMongo.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee init > ./logs/watchSql.log 2>&1 &


# Check this after onlined(@admin)

Online Date:[] ca8
- [X] restart watch
