BranchName: [v6_kr]
Author: [louis]
BatchName: [batch/i18n/i18n.coffee]
BatchDetail: add kr translation to i18n
DB tobe modified: [i18n]
Sales Doc: [N/A]

# steps:

- 確認 i18n 裡有沒有 id:L10000 字段
- 確認 collection 與 20210408-kr-ref-zh 中英文&id 是否對應
- `./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m import -t kr -f [absolute path to/20210408-kr-zh-translations.txt]/20210408-kr-zh-translations.txt > logs/kr.log 2>&1 &`
  *Error Handling: notify @louis to fix/record error and notify*
- 確認 i18n 裡有有 kr 字段,確認 collection 與 20210408-kr-ref-zh 中英文&id&kr 是否對應


# Check this after onlined(@admin)
Online Date: 20210728 13:00
- [X] (batch/i18n/i18n.coffee) runned
