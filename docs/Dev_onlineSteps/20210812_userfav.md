BranchName: [fix_saves]
Author: [liurui]
BatchDetail: 修改用户收藏房源储存位置
DB tobe modified: [properties,props_part,user_listing]
Add new DB: [prop_fav]

./start.sh lib/batchBase.coffee  batch/user/addPropFavToManageUserFavProp.coffee

修改内容：
batch 更改现有数据
更改添加，删除用户收藏房源储存位置
更新获取房源数据时当前用户收藏房源的方式
更新edm部分获取用户收藏房源的方式

等上线正常运行之后，需要 unset properties、props_part、user_listing 表中的 favU 字段
邮件发送fav properties正常，saves properties显示正常


Online Date:[2021-09-(01-3) 13:20] had issues, fixed multiple times 
- [X] ca8 batch runned, no dry run
- [ ] db.getCollection('user_listing').updateMany({},{$unset:{favU:1}})
- [ ] db.getCollection('properties').updateMany({},{$unset:{favU:1}})
- [ ] db.getCollection('props_part').updateMany({},{$unset:{favU:1}})

