BranchName: [userlisting_edm]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]
Description: set edmSent:true to all promoted user listings if marketPromoTs less than yesterday


# Steps:

 db.getCollection('user_listing').updateMany({'market.st':'Promoted',marketPromoTs:{$lte:new Date(Date.now() - 1000*3600*24)}},{$set:{edmSent:true}})

# Check this after onlined(@admin) 2021-08-25 11:26

- [X] db.getCollection('user_listing').count({'market.st':'Promoted',edmSent:{$ne:null}})

