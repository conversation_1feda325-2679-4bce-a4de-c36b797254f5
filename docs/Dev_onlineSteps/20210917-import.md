## BranchName: [import]
## Author: [<PERSON> & <PERSON>]
## DB to Restore: ['vow':cmtyFromProperties, filePath:]
## Sales Doc: [N/A]
## Description: 
  new Download with node
  new photo download with node
  new import with watch
  details Refer to Dev_import

## Config Change:
tpls/base.coffee:
module.exports.treb_image_dir = treb_image_dir = base_dir + 'mls'
module.exports.treb_image2_dirs = treb_image2_dirs = <%this.treb.imagedirs%>

module.exports.crea_base_dir = crea_base_dir = '<%this.crea.basedir%>'
module.exports.crea_dl_dir   = crea_dl_dir = crea_base_dir + 'dnld'
module.exports.crea_image_dir = crea_image_dir = crea_base_dir + 'img'
module.exports.crea_image2_dirs = crea_image2_dirs = <%this.crea.imagedirs%>

tpls/base.php:
$dir_treb_base = "<%this.treb.basedir%>";
$dir_treb_dl = $dir_treb_base . 'dnld';
$dir_treb_image = $dir_treb_base . "mls";
$dir_treb_image2 = <%this.treb.imagedirs%> # ["/mnt/ca6/mlsimgs/treb/mls"];

$dir_crea_ddf_base = "<%this.crea.basedir%>";
$dir_crea_ddf_dl = $dir_crea_ddf_base . 'dnld';
$dir_crea_ddf_image = $dir_crea_ddf_base . 'img';
$dir_crea_ddf_image2 = <%this.crea.imagedirs%> # ["/mnt/ca6/mlsimgs/crea/ddf/img"];

base.yml:
treb:
  basedir: /mnt/raid0/mlsimgs/treb/ #/opt/data/treb/mls/
  imagedirs:
    - '/mnt/ca6/mlsimgs/treb/mls'
crea:
  basedir: /mnt/raid0/mlsimgs/crea/ddf/ #/opt/data/crea/ddf/
  imagedirs:
    - '/mnt/ca6/mlsimgs/crea/ddf/img'
    
setup.coffee:
updateDoc = (d)->
  ret = {}
  for k,v of d
    if 'string' is typeof v
      v = TemplateEngine v,doc
    else if Array.isArray(v)
      v = "['"+v.join("','")+"']"
    else if 'object' is typeof v
      v = updateDoc v
    ret[k] = v
  ret


## steps:
## step 1 : on batch server, IMPORTANT! only on batch server, 数据更新。
1. sync source
   ```
    npm install 
   ```
2. sync cfg source
  ```
    npm install 
    coffee ./setup
  ```
3. restore cmtyFromProperties,prov_city_cmty
  path: filestorage
  
  ``` example:
  mongorestore --port 27018 --db listingP -u d4 -p d4 -c cmtyFromProperties --authenticationDatabase 'admin' --gzip ./cmtyFromProperties.bson.gz
  ```
   
4. properties Clean Step1 (run about 3 days)
  clean properties, to propertiesNew. changes refer to Dev_import/import.md
  只有properties和rm_listing需要处理，其他的已经在provClean branch 处理过了。
  dropIndex: db.properties/props_parts.dropIndex('addr_text/addr_text_mt')
  For test server:
  ```
  nohup ./start.sh lib/batchBase.coffee batch/correctData/provClean.coffee init propertyOnly nogeo noTag > ./logs/provclean.log 2>&1 &
  ```
  run about 3 days,timestep saved in 'vow',provCleanSysData
  spend 261527.597 s = 72hr.

  for Prod
  
  for Those props within two years, if not find in cache, and prop.geoq < 100, do real geocoding
  if prop.geoq = 100, only do real geocoding for sold an active props within half years.
  126200 need to do realcoding
  db.getCollection('properties_import_log').count({'geocoding.doCoding':true,_id:/property/})
  ```
  screen -r
  nohup ./start.sh lib/batchBase.coffee batch/correctData/provClean.coffee init propertyOnly nogeo noTag> ./logs/provclean.log 2>&1 &
  ```

5. Import old collections to new rni db first time, can run same time with 4 
   Drop current rni db in server, give new db path @fred, recreate db.@fred.
   collections under rni should keep.  manual log and donwload log, drop after


   replace the connection string with product rni
   migrateCrea.log  3:47:35.152  (h:mm:ss.mmm)
   migrateTreb.log: import from database: 2:59:27.409 (h:mm:ss.mmm)
  
  ```
   nohup ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c treb > logs/migrateTreb.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c crea > logs/migrateCrea.log 2>&1 &
  ```

6. 通知手动import暂停1天


7. Check Data Clean result. (5min) #todo
   ```
   nohup ./start.sh lib/batchBase.coffee batch/correctData/checkProvCleanResult.coffee properties > ./logs/checkPropertyClean.log 2>&1 &
   ```
  sample result:
  DEBUG:batch/correctData/checkProvCleanResult.coffee,batch/correctData/checkProvCleanResult.coffee,2021-09-24T15:01:34.230
 {
  oldProps: 7922092,
  newProps: 7905113,
  prov: 7908796,
  geoq120: 0,
  needToDoGeocoding: 18083,
  mergedProps: 78457,
  processed: 7922100,
  DropRecords: 16979,
  fixAddrFromSrc: 4375768,
  sldDom: 3839,
  cmty: 10322,
  unt: 864597,
  addr: 3080419,
  geoq: 1770317,
  lp: 1018772,
  lpr: 363,
  saletp: 956,
  invalidSaleTp: 0,
  lpEqualLpr: 59,
  lpNotEqualLpr: 3637,
  lpOutOfRange: 6692,
  noLpAndLpr: 0,
  setOldAsMerge: 78433,
  bndProv: 193927,
  noBndProv: 192259,
  noBndCity: 189539,
  houseHasUnt: 235385,
  fixRegion: 2793218,
  fixCity: 432577
}

print.newProps + print.DropRecords = 7922092 should same with old prop: 7922092, 
number of old props might be a little smaller as there's auto expired rm listing 
start check: 17:52.240 (m:ss.mmm) 

-------------9th 10PM--------------

9.  Run prov clean again.(around 5 hour for 190000 .
     second round is from _mt
  
  10.1 Run prov
  ```
  screen -r
  nohup ./start.sh lib/batchBase.coffee batch/correctData/provClean.coffee propertyOnly nogeo noTag> ./logs/provcleanStep3.log 2>&1 &
  ```

  ```
  10.2 .check result again.  
  ```
  nohup ./start.sh lib/batchBase.coffee batch/correctData/checkProvCleanResult.coffee properties> ./logs/checkProvClean.log 2>&1 &
  ```
-------------HERE--------------

10. stop all batch，cronjob #Need check latest cronjob again to see if missing any
   1. stop watchPropAndUpdateMongo
   2. stop watchPropAndUpdatePostSql in each server
   3. stop treb_download_pic.php & treb_download_idx.php
   4. stop mongoWatchv2 @ca6
   5. check and stop batch from cronjob batch, comment out cronjob or rename.
      1. stop findschoolv3
      2. stop job_hourly.sh, remove from crontab
      3. stop job_4hours.sh,remove from crontab
      4. stop syncTgr.coffee maybe not need any more, need check again
      5. stop sendMail.log
      6. stop prop_mw.coffee
      7. stop prop_daily.coffee
      8. stop dailyNotify.coffee
      9. stop wepageClearCounter.coffee
      10. stop wechat.coffee
      11. stop edmNotify.coffee
      12. stop topListingNotify.coffee
      13. stop edmExpNotify
      14. stop getAgentWithListings
      15. stop getAgentRealChatStat
      16. stop addTagToProperty
      17. stop statuname

11. 
Import old collections to new rni db second time,as import already stopped, so run twice is enough.

   ```
   nohup ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c treb > logs/migrateTreb.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c crea > logs/migrateCrea.log 2>&1 &
   ```

12. run prov clean again,to save stop and restart time 
  nohup ./start.sh lib/batchBase.coffee batch/correctData/provClean.coffee  propertyOnly nogeo noTag> ./logs/provcleanStep4.log 2>&1 &

-----CRITIAL------
13. ***** Run provClean 3rd round and Rename collection , restart server (IMPORTANT!!)
  13.1. update other server web|app|appChina 
    1. sync source tree
    
  13.2. stop app|web|appchina server. server may down for 10 min。 can run prov clean again before stop server.

  13.3. job_import_provclean.sh, will rename and restart server. should run on app server around 10min for 10000.
    ```
    go cfg
    pull master
    coffee ./setup.coffee
    chmod 755 ./built/job_import_provclean.sh

    nohup ./built/job_import_provclean.sh > ./logs/job_import_provclean.log 2>&1 &
    ```


    read all ids into cache spend 5 min in test server.for 2593 records.
    INFO:batch/correctData/provClean.coffee,libapp/provCleanHelper.coffee,2021-10-27T16:37:18.161
    Total 2593 records
    INFO:batch/correctData/provClean.coffee,libapp/provCleanHelper.coffee,2021-10-27T16:37:18.161
    spend 250294 ms to read 

  13.4 restart web appchina server。
  

  #### do error restore if 13 failed:
    1.shutdown server
    2 运行restore脚本，将所有的properties->new. backup->properties
    ```
      ./start.sh lib/batchBase.coffee batch/correctData/provCleanRollback.coffee  propertyOnly 
    ```
    3.checkout v6 branch 
    ```
    npm install
    ```
    4.restart 

14. Start new import routines in batch server
  replace the id and credentials with real ones
  
  14.1. backup tables under old rni -> _backup
   1. mls_bcre_manual_records_log 
   2. mls_crea_ddf_records_log
   3. mls_treb_evow_records_log
   4. mls_treb_idx_records_log
   5. mls_treb_manual_records_log
   6. mls_treb_records_dta_log
   7. mls_treb_vow_records_log
 
  
  14.2 start new import and watch.
  
   ```
   screen -r
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init > ./logs/watchPropMongo.log 2>&1 &

  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta init -h 2 nogeo> ./logs/watchImportToProp.log 2>&1 &

   nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea > logs/creaPhoto.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb > logs/trebPhoto.log 2>&1 & 
   nohup ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee rKfpLGwCCyfcWM1KzTdSQ6Y8 VenzibWOzDXnAm70CCJEpeVW > logs/creaDownload.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee evow EV20ywa 'C8p6\$39' > logs/trebEVOW.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx D20ywa 'H9p3\$63' > logs/trebIDX.log 2>&1 &

   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee vow V14fxd 'Rd\$8439' skipDiff > logs/trebVOW.log 2>&1 &
   ```
  dta import still in cronjob, run hourly
  add this to cronjob:
  ```
  10,40 0-23 * * * ./start.sh lib/batchBase.coffee mlsImport/dtaDownload.coffee >> /cfg/logs/dta.log 2>&1
  ```

15.    Start sql init in different server.
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee init > ./logs/watchPropSql.log 2>&1 & 

16.   Start manual import. Need to check if all good, manual import only save original record to treb_manual or bcre_records. did not test in test server. 9 号服可以测试。

17.  put back cronjob and edit
  1. remove job_hourly.sh
  2. remove job_4hours.sh
  3. remove syncTgr.coffee
  4. remove addTagToProperty 


17.5 re-run tags
```
screen -r
nohup ./start.sh batch/tag/addTagToProperty.coffee init school prop census census2016 transit boundary > /home/<USER>/rmcfg/logs/proptagInit.log 2>&1
```
-----------------TODO:---------------

18.  drop listingP, rniP,dataP in test server, redo sync
19.  drop unused index in properties (which?)
20. fix geocoding  (quota 10000)
```
./start.sh lib/batchBase.coffee batch/correctData/fixGeocodingAfterProvClean.coffee dry
```
prod:
```
./start.sh lib/batchBase.coffee batch/correctData/fixGeocodingAfterProvClean.coffee
```


7天后,运转正常时:
1. 运行删除脚本，删除所有的backup table
./start.sh batch/correctData/provCleanRemoveBackup.coffee

