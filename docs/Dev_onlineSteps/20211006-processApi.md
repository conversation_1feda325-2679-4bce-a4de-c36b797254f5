BranchName: [processApi]
Author: [<PERSON>]
BatchName: [batch/checkServerStatus.sh]
BatchDetail: See inside batch file
DB to Restore: [N/A]
Sales Doc: [N/A]

# steps:

1. Install dependencies

- install [jq](https://stedolan.github.io/jq/download/)

2. Add corn job

```shell
./batch/checkServerStatus.sh -a [app_url] -w [web_url] -e [email,email2,email3]
```

# Check this after onlined(@admin)

Online Date:[YYYYMMDD hh:mm]

- [ ] Install dependencies
- [ ] Add corn job
