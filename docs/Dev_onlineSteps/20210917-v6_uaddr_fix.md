BranchName: [v6_uaddr_fix]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch:
 nohup ./start.sh lib/batchBase.coffee batch/correctData/propertiesUaddrFix.coffee > logs/uaddrFix.log 2>&1 &
Detail: 
  1. fix properties uaddr from CA:ON  back to Canada:Ontario. Was introduced by geocoding

step
restart server before run batch. batch only run once.
run batch
  ./start.sh lib/batchBase.coffee batch/correctData/propertiesUaddrFix.coffee

Online Date:[] ca8/ca1/shf1
- [X] batch