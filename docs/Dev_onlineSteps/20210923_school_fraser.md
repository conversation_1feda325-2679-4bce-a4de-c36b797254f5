BranchName: [school_fraser]
Author: [<PERSON>]
BatchName: [school_fraser/school_fraser.coffee]
BatchDetail: See inside batch file
DB to Restore: [N/A]
Sales Doc: [N/A]

# steps:

- Run formatSchoolAddr.coffee to format school's addr

```
./start.sh lib/batchBase.coffee batch/school/formatSchoolAddr.coffee
```

- Check batch/puppeteer/src/school_fraser/school_fraser.js, there should be only fraser_full_2021.json
- Run school_fraser.coffee

```
./start.sh lib/batchBase.coffee batch/school_fraser/school_fraser.coffee
```

- Ensure batch runned correctly, expected out put should be
  Total matched:6418, Third party matched:1312, Unmatched:319, Incomplated: 0

# Check this after onlined(@admin)

Online Date:[YYYYMMDD hh:mm]

- [X] Run formatSchoolAddr.coffee
- [X] Run school_fraser.coffee
