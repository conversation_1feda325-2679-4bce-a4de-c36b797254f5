
1. sync src source.
   ```
   npm install
   npm list|grep coffee

   ```
2. sync cfg master.
   ```
   npm install

   npm list|grep coffee
   ```
3. if has error, cd coffee4client, check coffee version
   npm uninstall --global coffeescript@2.5.1
   npm install --global coffeescript@2.5.1
   npm i -g coffeescript@2.5.1
4. npm install --global @babel/core
5. kill server and restart
   restart server directly will cause error:
   Error: Cannot find module '/home/<USER>/cfglocal/node_modules/coffee-script/bin/coffee'
