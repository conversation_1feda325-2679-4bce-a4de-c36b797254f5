BranchName: [louis_fix]
Author: [<PERSON>]
BatchName: [findschoolv3.coffee]
BatchDetail: See inside batch file
DB to Restore:[N/A]
Sales Doc: [N/A]

# steps:

- Keep package.json
- (optional): npm i (moved cheerio to dev and updated sharp)
- run batch to update all active school

```
./start.sh lib/batchBase.coffee batch/findschool/findschoolv3.coffee -a >> ./logs/findschool.log 2>&1
```

- check public school list in property detail (app and web)
- check public school list in map
- check public school detail, grade, eqao

- update corn job

```
./start.sh lib/batchBase.coffee batch/findschool/findschoolv3.coffee
```

# Check this after onlined(@admin)

Online Date:[YYYYMMDD hh:mm]

- [X] Run batch
- [ ] Check results
