BranchName: [provClean]
Author: [<PERSON>]
BatchName: correctData/provClean.coffee 
BatchDetail: change prov to abbr version


1. stop batch findschoolv3.coffee, comment out in crontab.
2. 运行provClean，30min
   ```
    nohup ./start.sh lib/batchBase.coffee batch/correctData/provClean.coffee init noProperties > ./logs/provclean.log 2>&1 & 

  ```


3. Check Data Clean result 1min.
   ```
   nohup ./start.sh lib/batchBase.coffee batch/correctData/checkProvCleanResult.coffee > ./logs/checkProvClean.log 2>&1 &
   ```
  sample result:
   count of user 248228 is same as user 248228
   cpm_stat_viewNew and cpm_stat has a lot unsupport cpm, ignore for now.


4. do job_provclean.sh, process remaining, rename and restart.
  
    ```
    go cfg
    pull master
    coffee ./setup.coffee
    chmod +x ./built/job_provclean.sh
    nohup ./built/job_provclean.sh > ./logs/job_provclean.log 2>&1 &
    
    ```
5. 运行findschoolv3
   ./start.sh lib/batchBase.coffee batch/findschoolv3.coffee

#### do error restore if 4 failed:
    1.shutdown server
    2 运行restore脚本，将所有的表恢复。
    ```
      ./start.sh lib/batchBase.coffee batch/correctData/provCleanRollback.coffee  > ./logs/provCleanRollback.log 2>&1&
    ```
    3.checkout v6 branch 
    ```
    npm install
    ```
    4.restart 