# storage cmty_image collection in mongodb

cd fileStorage
mongorestore -d listing -c cmty_image cmty_image.bson

DB Data Structure:
o: original cmty image
b: cmty image with boundary
z: cmty image with larger zoom level
{
    "_id" : "CA:ON:TORONTO:WEST HUMBER-CLAIRVILLE",
    "_mt" : ISODate("2021-06-26T00:55:15.380Z"),
    "b" : {
        "zoom" : 11.1024063616579,
        "bbox" : [ 
            -79.6981211473926, 
            43.6641877644653, 
            -79.4934506830489, 
            43.7624272608156
        ],
        "fn" : "ca_on_toronto_west_humber_clairville_b"
    },
    "mt" : ISODate("2021-06-26T00:55:15.380Z"),
    "o" : {
        "zoom" : 11.1024063616579,
        "bbox" : [ 
            -79.6981211473926, 
            43.6641877644653, 
            -79.4934506830489, 
            43.7624272608156
        ],
        "fn" : "ca_on_toronto_west_humber_clairville"
    },
    "z" : {
        "zoom" : 12.1024063616579,
        "bbox" : [ 
            -79.6981211473926, 
            43.6641877644653, 
            -79.4934506830489, 
            43.7624272608156
        ],
        "fn" : "ca_on_toronto_west_humber_clairville_z"
    }
}

## Update config.coffee, add '<%this.srcPath%>/appBatch' to end of apps
apps: ['<%this.srcPath%>/model','<%this.srcPath%>/apps_common','<%this.srcPath%>/apps','<%this.srcPath%>/appBatch']