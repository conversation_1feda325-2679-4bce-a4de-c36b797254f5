
BranchName: v6_geocoding
Author: <PERSON>: 
   batch/correctDate/provClean.coffee
   batch/correctDate/renameGeoCache
BatchDetail: See inside batch file
   1. Only do provClean geoCacheOnly in this branch
   2. main changes:
      1.  删掉没有lat，lng 的record
      2.  删掉半年前q<100 的record
      3.  检查_id和addr，不匹配的q=q-10,
      4.  addr为数字的q=q-10
      5.  补全空的addr
      6.  faddr 是数字开头的保留，并用faddr解析出地址
      7.  非数字开头的faddr q = q-10
   
DB to Restore: geo_cacheNew
Sales Doc: NA

# steps:
on test server:
1. run batch, around 1.5 hour (could restore a new db or use listingP,drop geo_cacheNew before run batch)
<!-- NOTE:2021-09-07T12:35:59.92  -> (2021-09-07 14:01) 200w record, total 266w/420w(dump some records?), total:2.5hrs-->
- screen -r
- `nohup ./start.sh lib/batchBase.coffee batch/correctData/provClean.coffee init geoCacheOnly nogeo > ./logs/provclean.log &`
- eg result:
-  geo result {
  dropQ: 373873,
  trbOrManual: 156626,
  upsert: 2568049,
  badFaddrLen: 617551,
  correctWithFaddr: 2036068,
  findOldInCache: 1180266,
  decNoStNum: 938070,
  dropNoLatLng: 36110,
  copyAddrFromId: 1132166,
  addrIsNumber: 87
}
	
2 . check result around 10min
- `nohup ./start.sh lib/batchBase.coffee batch/correctData/checkGeocacheResult.coffee > ./logs/checkGeocacheResult.log &` 
  - Ensure batch runned correctly, expected out put should be
  eg result: should not significant different
   {
      oldGeoCnt: 4158299,
      newGeoCnt: 2587270,
      badQ: 373712,
      findOldInNew: 3748220,
      decreaseQ: 253045,
      latNotSame: 14876,
      oldNoLat: 36366,
      noOldId: 1
      }


3. dump geo_cacheNew and restore to production
   1. if restore index failed, create index manually       
      db.getCollection('geo_cacheNew').createIndex({aUaddr:1},{background:true})
      db.getCollection('geo_cacheNew').createIndex({_mt:-1},{background:true})
      db.getCollection('geo_cacheNew').createIndex({src:1},{background:true})
      db.getCollection('geo_cacheNew').createIndex({archive:1},{background:true})
      db.geo_cache.renameCollection('geo_cache_backup',true)
      db.geo_cacheNew.renameCollection('geo_cache',false)
      

on Production:

1. stop all import，
   1. comment out job_hourly, job_4hourly in crontab
   2. confirm no manual import in process
   3. check and kill if there's running import process, crea_import.coffee, treb_import.coffee

2. edit config, add db config. bing key is saved in db, do not set in config.
  1. vi tpls/config.coffee

  app_config:
   here:'gI6hBaOR4eBPRKgiNE4qIUUDY6UZ-ZlAlZxZt2o5LZE'
   mapquest: '********************************'
   
  2. coffee ./setup.coffee 

3. pull code and restart, will run migrate and rename geo_cacheNew to geo_cache

4. add job_hourly, job_4hourly  back in crontab.


# Check this after onlined(@admin)
Online Date:[YYYYMMDD hh:mm]
- [X] update cfg for ca8
- [X] Update db(gen & test & restore)
- [X] Updated corntab
- [X] drop edmMail
- [X] drop user_edm
- [X] geo_cache -> geo_cache_backup;  geo_cacheNew -> geo_cache

=============Below is for test purpose only===========

test import
1. treb
   1. db.getCollection('mls_treb_records').updateMany({mt:{$gt:new Date('2021-07-4 01:00:00'),$lt:new Date('2021-07-4 2:00:00')}},{$unset:{_i:1}}) 
   2. ./start.sh import/treb_import.coffee 
   
2. ddf
   1. db.getCollection('mls_crea_ddf_records').updateMany({_mt:{$gt:new Date('2021-07-4 1:00:00'),$lt:new Date('2021-07-4 2:00:00')}},{$unset:{_i:1}}) 189
   2.  ./start.sh import/crea_import.coffee
   
3. trebMan，bcreMan
   1. 查看4号服测试log