0 author create PR to [release], other person review
1 merge PR(one or multi depends on changes) to-> [release], and release freezes
2 unit & function test [release],
3 PR [release] -> [production]


##Possible issues when online
1. cfg/batch file not uploaded,
2. batch rename db, db is creating index/in use, cannot rename: 
3. prod cfg diff from build/dev env cfg, not isDevMode, debug.info
4. no error handling/no sufficient info
5. some code updates forgot to push
6. update PR after tested.
7. [release/production] hot fix/update conflict/bug with PR, such as prov/ptype(Residential->r) in toplisting, after merge to [import] this causes bug;

