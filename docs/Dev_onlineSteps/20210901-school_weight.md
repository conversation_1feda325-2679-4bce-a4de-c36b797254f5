BranchName: [school_weight]
Author: [<PERSON>]
BatchName: [batch/school_weight/*.coffee]
BatchDetail: See inside batch files
DB to Restore: [N/A]
Sales Doc: [N/A]

# steps:

- pull all school manually
  ./start.sh lib/batchBase.coffee batch/findschoolv3.coffee -a 
- Update find school corn job
  ./start.sh lib/batchBase.coffee batch/findschoolv3.coffee
- Run school weight batches
  - public school
    ./start.sh lib/batchBase.coffee batch/school_weight/school_weight.coffee
  - private school
    ./start.sh lib/batchBase.coffee batch/school_weight/private_weight.coffee
  - university
    ./start.sh lib/batchBase.coffee batch/school_weight/university_weight.coffee

# Check this after onlined(@admin)

Online Date:(2021-09-14 16:48)

- [X] findschoolv3.coffee -a 
- [X] Update find school corn job, remove -u
- [X] (batch/school_weight/school_weight.coffee)
- [X] (batch/school_weight/private_weight.coffee)
- [X] (batch/school_weight/university_weight.coffee)
