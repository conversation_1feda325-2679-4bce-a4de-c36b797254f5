BranchName: [processStatus]
Author: [<PERSON><PERSON><PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch:
Detail: 
  add process status api
## step
restart watch in each server
  ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee routine > ./logs/watchMongo.log 2>&1 &
  ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee routine > ./logs/watchSql.log 2>&1 &

Online Date:[(2021-10-12 15:02)]
- [X] run batch