<mxfile host="65bd71144e">
    <diagram id="6_COHc7h4wcuTtqOeNor" name="第 1 页">
        <mxGraphModel dx="102" dy="456" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="end" style="shape=mxgraph.flowchart.terminator;strokeWidth=2;gradientColor=none;gradientDirection=north;fontStyle=0;html=1;" vertex="1" parent="1">
                    <mxGeometry x="330" y="880" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;exitX=0.5;exitY=1;exitPerimeter=0;entryX=0.5;entryY=0;entryPerimeter=0;fontStyle=1;strokeColor=#003366;strokeWidth=1;html=1;" edge="1" parent="1" target="2">
                    <mxGeometry x="70" y="-467" width="100" height="100" as="geometry">
                        <mxPoint x="390" y="813" as="sourcePoint"/>
                        <mxPoint x="170" y="-997" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="&amp;nbsp; 1. 只做个人信用评级&lt;div&gt;&amp;nbsp; 2. 获取结果是xml文件，转成js object, 做html页面，支持下载ses发邮件附件&lt;/div&gt;&lt;div&gt;&amp;nbsp; 3. 结果写入单独mongo数据库， xml+结果&amp;nbsp; &amp;nbsp;model 写数据库&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;设计页面：用户输入页面+ 结果页面 + 错误信息页面&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="1">
                    <mxGeometry x="651.25" y="200" width="428.75" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="13">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="&lt;span style=&quot;text-wrap: nowrap;&quot;&gt;create InputSegments&amp;nbsp;&lt;/span&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;xml&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="282.5" y="340" width="215" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8" target="16">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390" y="118" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;OAuth 2.0&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;post&amp;nbsp;Equifax_UAT_OAuth_Endpoint_Canada_STS&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;get access token&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="245" y="116" width="290" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="10" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="start" style="shape=mxgraph.flowchart.terminator;strokeWidth=2;gradientColor=none;gradientDirection=north;fontStyle=0;html=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="26" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="16" target="6">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390" y="208" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="13" target="15">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="&lt;span style=&quot;text-wrap: nowrap;&quot;&gt;post&amp;nbsp;&lt;/span&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;EQUIFAX_UAT_STS_XML_CONSUMER&amp;nbsp;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;using token and InputSegments&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="248.75" y="446" width="282.5" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="15" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="get return xml" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="330" y="536" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;user input basic information:&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;LastName, FirstName,&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;SocialInsuranceNumber(optional&lt;/span&gt;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; background-color: initial;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%26lt%3Bdiv%20data-docx-has-block-data%3D%26quot%3Bfalse%26quot%3B%20data-page-id%3D%26quot%3BNkqjddDqToLDPdxaAvWu2FTFsHf%26quot%3B%26gt%3B%26lt%3Bdiv%20class%3D%26quot%3Bold-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg%26quot%3B%26gt%3Buser%20input%20basic%20information%3A%26lt%3B%2Fdiv%26gt%3B%26lt%3Bdiv%20class%3D%26quot%3Bold-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg%26quot%3B%26gt%3BLastName%2C%20FirstName%2C%26amp%3Bnbsp%3B%26lt%3Bspan%20style%3D%26quot%3Bbackground-color%3A%20initial%3B%26quot%3B%26gt%3BSocialInsuranceNumber%2C%26amp%3Bnbsp%3B%26lt%3B%2Fspan%26gt%3B%26lt%3B%2Fdiv%26gt%3B%26lt%3Bdiv%20class%3D%26quot%3Bold-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg%26quot%3B%26gt%3B%26lt%3Bspan%20style%3D%26quot%3Bbackground-color%3A%20initial%3B%26quot%3B%26gt%3BDateOfBirth%2C%20Occupation%2C%26amp%3Bnbsp%3B%26lt%3B%2Fspan%26gt%3B%26lt%3B%2Fdiv%26gt%3B%26lt%3Bdiv%20class%3D%26quot%3Bold-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg%26quot%3B%26gt%3B%26lt%3Bspan%20style%3D%26quot%3Bbackground-color%3A%20initial%3B%26quot%3B%26gt%3BAddress%3A%20CivicNumber%2C%20StreetName%2CCity%2C%20Province%2C%20PostalCode%26lt%3B%2Fspan%26gt%3B%26lt%3B%2Fdiv%26gt%3B%26lt%3B%2Fdiv%26gt%3B%26lt%3Bspan%20class%3D%26quot%3Blark-record-clipboard%26quot%3B%20data-lark-record-format%3D%26quot%3Bdocx%2Ftext%26quot%3B%20data-lark-record-data%3D%26quot%3B%7B%26amp%3Bquot%3BrootId%26amp%3Bquot%3B%3A%26amp%3Bquot%3BNkqjddDqToLDPdxaAvWu2FTFsHf%26amp%3Bquot%3B%2C%26amp%3Bquot%3Btext%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3BinitialAttributedTexts%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3Btext%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3B0%26amp%3Bquot%3B%3A%26amp%3Bquot%3BOAuth%202.0%E8%8E%B7%E5%BE%97access%20token%26amp%3Bquot%3B%7D%2C%26amp%3Bquot%3Battribs%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3B0%26amp%3Bquot%3B%3A%26amp%3Bquot%3B*0%2Bn%26amp%3Bquot%3B%7D%7D%2C%26amp%3Bquot%3Bapool%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3BnumToAttrib%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3B0%26amp%3Bquot%3B%3A%5B%26amp%3Bquot%3Bauthor%26amp%3Bquot%3B%2C%26amp%3Bquot%3B7378863704344903686%26amp%3Bquot%3B%5D%7D%2C%26amp%3Bquot%3BnextNum%26amp%3Bquot%3B%3A1%7D%7D%2C%26amp%3Bquot%3Btype%26amp%3Bquot%3B%3A%26amp%3Bquot%3Btext%26amp%3Bquot%3B%2C%26amp%3Bquot%3BreferenceRecordMap%26amp%3Bquot%3B%3A%7B%7D%2C%26amp%3Bquot%3Bextra%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3Bchannel%26amp%3Bquot%3B%3A%26amp%3Bquot%3Bsaas%26amp%3Bquot%3B%2C%26amp%3Bquot%3BpasteRandomId%26amp%3Bquot%3B%3A%26amp%3Bquot%3B85ffea8b-ab97-4676-ab77-82fa368c2811%26amp%3Bquot%3B%2C%26amp%3Bquot%3Bmention_page_title%26amp%3Bquot%3B%3A%7B%7D%2C%26amp%3Bquot%3Bexternal_mention_url%26amp%3Bquot%3B%3A%7B%7D%7D%2C%26amp%3Bquot%3BisKeepQuoteContainer%26amp%3Bquot%3B%3Afalse%2C%26amp%3Bquot%3BisFromCode%26amp%3Bquot%3B%3Afalse%2C%26amp%3Bquot%3Bselection%26amp%3Bquot%3B%3A%5B%7B%26amp%3Bquot%3Bid%26amp%3Bquot%3B%3A47%2C%26amp%3Bquot%3Btype%26amp%3Bquot%3B%3A%26amp%3Bquot%3Btext%26amp%3Bquot%3B%2C%26amp%3Bquot%3Bselection%26amp%3Bquot%3B%3A%7B%26amp%3Bquot%3Bstart%26amp%3Bquot%3B%3A16%2C%26amp%3Bquot%3Bend%26amp%3Bquot%3B%3A39%7D%2C%26amp%3Bquot%3BrecordId%26amp%3Bquot%3B%3A%26amp%3Bquot%3BONnydC06Ko55Yxx8Cclu9Dbustg%26amp%3Bquot%3B%7D%5D%2C%26amp%3Bquot%3BpayloadMap%26amp%3Bquot%3B%3A%7B%7D%2C%26amp%3Bquot%3BisCut%26amp%3Bquot%3B%3Afalse%7D%26quot%3B%26gt%3B%26lt%3B%2Fspan%26gt%3B%22%20style%3D%22shape%3Dmxgraph.flowchart.data%3BstrokeWidth%3D2%3BgradientColor%3Dnone%3BgradientDirection%3Dnorth%3BfontStyle%3D0%3Bhtml%3D1%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22107.56%22%20y%3D%2280%22%20width%3D%22424.87%22%20height%3D%2290%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3Eo&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;),&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;DateOfBirth, Occupation(optional),&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot; class=&quot;old-record-id-ONnydC06Ko55Yxx8Cclu9Dbustg&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Address: CivicNumber, Unit(optional), StreetName,City, Province, PostalCode&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="177.5" y="226" width="425" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="&lt;span style=&quot;text-wrap: nowrap;&quot;&gt;give error info&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="531.25" y="636" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="19" target="2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="generate html report" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="330" y="753" width="120" height="47" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="No" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="Yes" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="17">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="&lt;span style=&quot;text-wrap: nowrap;&quot;&gt;error record?&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="350" y="626" width="80" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>