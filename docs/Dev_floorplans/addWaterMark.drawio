<mxfile host="65bd71144e">
    <diagram id="RZoPIFeP_fAAwV-8I9Bt" name="第 1 页">
        <mxGraphModel dx="1873" dy="549" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="152" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="138" target="139" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="138" value="condos.floorplan&lt;br&gt;fp" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="260" y="20" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="139" value="floorplans存在fpId记录fpA?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="255" y="110" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="140" value="floorplans存在imgHash记录fpB?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="512" y="110" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="141" value="fpB与fp的name一致?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="512" y="220" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="173" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="142" target="143" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="142" value="backup/watermark" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="962" y="110" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="143" value="floorplans存在fp.name记录fpC?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="977" y="220" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="144" value="添加img,unit信息到fpC" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="962" y="430" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="145" value="新建记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1163" y="430" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="175" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="146" target="174" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="146" value="相同户型图不同name,&lt;br&gt;warning log" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="742" y="220" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="147" value="添加unit信息到fpB记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="317" y="430" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="153" value="fpB与fp的unt一致?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="512" y="310" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="154" value="重复记录，warning log" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="497" y="430" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="155" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="139" target="140" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="320" y="135" as="sourcePoint"/>
                        <mxPoint x="852" y="210" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="156" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="155" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="157" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="140" target="142" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="682" y="150" as="sourcePoint"/>
                        <mxPoint x="727" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="158" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="157" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="159" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="141" target="146" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="642" y="244.5" as="sourcePoint"/>
                        <mxPoint x="712" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="160" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="159" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="161" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="140" target="141" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="642" y="170" as="sourcePoint"/>
                        <mxPoint x="687" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="162" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="161" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="163" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="141" target="153" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="692" y="300" as="sourcePoint"/>
                        <mxPoint x="692" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="164" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="163" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="165" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="153" target="147" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="432" y="340" as="sourcePoint"/>
                        <mxPoint x="432" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="397" y="335"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="166" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="165" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="43" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="167" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="153" target="154" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="712" y="360" as="sourcePoint"/>
                        <mxPoint x="812" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="168" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="167" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="169" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="143" target="144" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="992" y="320" as="sourcePoint"/>
                        <mxPoint x="992" y="380" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="170" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="169" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint y="-30" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="171" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="143" target="145" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1132" y="280" as="sourcePoint"/>
                        <mxPoint x="1232" y="280" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1243" y="245"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="172" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="171" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-73" y="-25" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="262" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="174" target="261" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="174" value="backup/watermark" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="742" y="330" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="177" value="fpA与fp的name,img,unit一致?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-222" y="110" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="180" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="139" target="177" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="73" y="135" as="sourcePoint"/>
                        <mxPoint x="135" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="181" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="180" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="188" value="数据没有变化,更新fpA.touchedMt" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-412" y="110" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="192" value="condos unt变化,warning log&lt;br&gt;更新fpA的unit信息" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-670" y="320" width="155" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="196" value="floorplans存在fp.imgHash记录fpD?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-425" y="520" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="268" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;rounded=0;" parent="1" source="197" target="198" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-580" y="755"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="197" value="相同户型图不同name&lt;br&gt;warning log" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-640" y="520" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="205" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="198" target="199" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="198" value="backup/watermark" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-420" y="730" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="199" value="更新fpA的img,unit信息" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-420" y="835" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="209" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="177" target="188" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-232" y="170" as="sourcePoint"/>
                        <mxPoint x="-287" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="210" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="209" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="211" value="fpA记录verified?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-222" y="220" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="212" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="177" target="211" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-62" y="200" as="sourcePoint"/>
                        <mxPoint x="-62" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="213" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="212" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="214" value="已经被verified warning log&lt;br&gt;更新fpA.touchedMt" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-412" y="220" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="215" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="211" target="214" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-252" y="220" as="sourcePoint"/>
                        <mxPoint x="-322" y="220" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="216" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="215" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="217" value="fp与fpA的name一致?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-222" y="320" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="218" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="217" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-157" y="270" as="sourcePoint"/>
                        <mxPoint x="-82" y="370" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="219" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="218" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="220" value="fp与fpA的imgHash一致?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-425" y="320" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="221" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="220" target="192" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-475" y="320" as="sourcePoint"/>
                        <mxPoint x="-545" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="222" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="221" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="223" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="217" target="220" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-252" y="390" as="sourcePoint"/>
                        <mxPoint x="-322" y="390" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="224" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="223" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="228" value="floorplans存在fp.nm记录fpE?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-222" y="520" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="229" value="fp与fpE的imgHash一致?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="80" y="520" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="234" value="添加unit信息到fpE" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="228" y="755" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="239" value="floorplans存在fp.imgHash记录fpF?" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-222" y="610" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="282" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="242" target="245" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-30" y="755"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="242" value="相同户型图不同name&lt;br&gt;warning log" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-90" y="661" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="249" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="245" target="248" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="245" value="backup/watermark" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-217" y="730" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="248" value="新建记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-217" y="835" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="258" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="254" target="255" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="254" value="backup/watermark" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="85" y="650" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="255" value="添加img,unt信息到fpE" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="85" y="755" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="260" value="使用到的fields 结构&lt;br&gt;&lt;br&gt;field&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;type&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;description&lt;br&gt;nm&lt;span style=&quot;font-size: 15px;&quot;&gt;&#9;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;/span&gt;string&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;''&lt;br style=&quot;font-size: 15px;&quot;&gt;imgs&lt;span style=&quot;font-size: 15px;&quot;&gt;&#9;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;/span&gt;array object&lt;span style=&quot;white-space: pre; font-size: 15px;&quot;&gt;&#9;&lt;/span&gt;[ { img:'', origImg:[], imgHash:'' }]&lt;br style=&quot;font-size: 15px;&quot;&gt;unts&lt;span style=&quot;white-space: pre; font-size: 15px;&quot;&gt;&#9;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;/span&gt;array object&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;[ { unt:[], lvl:[], fce:[], fpId:'', origImg:'' } ]&lt;span style=&quot;white-space: pre; font-size: 15px;&quot;&gt;&#9;&lt;/span&gt;&lt;br&gt;verified&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;object&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;{ status:0, uid:'', ts:'' }" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;size=0;align=left;verticalAlign=top;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="560" y="730" width="470" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="261" value="新建记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="742" y="430" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="263" value="在floorplans下查记录,都是基于uaddr一致的条件下,查询imgHash,name记录" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-660" y="40" width="170" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="264" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="196" target="197" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-425" y="560" as="sourcePoint"/>
                        <mxPoint x="-515" y="560" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="265" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="264" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="266" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="196" target="198" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-340" y="600" as="sourcePoint"/>
                        <mxPoint x="-340" y="660" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="267" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="266" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="270" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="229" target="254" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="213" y="590" as="sourcePoint"/>
                        <mxPoint x="213" y="650" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="271" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="270" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="272" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="229" target="234" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="283" y="560" as="sourcePoint"/>
                        <mxPoint x="283" y="620" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="308" y="545"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="273" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="272" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-15" y="-56" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="274" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="228" target="239" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-80" y="570" as="sourcePoint"/>
                        <mxPoint x="-80" y="630" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="275" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="274" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="276" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="228" target="229" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-30" y="480" as="sourcePoint"/>
                        <mxPoint x="-125" y="480" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="277" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="276" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="278" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="239" target="245" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-200" y="680" as="sourcePoint"/>
                        <mxPoint x="-200" y="720" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="279" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="278" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="280" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="239" target="242" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-82" y="555" as="sourcePoint"/>
                        <mxPoint x="50" y="555" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-30" y="635"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="281" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="280" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-12" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="286" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="283" target="228" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="283" value="condos数据变动,warning log&lt;br&gt;fpA移除fpId相关信息" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-235.5" y="430" width="157" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="284" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="217" target="283" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-70" y="380" as="sourcePoint"/>
                        <mxPoint x="-70" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="285" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="284" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="287" value="W1:condos存在相同户型图不同名字的记录,&lt;br&gt;rec:{condosId:'',nm:'',unt:'',lvl:'',fce:''}&lt;br&gt;oth:{condosId:'',nm:'',unt:'',lvl:'',fce:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="740" y="170" width="240" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="289" value="floorplans存在可以merge的记录,merge到该记录" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=west;align=right;verticalAlign=top;size=0;fillColor=#fad7ac;strokeColor=#b46504;fontColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1050" y="270" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="291" value="condos下记录unit信息发生改变" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=west;align=left;verticalAlign=top;size=0;fillColor=#fad7ac;strokeColor=#b46504;fontColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="-515" y="285" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="292" value="W2:condos存在重复的记录,&lt;br&gt;rec:{condosId:'',nm:'',unt:'',lvl:'',fce:''}&lt;br&gt;oth:{condosId:'',nm:'',unt:'',lvl:'',fce:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="497" y="490" width="193" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="294" value="W3:已经验证过的记录source数据发生变化,&lt;br&gt;rec:{condosId:'',nm:'',unt:'',lvl:'',fce:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-510" y="190" width="236.5" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="296" value="W4:source unit信息发生变化,&lt;br&gt;rec:{condosId:'',nm:'',unt:'',lvl:'',fce:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-760" y="285" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="297" value="condos下户型图发生改变,并且存在相同户型图不同名称的记录" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=west;align=left;verticalAlign=top;size=0;fillColor=#fad7ac;strokeColor=#b46504;fontColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="-520" y="480" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="298" value="W6:source 户型图发生变化,存在相同户型图不同名称的记录,now:{condosId:'',nm:'',unt:'',lvl:''},&lt;br&gt;oth:{condosId:'',nm:'',unt:'',lvl:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-810" y="485" width="270" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="299" value="condos下记录name发生改变" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=west;align=left;verticalAlign=top;size=0;fillColor=#fad7ac;strokeColor=#b46504;fontColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="-140" y="395" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="300" value="W7:source name发生改变,rec:{condosId:'',nm:'',unt:'',lvl:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-78.5" y="440" width="190" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="301" value="存在相同name的记录" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=west;align=left;verticalAlign=top;size=0;fillColor=#fad7ac;strokeColor=#b46504;fontColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="-70" y="510" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="302" value="户型图与其它记录的户型图一致" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=west;align=left;verticalAlign=top;size=0;fillColor=#fad7ac;strokeColor=#b46504;fontColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="-110" y="600" width="170" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="305" value="W8:source name发生改变,存在相同户型图不同name的记录,rec:{condosId:'',nm:'',unt:'',lvl:''}&lt;br&gt;oth:{condosId:'',nm:'',unt:'',lvl:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-30" y="842.5" width="250" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="306" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;fontColor=#000000;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="1" source="305" target="242" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="-30" y="830" as="sourcePoint"/>
                        <mxPoint x="20" y="780" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="310" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="307" target="196">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="307" value="condos 户型图改变&lt;br&gt;warning log" style="whiteSpace=wrap;html=1;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="-438.5" y="429" width="157" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="308" value="" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="220" target="307">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-300" y="380" as="sourcePoint"/>
                        <mxPoint x="-300" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="309" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="308">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="311" value="W5:source 户型图发生变化,&lt;br&gt;now:{condosId:'',nm:'',unt:'',lvl:''},&lt;br&gt;oth:{condosId:'',nm:'',unt:'',lvl:''}" style="text;strokeColor=none;align=left;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="-610" y="400" width="180" height="40" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>