<mxfile host="65bd71144e">
    <diagram id="WQYbdSiGuy-YJf5-uBSD" name="Page-1">
        <mxGraphModel dx="882" dy="439" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="_TBPnletHmMv3rk39tA5-19" value="General services:&lt;br&gt;When outside service is accessible,&lt;br&gt;Relay queue to outside queue.&lt;br&gt;When too long time unavailable,&lt;br&gt;send message to email/sms.&lt;br&gt;all parameters from command line or&amp;nbsp;&lt;br&gt;a config file, or environments." style="rounded=0;whiteSpace=wrap;html=1;dashed=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="240" y="400" width="520" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="_TBPnletHmMv3rk39tA5-1" target="_TBPnletHmMv3rk39tA5-9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="_TBPnletHmMv3rk39tA5-1" target="2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-1" value="Request Queue" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/azure2/general/Storage_Queue.svg;" parent="1" vertex="1">
                    <mxGeometry x="430" y="696" width="64" height="52" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-2" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="40" y="380" as="sourcePoint"/>
                        <mxPoint x="810" y="380" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-3" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="40" y="320" as="sourcePoint"/>
                        <mxPoint x="810" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="_TBPnletHmMv3rk39tA5-4" target="_TBPnletHmMv3rk39tA5-20" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-4" value="Response Queue" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/azure2/general/Storage_Queue.svg;" parent="1" vertex="1">
                    <mxGeometry x="650" y="700" width="64" height="52" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-5" value="Request Queue" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/azure2/general/Storage_Queue.svg;" parent="1" vertex="1">
                    <mxGeometry x="510" y="230" width="64" height="52" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="_TBPnletHmMv3rk39tA5-6" target="_TBPnletHmMv3rk39tA5-12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-6" value="Response Queue" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/azure2/general/Storage_Queue.svg;" parent="1" vertex="1">
                    <mxGeometry x="650" y="230" width="64" height="52" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="_TBPnletHmMv3rk39tA5-7" target="_TBPnletHmMv3rk39tA5-1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-7" value="Request Process" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=process;whiteSpace=wrap;rounded=1;size=0.14;arcSize=6;" parent="1" vertex="1">
                    <mxGeometry x="180" y="696" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.469;entryY=1.019;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="_TBPnletHmMv3rk39tA5-9" target="_TBPnletHmMv3rk39tA5-5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-9" value="Request Mirror" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=process;whiteSpace=wrap;rounded=1;size=0.14;arcSize=6;" parent="1" vertex="1">
                    <mxGeometry x="492" y="420" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="_TBPnletHmMv3rk39tA5-12" target="_TBPnletHmMv3rk39tA5-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-12" value="Response Mirror" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=process;whiteSpace=wrap;rounded=1;size=0.14;arcSize=6;" parent="1" vertex="1">
                    <mxGeometry x="632" y="420" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-15" value="Outside Process" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=process;whiteSpace=wrap;rounded=1;size=0.14;arcSize=6;" parent="1" vertex="1">
                    <mxGeometry x="550" y="50" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-16" value="Servers" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="1" vertex="1">
                    <mxGeometry x="90" y="400" width="60" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-17" value="Dynamic Outside Servers" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" parent="1" vertex="1">
                    <mxGeometry x="90" y="256" width="140" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-18" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="210" y="790" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="_TBPnletHmMv3rk39tA5-20" target="_TBPnletHmMv3rk39tA5-18" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="_TBPnletHmMv3rk39tA5-20" value="Response processing service" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=process;whiteSpace=wrap;rounded=1;size=0.14;arcSize=6;" parent="1" vertex="1">
                    <mxGeometry x="632" y="800" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="2" target="_TBPnletHmMv3rk39tA5-4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="Process that can run in local box &amp;amp; expiry process" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=process;whiteSpace=wrap;rounded=1;size=0.14;arcSize=6;" vertex="1" parent="1">
                    <mxGeometry x="520" y="620" width="100" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>