
BranchName: 
Author: 
BatchName: 
BatchDetail: 

Major files changed 

[] review all changed code, follow reviewSteps.md
[] run all unit test ./test.sh
[] check new unitTest of related change
    
[] run batch files local
  [] 
[] run batch on Test Server
[NA] test web or app local
[NA] test Web Or App on Test Server
[] go through online step on test server.
[] test after online

reviewer: <PERSON>
[x] review all changed code, follow reviewSteps.md
[x] run all unit test ./test.sh
[x] check new unitTest of related change
    school_weight_helper.coffee has unit test, coverage ok
[x] ran batch files 
    findschoolv3.coffee local, check result
    ran school_weight.coffee local, check result
[] if web or app, test with browser.
[] ran batch on test server
[] go through online step on test server.
[] test after online

  
