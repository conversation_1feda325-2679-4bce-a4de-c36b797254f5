## Reference https://www.atlassian.com/git/tutorials/git-lfs

## Instruction: https://support.atlassian.com/bitbucket-cloud/docs/use-git-lfs-with-bitbucket/

1. install LFS:
  brew install git-lfs
  git lfs install
2. Set up Git LFS file tracking locally
  run this command in root folder
    git lfs track '<pattern>' #/src/webroot/img/imgstocks/cmty/

  The patterns are added to the .gitattributes file for the repo.
3. git add/commit/push/pull same as normal
   `git lfs ls-files` to lsp