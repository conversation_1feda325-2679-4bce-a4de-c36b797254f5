Q: When I send a lead using event api. I get a successful response with id = people's id. How do I know if the event is processed successfully?

A:

I can see your successful POST request to /events for <PERSON> in our API logs, so congrats on successfully submitting an event!

The information returned from a POST to /events is the person/contact/lead as you've seen. For an event to be attributed to the correct person, the person object must contain some identifying information like their phone number, email, or if the person_id is known, the specific ID.

Our example docs that simply show an empty person object except for "contacted: false" is confusing as it'll create an new contact with no name - we need to improve those docs on our end, my apologies for any confusion there.

As for knowing whether an event is processed successfully or not, if you receive a 2xx response from the API, then the event successfully processed. Whether or not it was correctly attributed to the right person is determined by that person object and the identifying information sent over. If we found an existing contact with that info, we'll return them after processing the event. If you want to be sure the event you sent was properly processed, you can do a follow-up call to [GET] /events with the query parameter personId={the person's ID} and you'll get a list of events for that person and can check the correct timestamps, source, type, etc.




Q: how to get Fub-Signature when register webhook 
A:
When you register a webhook with us, you do so with your system name and system's secret key. This key is used to calculate the signature on your end and verify it against the one we provided you so you know it came from Follow Up Boss (see "Verifying the request").​ The "Fub-Signature" header is only sent from our system to the URL you requested to receive webhooks at (ex: "https://mysite.com/fub/webhooks").

If you're still stuck, I'd recommend checking out the raw HTTP request coming into your system to make sure nothing funky is going on with your server's handling of headers. From there, you can send us the raw requests you received and the URL you registered to receive webhooks and we can go from there.



Q: "We want to know if we already have the contact id with the user, can we add it in the post to avoid creating a new user?"

A: Yes you can. Just add an "id" parameter to the "person" object within the event body when you POST to the /events endpoint. It'll directly update that person and attribute the event to them.
 

Q: "which agent will the API send to? In my test, it returns the one which is created later"

A: If the agent lead duplication power-up is on and the user (identified via their API key) is an Agent (regular user, not an admin), the duplicate will be assigned to that user and create a new contact. Otherwise, we'll deduplicate, and I believe if an Admin's API key is used, they will be assigned.
 

Q:"I still want to confirm if we turn on turning off Agent-owned lead duplication, will the Api create duplicate contact?(Currently I tested with turning off ,Api will not create new Contact.)"

A: With the power-up turned off, no duplicate will be created if we can find an existing contact (via email, phone, etc).
 


Q:"if there is any delay designed for the webhook?"

A: 
That shouldn't happen. Our webhook events are queued for background workers to pick up and another one of our services will send the webhook out, so you should always get the initial API response before the webhook comes through. If you're getting the webhook first, that could be a weird edge case and not the norm. If this is causing a problem, you could update your code that handles a webhook to delay the handling (ie: queue it for a minute or so).


Q: When we send an event from the API, we will process the callback. Is there any config to let the API request not trigger a webhook?
A:
There isn't any way to mute a webhook when creating an event. If you have a webhook subscribed to event creation, when you create an event, we'll always send the webhook. You would have to store the ID of the person created via the event and then when you receive the webhook, ignore with some logic based on the situation.
 


Q: currently our power-up is turned off. but the agent can still create duplicate users

 
A: So, from the screenshot, you're creating duplicates via the UI and not the API? Is the user you're logged in as an admin?
 
--
 
I just looked through the code once more and found that the /v1/people endpoint allows duplicates to be created. This endpoint doesn't trigger lead flows so you can directly assign an agent. This is done because if an integration is pushing to /people, we assume you mean to specifically create that person vs letting FUB determine the flow and handling (as is the case when creating via /v1/events endpoint). The FUB UI uses /v1/people which is why you're able to create duplicates via the UI (albeit, with a warning first).
 
--
Q: FollowUpboss already has 2 users for C, which will the new one append too or it will create another one?

A:
If there are multiple contacts with the same email address, FUB will choose the most recently updated one to attribute the inquiry to.
 