Follow up boss 
# docs:
https://docs.followupboss.com/reference/getting-started
https://www.followupboss.com/


# Set up procedure:

change config
app_config:
  followUpBoss:
    token:'fka_02gWVHLlA1VvNq1AR2X82ZuhXeFjtWLmjB'
    xSystem: 'realmasterWeb'
    xSystemKey: 'bade25031e36fc00f93db96e9fbc404c'

## register webhook event api by post to /webhooks manully

go to 
https://docs.followupboss.com/reference/webhooks-1
params:
event: peopleUpdated
url: https://realmaster.com/1.5/fub/peopleUpdated
xSystemKey: 'bade25031e36fc00f93db96e9fbc404c'



## sync agent account
IN APP:
admin goto more -> manage people fub info
sync from fub
add _crmNoAssign for these account:

<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>



# Feature description:
1. Send inquiry from app to crm, create a lead in crm. Save lead history in db.
  如果已经有关联的crm经纪，设置assignToUserId 可以直接发给指定经纪，
  新用户直接发到page.src 的组里，由fub管理员分配经纪。分配后调用webhook更新app端的关联关系。
  response里会生成用户的fubid，在app端更新。

  api url: https://api.followupboss.com
  source file: libapp/followUpBoss.coffee

2. manage relationship between app user and crm user. 
  crm: 
  realtor: 对应followupboss里面的agent
    fubAgentId: agent Id in fub
    fubLeadEml : <EMAIL>
    fubEml: <EMAIL>
    fubMbl

  users: 对应followupboss里面的people
    flwngRm:{uid,ts}
    fubId: userId In fub
    发送form到crm时创建用户，更新fubId,
    当user在crm里assign给某个经纪时，需要用fubId去对应处理webhook的结果。发送新的form时，带着fubId会避免创建新用户，可以解决用户修改eml的问题。


3. followUpBoss webhook  usage:
  1. register https://apps.followupboss.com/system-registration, get a X-System and X-System-Key
  2. register webhook event api by post to /webhooks (with callback url in body)
  3. create webHook post api for each event needed. 
    when receiving people update event
    1. check post event, verify FUB-Signature with X-System-Key
    2. save the event in fubEvent collection {uri}
    3. process event, get uri for the event detail
    4. get people info by url. find user by fubid, find assigned agent by assigentAgentId,set flwngRm to user.
  4. if lead in other group assign to an agent automatically. it will send webhook as well. this assigentAgentId should not exists in our app user。call back api wont find a agent to assign

4. follow up boss group usage
  在follow up里设置用户组。将lead flow指定到某个用户组，
  All leads from this source should come into this lead Flow and assigned to the group.
  IMPORTANT NOTE:
    In API, use source, leave person.source as blank. put url under property.url
    if source is difference person.source, might create a new lead Flow with same source, and there might has bug when the lead goes to which flow.
    if a user eml|phone already exits in the contacts, lead from other source will still go to the exist user.





fillFormWithFollwedAgent
  if has flwngRm
    if has leadEml, send lead to leadEml
    if not, send email to flwngRm user, send lead to tp+Other group.


new user fill Form
  1. GTA props, send to crm tp+gta group, do not send form email to agent. set fubId to user
    when fub crm group received the lead,assign to an agent, send webhook callback to app api
    refer to followUpBoss webhook  usage

  2. Out of gta props, find agent in the city, choose one random, send form email to the agent, assign flwngRm to user
    also send lead to crm tp+other group. set fubId to user

只有api创建的。

管理员界面
1. set app and crm connection
2. manage user‘s following agent
3. manage realtor's following user
4. manage front Dest Admin