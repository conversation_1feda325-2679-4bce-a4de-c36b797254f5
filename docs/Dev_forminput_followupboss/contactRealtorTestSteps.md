
测试用例：

用户：
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
注意user不能follow staff

通过浏览器控制haswechat


Project: (一个，通过修改spronsor来看修改的行为)
```
set sponor = <EMAIL>
db.getCollection('pre_constrct').updateOne({_id:ObjectId('60f9a1794176ad64beb94deb')},{$set:{spuids:[ObjectId("6272e6dbff29777117bc3dc8")]}})

set sponor = <EMAIL>
db.getCollection('pre_constrct').updateOne({_id:ObjectId('60f9a1794176ad64beb94deb')},{$set:{spuids:[ObjectId("6272e6f0ff29777117bc3dc9")]}})

set sponor = <EMAIL>
db.getCollection('pre_constrct').updateOne({_id:ObjectId('60f9a1794176ad64beb94deb')},{$set:{spuids:[ObjectId("6272e72dff29777117bc3dcb")]}})

set nosponsor
db.getCollection('pre_constrct').updateOne({_id:ObjectId('60f9a1794176ad64beb94deb')},{$set:{spuids:[]}})
```

根据登陆用户的状态：
- [x] 有关联经纪，显示关联经纪
    - [ ] <EMAIL> 用户，显示************************
- [ ] 关联房大师经纪，<EMAIL>，
    - [x] 非微信，登陆用户
      显示*****************关联经纪+表格，提交表格发给，<EMAIL>，设置user_follow_*****************的fubid
      检查：db.getCollection('fub_result').find({}).sort({_id:-1}), 有新的event
      db.getCollection('user').find({eml:'<EMAIL>'})， 有fubId = 20 
      follow up boss 里有新的lead 给*****************
      db.getCollection('form_input').find({}).sort({_mt:-1})
        tousers: "fubAgentId" : 5,
    - [ ] 有微信
        - [x] project <NAME_EMAIL>
          [x] 显示sponsor(<EMAIL>)和表格。显示contact vvip agent
          发送email 给************************
          检查：db.getCollection('fub_result').find({}).sort({_id:-1}), 有新的event
          db.getCollection('user').find({eml:'<EMAIL>'})， fubId 不变 = 20
          follow up boss 里有新的message 给*****************, send mail <NAME_EMAIL>
          db.getCollection('form_input').find({}).sort({_mt:-1})
            tousers：[crm,  <EMAIL>]
        [x] project <NAME_EMAIL>
            - [x] 显示*****************和表格。发到crm，发给*********************
        - [x] project <NAME_EMAIL>
            - [x] 显示*****************和表格。发到crm，发给*********************,发邮件给*****************
        - [x] Project has no sponsor 
            - [x] 显示关联经纪*****************关联经纪+表格。发到crm，发给*********************
- [ ] 无关联经纪。<EMAIL>
    关联关系。
    - [ ] 非微信，显示表格，不显示挂盘经纪，但是还可能需要发给挂牌经纪
        sponsor= <EMAIL> 
        - [x] project sponsor ：外部经纪 <EMAIL> 
            - [x] 显示表格。发送email 给************************
        - [x] project sponsor is 是crm经纪*****************
         sponsor= <EMAIL> 
            - [x] 显示表格。发到crm，发给*********************
        - [x] Project has no sponsor 
            - [x] 显示表格。发到crm，自动assign 经纪，staff不assign，其他的建立follow关系。
    - [ ] 有微信，显示表格+挂盘经纪，<EMAIL>
        - [x] project sponsor is 外部经纪 <EMAIL> 
            - [x] 显示表格+挂盘经纪。发送email 给************************
        - [x] project sponsor is 是crm经纪*****************
            - [x] 显示表格+挂盘经纪。发到crm，发给*********************
        - [x] Project has no sponsor 
            - [x] 显示表格。发到crm，没有经纪
        - [ ] 
- [x] 所有经纪
    - [ ] sponsor is  <EMAIL>,
        - [x] 用户 ************************显示*****************,
        - [x] 用户***************** 显示*****************,
      [x] no sponsor, 不显示入口。前台判断


Rmlisting:
rmlisting 的owner是staff
rmlisting 的owner 是 vipRealtorNotInCrm
rmlisting 的owner 是 rmRealtor1


根据登陆用户的状态：
- [x] 有关联经纪，显示关联经纪
    - [x] <EMAIL> 用户，显示************************
- [x ] 关联房大师经纪，<EMAIL>，
    - [x] 非微信
      显示*****************关联经纪+表格，提交表格发给，<EMAIL>，设置user_follow_*****************的fubid
      检查：db.getCollection('fub_result').find({}).sort({_id:-1}), 有新的event
      db.getCollection('user').find({eml:'<EMAIL>'})， 有fubId = 20 
      follow up boss 里有新的lead 给*****************
      db.getCollection('form_input').find({}).sort({_mt:-1})
        tousers: "fubAgentId" : 5,
    - [x] 有微信
        [x]  <NAME_EMAIL>
          [x] 显示owner(<EMAIL>)和表格
          发送email 给************************, 发给crm
          检查：db.getCollection('fub_result').find({}).sort({_id:-1}), 有新的event
          db.getCollection('user').find({eml:'<EMAIL>'})， fubId 不变 = 20
          follow up boss 里有新的message 给*****************, send mail <NAME_EMAIL>
          db.getCollection('form_input').find({}).sort({_mt:-1})
            tousers：[crm,  <EMAIL>]
        [x] <NAME_EMAIL>
            - [x] 显示*****************和表格。发到crm，发给*********************
        - [x] <NAME_EMAIL>
            - [x] 显示*****************和表格。发到crm，发给*********************,发邮件给*****************
        [x] <NAME_EMAIL>
            send to crm followed agent，<EMAIL>， send <NAME_EMAIL>

- [x ] 无关联经纪。<EMAIL>
    关联关系。
    - [ ] 非微信，显示表格，不显示挂盘经纪，但是还可能需要发给挂牌经纪
        sponsor= <EMAIL> 
        - [x] owner ：外部经纪 <EMAIL> 
            - [x] 显示表格。发送email 给************************
        - [x] owner is 是crm经纪*****************, send to crm, with assigned id = <EMAIL>
            - [x] 显示表格。发到crm，发给*********************
        -[x] owner is staff, send to crm, with no assignedId.
    - [x] 有微信，显示表格+挂盘经纪
        - [x] owner is 外部经纪 <EMAIL> 
            - [x] 显示表格+挂盘经纪。发送email 给************************
        - [x] owner is 是crm经纪*****************
            - [x] 显示表格+挂盘经纪。发到crm，发给*********************    
        -[x] owner is staff, send to crm, with no assignedId.




Mls:
- [x] 有关联经纪，显示关联经纪
    - [x] <EMAIL> 用户，显示************************
- [x] 非vip经纪 <EMAIL>
    - [x] 显示升级表格
- [x] vip经纪（商家视同普通用户）  <EMAIL>
    - [x] 显示toplisting 和挂牌经纪
- [ ] 关联房大师经纪，<EMAIL>，
    - [] 有微信 或非微信
        [X]  <NAME_EMAIL>
            显示owner(<EMAIL>)和表格，发给crm，同时发email给tl
        [x] <NAME_EMAIL>
           显示*****************和表格。发到crm，发给*********************
        [X] <NAME_EMAIL>
            - [] 显示*****************和表格。发到crm，发给*********************,发邮件给*****************
        [X] no tl, show 关联经纪，发给crm,不区分本地城市外地城市

- [ ] 无关联经纪。<EMAIL>
    - [ ] 无微信，不显示top agent，直接进crm

    - [ ] 有微信，显示tA
        TL= <EMAIL> 
        - [x] owner ：外部经纪 <EMAIL> 
            - [] 显示表格。发送email 给************************
        - [x] owner is 是crm经纪*****************, send to crm, with assigned id = <EMAIL>
            - [] 显示表格。发到crm，发给*********************
        [x] 无tl， 外地城市打开黄页，本地城市发给crm
    
- [x] 所有经纪
    - [x] 显示挂盘经纪


外地城市房源
[ ] 显示黄页


估价
- [] 有关联经纪，显示关联经纪
    - [] <EMAIL> 用户，显示************************
- [x] 非vip经纪 <EMAIL>
    - [x] 不显示入口
- [x] vip经纪（商家视同普通用户）  <EMAIL>
    - [x] 不显示入口
-[] 外地城市
    打开黄页
- [ ] 关联房大师经纪，<EMAIL>，
    - [] 有微信 或非微信
        显示关联经纪，发给crm

- [ ] 无关联经纪。<EMAIL>
    显示表格，发给crm


###