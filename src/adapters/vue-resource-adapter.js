const path = require('path');

// vue-resource 适配器
// 将 RMSrv.fetch 封装为与 vue-resource 兼容的 API
// 注意：此适配器不支持以下 vue-resource 功能：
// - mapResponse 转换器链
// - 请求/响应拦截器的完整功能

const vueResourceAdapter = {
  // 创建基础http对象
  createResource(options = {}) {
    const resource = {};
    
    // 实现基本的HTTP方法
    ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'].forEach(method => {
      resource[method] = (url, data, config = {}) => {
        // 调整参数以适配RMSrv.fetch
        const requestOptions = {
          ...config,
          method: method.toUpperCase(),
          url
        };
        
        // 支持 timeout 配置
        if (config.timeout) {
          requestOptions.timeout = config.timeout;
        }
        
        // 对于GET请求，将数据作为查询参数
        if (method === 'get' && data) {
          const queryParams = new URLSearchParams();
          Object.keys(data).forEach(key => {
            queryParams.append(key, data[key]);
          });
          // url = url + '?' + queryParams.toString();
          const separator = url.includes('?') ? '&' : '?';
          url = url + separator + queryParams.toString();
          requestOptions.url = url;
        } else {
          // 对于其他请求，将数据放在body中
          requestOptions.body = data;
        }
        
        // 创建带超时的 Promise
        let fetchPromise = window.RMSrv.fetch(requestOptions);
        
        // 如果设置了 timeout，添加超时处理
        if (config.timeout) {
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error('Request timeout after ' + config.timeout + 'ms'));
            }, config.timeout);
          });
          
          fetchPromise = Promise.race([fetchPromise, timeoutPromise]);
        }
        
        // 调用RMSrv.fetch并返回Promise
        return fetchPromise
          .then(response => {
            // 模拟vue-resource响应格式
            const responseObj = {
              ok: true,
              status: 200,
              statusText: 'OK',
              body: response,
              json: () => Promise.resolve(response),
              text: () => Promise.resolve(JSON.stringify(response)),
              blob: () => Promise.reject(new Error('Blob not supported in adapter')),
              headers: new Map()
            };
            
            // 关键改动：为响应添加data属性作为body的别名
            Object.defineProperty(responseObj, 'data', {
              get: function() { return this.body; },
              set: function(val) { this.body = val; }
            });
            
            return responseObj;
          })
          .catch(error => {
            // 处理错误
            const errorResponse = {
              ok: false,
              status: error.status || (error.message?.includes('timeout') ? 408 : 500),
              statusText: error.message || 'Error',
              body: error,
              json: () => Promise.reject(error),
              text: () => Promise.resolve(error.toString()),
              headers: new Map()
            };
            
            // 同样为错误响应添加data属性
            Object.defineProperty(errorResponse, 'data', {
              get: function() { return this.body; },
              set: function(val) { this.body = val; }
            });
            
            return Promise.reject(errorResponse);
          });
      };
    });
    
    return resource;
  },
  
  // 添加 install 方法以支持 Vue.use()
  install(Vue) {
    const http = this.createResource();
    
    // 将http绑定到Vue原型上，使其在所有组件中可用
    Vue.prototype.$http = http;
    Vue.http = http;
  }
};

// 导出适配器
module.exports = vueResourceAdapter;

// 为了支持 Vue.use(VueResource)
vueResourceAdapter.mapResponse = function(callback) {
  console.warn('mapResponse is not fully implemented in the adapter');
  return function(response) { 
    return callback ? callback(response) : response; 
  };
};

// 支持其他vue-resource常用属性
vueResourceAdapter.options = {
  timeout: 0,
  credentials: 'same-origin'
};

// 基础拦截器支持
vueResourceAdapter.interceptors = {
  request: [],
  response: []
};
