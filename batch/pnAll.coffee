
helpers = require "../src/lib/helpers"
InterNode = require "../src/inter.node/internode"
db = require('./db')
exit = db.exit
puts = console.log
debug = console.log
argv = require('optimist').usage('Usage: $0 -l [en|zh-cn|zh] -m [string] -r 1(real send)').demand(['l','m']).default('r',0).argv;


server = InterNode.connect()
#server.connect 'localhost',9919
#server = InterNode.connect true
puts argv

sendPN = (notice)->
  server.get 'push-notify', (err,m)->
    if err
      console.error err
      return exit 4,err
    m notice,(err,result)->
      console.error err if err
      console.log result if result
      puts "Done"
      return exit 0


db.mongo (err, MongoDB)->
  if err then return exit 1,err
  db = 'chome'
  User = new MongoDB(db,'user')
  q = {pn:{$exists:true}}
  if argv.l is 'en'
    q.$or = [{locale:argv.l},{locale:$exists:false}]
  else
    q.locale = argv.l
  User.findToArray q,{projection:{pn:1}},(err,users)->
    if err
      console.error err
      return exit 3,err
    if not users
      console.error "No Users"
      return exit 2,"No Users"
    debug users
    pns = (u.pn for u in users)
    notice =
      from: 'RealMaster'
      to: pns
      msg: argv.m
    if argv.r is 1
      return sendPN notice
    puts "Message: #{argv.m}"
    for n in pns
      console.log "Send to #{n}"
    exit 0




